{"ast": null, "code": "import React,{useState}from'react';import{Link,useLocation}from'react-router-dom';import{useAuth}from'../../contexts/AuthContext';import{useBusinessSettings}from'../../contexts/BusinessSettingsContext';import{Monitor,LayoutDashboard,ShoppingCart,Package,BarChart3,Settings,LogOut,Menu,X,User}from'lucide-react';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const Layout=_ref=>{let{children}=_ref;const[sidebarOpen,setSidebarOpen]=useState(false);const{currentUser,logout,hasPermission}=useAuth();const{businessSettings}=useBusinessSettings();const location=useLocation();const navigation=[{name:'Dashboard',href:'/dashboard',icon:LayoutDashboard,roles:['admin','attendant','technician']},{name:'P<PERSON>',href:'/pos',icon:ShoppingCart,roles:['admin','attendant']},{name:'Services',href:'/services',icon:Monitor,roles:['admin']},{name:'Inventory',href:'/inventory',icon:Package,roles:['admin','attendant']},{name:'Reports',href:'/reports',icon:BarChart3,roles:['admin']},{name:'Settings',href:'/settings',icon:Settings,roles:['admin','technician']}];const filteredNavigation=navigation.filter(item=>hasPermission(item.roles));const handleLogout=async()=>{try{await logout();}catch(error){console.error('Logout error:',error);}};return/*#__PURE__*/_jsxs(\"div\",{className:\"h-screen flex overflow-hidden bg-gray-100\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"fixed inset-0 flex z-40 md:hidden \".concat(sidebarOpen?'':'hidden'),children:[/*#__PURE__*/_jsx(\"div\",{className:\"fixed inset-0 bg-gray-600 bg-opacity-75\",onClick:()=>setSidebarOpen(false)}),/*#__PURE__*/_jsxs(\"div\",{className:\"relative flex-1 flex flex-col max-w-xs w-full bg-white\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"absolute top-0 right-0 -mr-12 pt-2\",children:/*#__PURE__*/_jsx(\"button\",{className:\"ml-1 flex items-center justify-center h-10 w-10 rounded-full focus:outline-none focus:ring-2 focus:ring-inset focus:ring-white\",onClick:()=>setSidebarOpen(false),children:/*#__PURE__*/_jsx(X,{className:\"h-6 w-6 text-white\"})})}),/*#__PURE__*/_jsx(SidebarContent,{navigation:filteredNavigation,currentPath:location.pathname,businessSettings:businessSettings})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"hidden md:flex md:flex-shrink-0\",children:/*#__PURE__*/_jsx(\"div\",{className:\"flex flex-col w-64\",children:/*#__PURE__*/_jsx(SidebarContent,{navigation:filteredNavigation,currentPath:location.pathname,businessSettings:businessSettings})})}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-col w-0 flex-1 overflow-hidden\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"relative z-10 flex-shrink-0 flex h-16 bg-white shadow\",children:[/*#__PURE__*/_jsx(\"button\",{className:\"px-4 border-r border-gray-200 text-gray-500 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary-500 md:hidden\",onClick:()=>setSidebarOpen(true),children:/*#__PURE__*/_jsx(Menu,{className:\"h-6 w-6\"})}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1 px-4 flex justify-between\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"flex-1 flex\",children:/*#__PURE__*/_jsx(\"div\",{className:\"w-full flex md:ml-0\",children:/*#__PURE__*/_jsx(\"div\",{className:\"relative w-full text-gray-400 focus-within:text-gray-600\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center h-16\",children:[(businessSettings===null||businessSettings===void 0?void 0:businessSettings.logoUrl)&&/*#__PURE__*/_jsx(\"img\",{src:businessSettings.logoUrl,alt:\"Company Logo\",className:\"h-8 w-8 mr-3 object-contain\"}),/*#__PURE__*/_jsxs(\"h1\",{className:\"text-xl font-semibold text-gray-900\",children:[(businessSettings===null||businessSettings===void 0?void 0:businessSettings.businessName)||'Cyber Services & Stationery',\" POS\"]})]})})})}),/*#__PURE__*/_jsx(\"div\",{className:\"ml-4 flex items-center md:ml-6\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-4\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-2\",children:[/*#__PURE__*/_jsx(User,{className:\"h-5 w-5 text-gray-400\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-sm text-gray-700\",children:currentUser===null||currentUser===void 0?void 0:currentUser.name}),/*#__PURE__*/_jsx(\"span\",{className:\"text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded\",children:currentUser===null||currentUser===void 0?void 0:currentUser.role})]}),/*#__PURE__*/_jsx(\"button\",{onClick:handleLogout,className:\"bg-white p-1 rounded-full text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500\",children:/*#__PURE__*/_jsx(LogOut,{className:\"h-5 w-5\"})})]})})]})]}),/*#__PURE__*/_jsx(\"main\",{className:\"flex-1 relative overflow-y-auto focus:outline-none\",children:/*#__PURE__*/_jsx(\"div\",{className:\"py-6\",children:/*#__PURE__*/_jsx(\"div\",{className:\"max-w-7xl mx-auto px-4 sm:px-6 md:px-8\",children:children})})})]})]});};// Sidebar content component\nconst SidebarContent=_ref2=>{let{navigation,currentPath,businessSettings}=_ref2;return/*#__PURE__*/_jsx(\"div\",{className:\"flex flex-col h-0 flex-1 border-r border-gray-200 bg-white\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1 flex flex-col pt-5 pb-4 overflow-y-auto\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center flex-shrink-0 px-4\",children:[businessSettings!==null&&businessSettings!==void 0&&businessSettings.logoUrl?/*#__PURE__*/_jsx(\"img\",{src:businessSettings.logoUrl,alt:\"Company Logo\",className:\"h-8 w-8 object-contain rounded-lg\"}):/*#__PURE__*/_jsx(\"div\",{className:\"h-8 w-8 bg-primary-600 rounded-lg flex items-center justify-center\",children:/*#__PURE__*/_jsx(Monitor,{className:\"h-5 w-5 text-white\"})}),/*#__PURE__*/_jsx(\"span\",{className:\"ml-2 text-lg font-semibold text-gray-900\",children:businessSettings!==null&&businessSettings!==void 0&&businessSettings.businessName?businessSettings.businessName.split(' ').slice(0,2).join(' '):'Cyber POS'})]}),/*#__PURE__*/_jsx(\"nav\",{className:\"mt-5 flex-1 px-2 space-y-1\",children:navigation.map(item=>{const isActive=currentPath===item.href;return/*#__PURE__*/_jsxs(Link,{to:item.href,className:\"\".concat(isActive?'bg-primary-100 border-primary-500 text-primary-700':'border-transparent text-gray-600 hover:bg-gray-50 hover:text-gray-900',\" group flex items-center px-2 py-2 text-sm font-medium border-l-4\"),children:[/*#__PURE__*/_jsx(item.icon,{className:\"\".concat(isActive?'text-primary-500':'text-gray-400 group-hover:text-gray-500',\" mr-3 h-5 w-5\")}),item.name]},item.name);})})]})});};export default Layout;", "map": {"version": 3, "names": ["React", "useState", "Link", "useLocation", "useAuth", "useBusinessSettings", "Monitor", "LayoutDashboard", "ShoppingCart", "Package", "BarChart3", "Settings", "LogOut", "<PERSON><PERSON>", "X", "User", "jsx", "_jsx", "jsxs", "_jsxs", "Layout", "_ref", "children", "sidebarOpen", "setSidebarOpen", "currentUser", "logout", "hasPermission", "businessSettings", "location", "navigation", "name", "href", "icon", "roles", "filteredNavigation", "filter", "item", "handleLogout", "error", "console", "className", "concat", "onClick", "<PERSON>bar<PERSON><PERSON>nt", "currentPath", "pathname", "logoUrl", "src", "alt", "businessName", "role", "_ref2", "split", "slice", "join", "map", "isActive", "to"], "sources": ["E:/FX/Cyber POS/cyber-pos-system/src/components/layout/Layout.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Link, useLocation } from 'react-router-dom';\nimport { useAuth } from '../../contexts/AuthContext';\nimport { useBusinessSettings } from '../../contexts/BusinessSettingsContext';\nimport {\n  Monitor,\n  LayoutDashboard,\n  ShoppingCart,\n  Package,\n  BarChart3,\n  Settings,\n  LogOut,\n  Menu,\n  X,\n  User\n} from 'lucide-react';\n\ninterface LayoutProps {\n  children: React.ReactNode;\n}\n\nconst Layout: React.FC<LayoutProps> = ({ children }) => {\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n  const { currentUser, logout, hasPermission } = useAuth();\n  const { businessSettings } = useBusinessSettings();\n  const location = useLocation();\n\n  const navigation = [\n    { name: 'Dashboard', href: '/dashboard', icon: LayoutDashboard, roles: ['admin', 'attendant', 'technician'] },\n    { name: '<PERSON><PERSON>', href: '/pos', icon: ShoppingCart, roles: ['admin', 'attendant'] },\n    { name: 'Services', href: '/services', icon: Monitor, roles: ['admin'] },\n    { name: 'Inventory', href: '/inventory', icon: Package, roles: ['admin', 'attendant'] },\n    { name: 'Reports', href: '/reports', icon: BarChart3, roles: ['admin'] },\n    { name: 'Settings', href: '/settings', icon: Settings, roles: ['admin', 'technician'] },\n  ];\n\n  const filteredNavigation = navigation.filter(item => \n    hasPermission(item.roles as any)\n  );\n\n  const handleLogout = async () => {\n    try {\n      await logout();\n    } catch (error) {\n      console.error('Logout error:', error);\n    }\n  };\n\n  return (\n    <div className=\"h-screen flex overflow-hidden bg-gray-100\">\n      {/* Mobile sidebar */}\n      <div className={`fixed inset-0 flex z-40 md:hidden ${sidebarOpen ? '' : 'hidden'}`}>\n        <div className=\"fixed inset-0 bg-gray-600 bg-opacity-75\" onClick={() => setSidebarOpen(false)} />\n        <div className=\"relative flex-1 flex flex-col max-w-xs w-full bg-white\">\n          <div className=\"absolute top-0 right-0 -mr-12 pt-2\">\n            <button\n              className=\"ml-1 flex items-center justify-center h-10 w-10 rounded-full focus:outline-none focus:ring-2 focus:ring-inset focus:ring-white\"\n              onClick={() => setSidebarOpen(false)}\n            >\n              <X className=\"h-6 w-6 text-white\" />\n            </button>\n          </div>\n          <SidebarContent navigation={filteredNavigation} currentPath={location.pathname} businessSettings={businessSettings} />\n        </div>\n      </div>\n\n      {/* Desktop sidebar */}\n      <div className=\"hidden md:flex md:flex-shrink-0\">\n        <div className=\"flex flex-col w-64\">\n          <SidebarContent navigation={filteredNavigation} currentPath={location.pathname} businessSettings={businessSettings} />\n        </div>\n      </div>\n\n      {/* Main content */}\n      <div className=\"flex flex-col w-0 flex-1 overflow-hidden\">\n        {/* Top bar */}\n        <div className=\"relative z-10 flex-shrink-0 flex h-16 bg-white shadow\">\n          <button\n            className=\"px-4 border-r border-gray-200 text-gray-500 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary-500 md:hidden\"\n            onClick={() => setSidebarOpen(true)}\n          >\n            <Menu className=\"h-6 w-6\" />\n          </button>\n          \n          <div className=\"flex-1 px-4 flex justify-between\">\n            <div className=\"flex-1 flex\">\n              <div className=\"w-full flex md:ml-0\">\n                <div className=\"relative w-full text-gray-400 focus-within:text-gray-600\">\n                  <div className=\"flex items-center h-16\">\n                    {businessSettings?.logoUrl && (\n                      <img\n                        src={businessSettings.logoUrl}\n                        alt=\"Company Logo\"\n                        className=\"h-8 w-8 mr-3 object-contain\"\n                      />\n                    )}\n                    <h1 className=\"text-xl font-semibold text-gray-900\">\n                      {businessSettings?.businessName || 'Cyber Services & Stationery'} POS\n                    </h1>\n                  </div>\n                </div>\n              </div>\n            </div>\n            \n            <div className=\"ml-4 flex items-center md:ml-6\">\n              <div className=\"flex items-center space-x-4\">\n                <div className=\"flex items-center space-x-2\">\n                  <User className=\"h-5 w-5 text-gray-400\" />\n                  <span className=\"text-sm text-gray-700\">{currentUser?.name}</span>\n                  <span className=\"text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded\">\n                    {currentUser?.role}\n                  </span>\n                </div>\n                <button\n                  onClick={handleLogout}\n                  className=\"bg-white p-1 rounded-full text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500\"\n                >\n                  <LogOut className=\"h-5 w-5\" />\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Page content */}\n        <main className=\"flex-1 relative overflow-y-auto focus:outline-none\">\n          <div className=\"py-6\">\n            <div className=\"max-w-7xl mx-auto px-4 sm:px-6 md:px-8\">\n              {children}\n            </div>\n          </div>\n        </main>\n      </div>\n    </div>\n  );\n};\n\n// Sidebar content component\nconst SidebarContent: React.FC<{\n  navigation: Array<{\n    name: string;\n    href: string;\n    icon: React.ComponentType<any>;\n    roles: string[];\n  }>;\n  currentPath: string;\n  businessSettings: any;\n}> = ({ navigation, currentPath, businessSettings }) => {\n  return (\n    <div className=\"flex flex-col h-0 flex-1 border-r border-gray-200 bg-white\">\n      <div className=\"flex-1 flex flex-col pt-5 pb-4 overflow-y-auto\">\n        <div className=\"flex items-center flex-shrink-0 px-4\">\n          {businessSettings?.logoUrl ? (\n            <img\n              src={businessSettings.logoUrl}\n              alt=\"Company Logo\"\n              className=\"h-8 w-8 object-contain rounded-lg\"\n            />\n          ) : (\n            <div className=\"h-8 w-8 bg-primary-600 rounded-lg flex items-center justify-center\">\n              <Monitor className=\"h-5 w-5 text-white\" />\n            </div>\n          )}\n          <span className=\"ml-2 text-lg font-semibold text-gray-900\">\n            {businessSettings?.businessName ?\n              businessSettings.businessName.split(' ').slice(0, 2).join(' ') :\n              'Cyber POS'\n            }\n          </span>\n        </div>\n        <nav className=\"mt-5 flex-1 px-2 space-y-1\">\n          {navigation.map((item) => {\n            const isActive = currentPath === item.href;\n            return (\n              <Link\n                key={item.name}\n                to={item.href}\n                className={`${\n                  isActive\n                    ? 'bg-primary-100 border-primary-500 text-primary-700'\n                    : 'border-transparent text-gray-600 hover:bg-gray-50 hover:text-gray-900'\n                } group flex items-center px-2 py-2 text-sm font-medium border-l-4`}\n              >\n                <item.icon\n                  className={`${\n                    isActive ? 'text-primary-500' : 'text-gray-400 group-hover:text-gray-500'\n                  } mr-3 h-5 w-5`}\n                />\n                {item.name}\n              </Link>\n            );\n          })}\n        </nav>\n      </div>\n    </div>\n  );\n};\n\nexport default Layout;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,KAAQ,OAAO,CACvC,OAASC,IAAI,CAAEC,WAAW,KAAQ,kBAAkB,CACpD,OAASC,OAAO,KAAQ,4BAA4B,CACpD,OAASC,mBAAmB,KAAQ,wCAAwC,CAC5E,OACEC,OAAO,CACPC,eAAe,CACfC,YAAY,CACZC,OAAO,CACPC,SAAS,CACTC,QAAQ,CACRC,MAAM,CACNC,IAAI,CACJC,CAAC,CACDC,IAAI,KACC,cAAc,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAMtB,KAAM,CAAAC,MAA6B,CAAGC,IAAA,EAAkB,IAAjB,CAAEC,QAAS,CAAC,CAAAD,IAAA,CACjD,KAAM,CAACE,WAAW,CAAEC,cAAc,CAAC,CAAGvB,QAAQ,CAAC,KAAK,CAAC,CACrD,KAAM,CAAEwB,WAAW,CAAEC,MAAM,CAAEC,aAAc,CAAC,CAAGvB,OAAO,CAAC,CAAC,CACxD,KAAM,CAAEwB,gBAAiB,CAAC,CAAGvB,mBAAmB,CAAC,CAAC,CAClD,KAAM,CAAAwB,QAAQ,CAAG1B,WAAW,CAAC,CAAC,CAE9B,KAAM,CAAA2B,UAAU,CAAG,CACjB,CAAEC,IAAI,CAAE,WAAW,CAAEC,IAAI,CAAE,YAAY,CAAEC,IAAI,CAAE1B,eAAe,CAAE2B,KAAK,CAAE,CAAC,OAAO,CAAE,WAAW,CAAE,YAAY,CAAE,CAAC,CAC7G,CAAEH,IAAI,CAAE,KAAK,CAAEC,IAAI,CAAE,MAAM,CAAEC,IAAI,CAAEzB,YAAY,CAAE0B,KAAK,CAAE,CAAC,OAAO,CAAE,WAAW,CAAE,CAAC,CAChF,CAAEH,IAAI,CAAE,UAAU,CAAEC,IAAI,CAAE,WAAW,CAAEC,IAAI,CAAE3B,OAAO,CAAE4B,KAAK,CAAE,CAAC,OAAO,CAAE,CAAC,CACxE,CAAEH,IAAI,CAAE,WAAW,CAAEC,IAAI,CAAE,YAAY,CAAEC,IAAI,CAAExB,OAAO,CAAEyB,KAAK,CAAE,CAAC,OAAO,CAAE,WAAW,CAAE,CAAC,CACvF,CAAEH,IAAI,CAAE,SAAS,CAAEC,IAAI,CAAE,UAAU,CAAEC,IAAI,CAAEvB,SAAS,CAAEwB,KAAK,CAAE,CAAC,OAAO,CAAE,CAAC,CACxE,CAAEH,IAAI,CAAE,UAAU,CAAEC,IAAI,CAAE,WAAW,CAAEC,IAAI,CAAEtB,QAAQ,CAAEuB,KAAK,CAAE,CAAC,OAAO,CAAE,YAAY,CAAE,CAAC,CACxF,CAED,KAAM,CAAAC,kBAAkB,CAAGL,UAAU,CAACM,MAAM,CAACC,IAAI,EAC/CV,aAAa,CAACU,IAAI,CAACH,KAAY,CACjC,CAAC,CAED,KAAM,CAAAI,YAAY,CAAG,KAAAA,CAAA,GAAY,CAC/B,GAAI,CACF,KAAM,CAAAZ,MAAM,CAAC,CAAC,CAChB,CAAE,MAAOa,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,eAAe,CAAEA,KAAK,CAAC,CACvC,CACF,CAAC,CAED,mBACEpB,KAAA,QAAKsB,SAAS,CAAC,2CAA2C,CAAAnB,QAAA,eAExDH,KAAA,QAAKsB,SAAS,sCAAAC,MAAA,CAAuCnB,WAAW,CAAG,EAAE,CAAG,QAAQ,CAAG,CAAAD,QAAA,eACjFL,IAAA,QAAKwB,SAAS,CAAC,yCAAyC,CAACE,OAAO,CAAEA,CAAA,GAAMnB,cAAc,CAAC,KAAK,CAAE,CAAE,CAAC,cACjGL,KAAA,QAAKsB,SAAS,CAAC,wDAAwD,CAAAnB,QAAA,eACrEL,IAAA,QAAKwB,SAAS,CAAC,oCAAoC,CAAAnB,QAAA,cACjDL,IAAA,WACEwB,SAAS,CAAC,gIAAgI,CAC1IE,OAAO,CAAEA,CAAA,GAAMnB,cAAc,CAAC,KAAK,CAAE,CAAAF,QAAA,cAErCL,IAAA,CAACH,CAAC,EAAC2B,SAAS,CAAC,oBAAoB,CAAE,CAAC,CAC9B,CAAC,CACN,CAAC,cACNxB,IAAA,CAAC2B,cAAc,EAACd,UAAU,CAAEK,kBAAmB,CAACU,WAAW,CAAEhB,QAAQ,CAACiB,QAAS,CAAClB,gBAAgB,CAAEA,gBAAiB,CAAE,CAAC,EACnH,CAAC,EACH,CAAC,cAGNX,IAAA,QAAKwB,SAAS,CAAC,iCAAiC,CAAAnB,QAAA,cAC9CL,IAAA,QAAKwB,SAAS,CAAC,oBAAoB,CAAAnB,QAAA,cACjCL,IAAA,CAAC2B,cAAc,EAACd,UAAU,CAAEK,kBAAmB,CAACU,WAAW,CAAEhB,QAAQ,CAACiB,QAAS,CAAClB,gBAAgB,CAAEA,gBAAiB,CAAE,CAAC,CACnH,CAAC,CACH,CAAC,cAGNT,KAAA,QAAKsB,SAAS,CAAC,0CAA0C,CAAAnB,QAAA,eAEvDH,KAAA,QAAKsB,SAAS,CAAC,uDAAuD,CAAAnB,QAAA,eACpEL,IAAA,WACEwB,SAAS,CAAC,+HAA+H,CACzIE,OAAO,CAAEA,CAAA,GAAMnB,cAAc,CAAC,IAAI,CAAE,CAAAF,QAAA,cAEpCL,IAAA,CAACJ,IAAI,EAAC4B,SAAS,CAAC,SAAS,CAAE,CAAC,CACtB,CAAC,cAETtB,KAAA,QAAKsB,SAAS,CAAC,kCAAkC,CAAAnB,QAAA,eAC/CL,IAAA,QAAKwB,SAAS,CAAC,aAAa,CAAAnB,QAAA,cAC1BL,IAAA,QAAKwB,SAAS,CAAC,qBAAqB,CAAAnB,QAAA,cAClCL,IAAA,QAAKwB,SAAS,CAAC,0DAA0D,CAAAnB,QAAA,cACvEH,KAAA,QAAKsB,SAAS,CAAC,wBAAwB,CAAAnB,QAAA,EACpC,CAAAM,gBAAgB,SAAhBA,gBAAgB,iBAAhBA,gBAAgB,CAAEmB,OAAO,gBACxB9B,IAAA,QACE+B,GAAG,CAAEpB,gBAAgB,CAACmB,OAAQ,CAC9BE,GAAG,CAAC,cAAc,CAClBR,SAAS,CAAC,6BAA6B,CACxC,CACF,cACDtB,KAAA,OAAIsB,SAAS,CAAC,qCAAqC,CAAAnB,QAAA,EAChD,CAAAM,gBAAgB,SAAhBA,gBAAgB,iBAAhBA,gBAAgB,CAAEsB,YAAY,GAAI,6BAA6B,CAAC,MACnE,EAAI,CAAC,EACF,CAAC,CACH,CAAC,CACH,CAAC,CACH,CAAC,cAENjC,IAAA,QAAKwB,SAAS,CAAC,gCAAgC,CAAAnB,QAAA,cAC7CH,KAAA,QAAKsB,SAAS,CAAC,6BAA6B,CAAAnB,QAAA,eAC1CH,KAAA,QAAKsB,SAAS,CAAC,6BAA6B,CAAAnB,QAAA,eAC1CL,IAAA,CAACF,IAAI,EAAC0B,SAAS,CAAC,uBAAuB,CAAE,CAAC,cAC1CxB,IAAA,SAAMwB,SAAS,CAAC,uBAAuB,CAAAnB,QAAA,CAAEG,WAAW,SAAXA,WAAW,iBAAXA,WAAW,CAAEM,IAAI,CAAO,CAAC,cAClEd,IAAA,SAAMwB,SAAS,CAAC,qDAAqD,CAAAnB,QAAA,CAClEG,WAAW,SAAXA,WAAW,iBAAXA,WAAW,CAAE0B,IAAI,CACd,CAAC,EACJ,CAAC,cACNlC,IAAA,WACE0B,OAAO,CAAEL,YAAa,CACtBG,SAAS,CAAC,wIAAwI,CAAAnB,QAAA,cAElJL,IAAA,CAACL,MAAM,EAAC6B,SAAS,CAAC,SAAS,CAAE,CAAC,CACxB,CAAC,EACN,CAAC,CACH,CAAC,EACH,CAAC,EACH,CAAC,cAGNxB,IAAA,SAAMwB,SAAS,CAAC,oDAAoD,CAAAnB,QAAA,cAClEL,IAAA,QAAKwB,SAAS,CAAC,MAAM,CAAAnB,QAAA,cACnBL,IAAA,QAAKwB,SAAS,CAAC,wCAAwC,CAAAnB,QAAA,CACpDA,QAAQ,CACN,CAAC,CACH,CAAC,CACF,CAAC,EACJ,CAAC,EACH,CAAC,CAEV,CAAC,CAED;AACA,KAAM,CAAAsB,cASJ,CAAGQ,KAAA,EAAmD,IAAlD,CAAEtB,UAAU,CAAEe,WAAW,CAAEjB,gBAAiB,CAAC,CAAAwB,KAAA,CACjD,mBACEnC,IAAA,QAAKwB,SAAS,CAAC,4DAA4D,CAAAnB,QAAA,cACzEH,KAAA,QAAKsB,SAAS,CAAC,gDAAgD,CAAAnB,QAAA,eAC7DH,KAAA,QAAKsB,SAAS,CAAC,sCAAsC,CAAAnB,QAAA,EAClDM,gBAAgB,SAAhBA,gBAAgB,WAAhBA,gBAAgB,CAAEmB,OAAO,cACxB9B,IAAA,QACE+B,GAAG,CAAEpB,gBAAgB,CAACmB,OAAQ,CAC9BE,GAAG,CAAC,cAAc,CAClBR,SAAS,CAAC,mCAAmC,CAC9C,CAAC,cAEFxB,IAAA,QAAKwB,SAAS,CAAC,oEAAoE,CAAAnB,QAAA,cACjFL,IAAA,CAACX,OAAO,EAACmC,SAAS,CAAC,oBAAoB,CAAE,CAAC,CACvC,CACN,cACDxB,IAAA,SAAMwB,SAAS,CAAC,0CAA0C,CAAAnB,QAAA,CACvDM,gBAAgB,SAAhBA,gBAAgB,WAAhBA,gBAAgB,CAAEsB,YAAY,CAC7BtB,gBAAgB,CAACsB,YAAY,CAACG,KAAK,CAAC,GAAG,CAAC,CAACC,KAAK,CAAC,CAAC,CAAE,CAAC,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC,CAC9D,WAAW,CAET,CAAC,EACJ,CAAC,cACNtC,IAAA,QAAKwB,SAAS,CAAC,4BAA4B,CAAAnB,QAAA,CACxCQ,UAAU,CAAC0B,GAAG,CAAEnB,IAAI,EAAK,CACxB,KAAM,CAAAoB,QAAQ,CAAGZ,WAAW,GAAKR,IAAI,CAACL,IAAI,CAC1C,mBACEb,KAAA,CAACjB,IAAI,EAEHwD,EAAE,CAAErB,IAAI,CAACL,IAAK,CACdS,SAAS,IAAAC,MAAA,CACPe,QAAQ,CACJ,oDAAoD,CACpD,uEAAuE,qEACT,CAAAnC,QAAA,eAEpEL,IAAA,CAACoB,IAAI,CAACJ,IAAI,EACRQ,SAAS,IAAAC,MAAA,CACPe,QAAQ,CAAG,kBAAkB,CAAG,yCAAyC,iBAC3D,CACjB,CAAC,CACDpB,IAAI,CAACN,IAAI,GAbLM,IAAI,CAACN,IAcN,CAAC,CAEX,CAAC,CAAC,CACC,CAAC,EACH,CAAC,CACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAAX,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}