{"ast": null, "code": "/**\n * Utility function to remove undefined values from objects\n * This is essential for Firebase operations which don't allow undefined values\n */export const cleanObject=obj=>{const cleaned={};for(const[key,value]of Object.entries(obj)){if(value!==undefined){if(typeof value==='object'&&value!==null&&!Array.isArray(value)&&!(value instanceof Date)){cleaned[key]=cleanObject(value);}else{cleaned[key]=value;}}}return cleaned;};/**\n * Clean an array of objects, removing undefined values from each object\n */export const cleanObjectArray=arr=>{return arr.map(item=>cleanObject(item));};", "map": {"version": 3, "names": ["cleanObject", "obj", "cleaned", "key", "value", "Object", "entries", "undefined", "Array", "isArray", "Date", "cleanObjectArray", "arr", "map", "item"], "sources": ["E:/FX/Cyber POS/cyber-pos-system/src/utils/objectUtils.ts"], "sourcesContent": ["/**\n * Utility function to remove undefined values from objects\n * This is essential for Firebase operations which don't allow undefined values\n */\nexport const cleanObject = (obj: any): any => {\n  const cleaned: any = {};\n  for (const [key, value] of Object.entries(obj)) {\n    if (value !== undefined) {\n      if (typeof value === 'object' && value !== null && !Array.isArray(value) && !(value instanceof Date)) {\n        cleaned[key] = cleanObject(value);\n      } else {\n        cleaned[key] = value;\n      }\n    }\n  }\n  return cleaned;\n};\n\n/**\n * Clean an array of objects, removing undefined values from each object\n */\nexport const cleanObjectArray = <T>(arr: T[]): T[] => {\n  return arr.map(item => cleanObject(item));\n};\n"], "mappings": "AAAA;AACA;AACA;AACA,GACA,MAAO,MAAM,CAAAA,WAAW,CAAIC,GAAQ,EAAU,CAC5C,KAAM,CAAAC,OAAY,CAAG,CAAC,CAAC,CACvB,IAAK,KAAM,CAACC,GAAG,CAAEC,KAAK,CAAC,EAAI,CAAAC,MAAM,CAACC,OAAO,CAACL,GAAG,CAAC,CAAE,CAC9C,GAAIG,KAAK,GAAKG,SAAS,CAAE,CACvB,GAAI,MAAO,CAAAH,KAAK,GAAK,QAAQ,EAAIA,KAAK,GAAK,IAAI,EAAI,CAACI,KAAK,CAACC,OAAO,CAACL,KAAK,CAAC,EAAI,EAAEA,KAAK,WAAY,CAAAM,IAAI,CAAC,CAAE,CACpGR,OAAO,CAACC,GAAG,CAAC,CAAGH,WAAW,CAACI,KAAK,CAAC,CACnC,CAAC,IAAM,CACLF,OAAO,CAACC,GAAG,CAAC,CAAGC,KAAK,CACtB,CACF,CACF,CACA,MAAO,CAAAF,OAAO,CAChB,CAAC,CAED;AACA;AACA,GACA,MAAO,MAAM,CAAAS,gBAAgB,CAAOC,GAAQ,EAAU,CACpD,MAAO,CAAAA,GAAG,CAACC,GAAG,CAACC,IAAI,EAAId,WAAW,CAACc,IAAI,CAAC,CAAC,CAC3C,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}