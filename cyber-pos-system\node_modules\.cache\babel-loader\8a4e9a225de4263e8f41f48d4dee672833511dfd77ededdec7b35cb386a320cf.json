{"ast": null, "code": "import*as XLSX from'xlsx';// Required columns for product import\nconst REQUIRED_COLUMNS=['Name','Category','Price','Stock Quantity'];// Optional columns with their default values\nconst OPTIONAL_COLUMNS={'Description':'','Reorder Level':10,'Has Expiry':false,'Expiry Date':null,'Is Active':true};// Column mapping for different naming conventions\nconst COLUMN_MAPPINGS={'product name':'Name','product_name':'Name','productname':'Name','item name':'Name','item_name':'Name','itemname':'Name','title':'Name','product category':'Category','product_category':'Category','productcategory':'Category','item category':'Category','item_category':'Category','itemcategory':'Category','cat':'Category','unit price':'Price','unit_price':'Price','unitprice':'Price','cost':'Price','amount':'Price','stock':'Stock Quantity','quantity':'Stock Quantity','qty':'Stock Quantity','inventory':'Stock Quantity','stock_quantity':'Stock Quantity','stockquantity':'Stock Quantity','desc':'Description','product description':'Description','product_description':'Description','productdescription':'Description','item description':'Description','item_description':'Description','itemdescription':'Description','reorder':'Reorder Level','reorder_level':'Reorder Level','reorderlevel':'Reorder Level','min stock':'Reorder Level','min_stock':'Reorder Level','minstock':'Reorder Level','minimum':'Reorder Level','expiry':'Has Expiry','has_expiry':'Has Expiry','hasexpiry':'Has Expiry','expires':'Has Expiry','perishable':'Has Expiry','expiry_date':'Expiry Date','expirydate':'Expiry Date','expiration':'Expiry Date','expiration_date':'Expiry Date','expirationdate':'Expiry Date','expires_on':'Expiry Date','expireson':'Expiry Date','active':'Is Active','is_active':'Is Active','isactive':'Is Active','enabled':'Is Active','status':'Is Active'};/**\n * Normalize column headers to match expected format\n */function normalizeHeaders(headers){return headers.map(header=>{const normalized=header.toLowerCase().trim();return COLUMN_MAPPINGS[normalized]||header;});}/**\n * Validate that required columns are present\n */function validateHeaders(headers){const normalizedHeaders=normalizeHeaders(headers);const errors=[];const warnings=[];// Check for required columns\nconst missingRequired=REQUIRED_COLUMNS.filter(required=>!normalizedHeaders.includes(required));if(missingRequired.length>0){errors.push(\"Missing required columns: \".concat(missingRequired.join(', ')));}// Check for duplicate columns\nconst duplicates=normalizedHeaders.filter((header,index)=>normalizedHeaders.indexOf(header)!==index);if(duplicates.length>0){warnings.push(\"Duplicate columns found: \".concat(duplicates.join(', ')));}return{isValid:errors.length===0,errors,warnings};}/**\n * Parse and validate a single row of product data\n */function parseProductRow(rowData,rowIndex){const errors=[];const product={};try{// Name (required)\nconst name=String(rowData['Name']||'').trim();if(!name){errors.push(\"Row \".concat(rowIndex,\": Name is required\"));}else{product.name=name;}// Category (required)\nconst category=String(rowData['Category']||'').trim();if(!category){errors.push(\"Row \".concat(rowIndex,\": Category is required\"));}else{product.category=category;}// Price (required)\nconst priceValue=rowData['Price'];if(priceValue===undefined||priceValue===null||priceValue===''){errors.push(\"Row \".concat(rowIndex,\": Price is required\"));}else{const price=parseFloat(String(priceValue).replace(/[^\\d.-]/g,''));if(isNaN(price)||price<0){errors.push(\"Row \".concat(rowIndex,\": Price must be a valid positive number\"));}else{product.price=price;}}// Stock Quantity (required)\nconst stockValue=rowData['Stock Quantity'];if(stockValue===undefined||stockValue===null||stockValue===''){errors.push(\"Row \".concat(rowIndex,\": Stock Quantity is required\"));}else{const stockQuantity=parseInt(String(stockValue));if(isNaN(stockQuantity)||stockQuantity<0){errors.push(\"Row \".concat(rowIndex,\": Stock Quantity must be a valid non-negative integer\"));}else{product.stockQuantity=stockQuantity;}}// Description (optional)\nconst description=String(rowData['Description']||'').trim();product.description=description;// Reorder Level (optional)\nconst reorderValue=rowData['Reorder Level'];if(reorderValue!==undefined&&reorderValue!==null&&reorderValue!==''){const reorderLevel=parseInt(String(reorderValue));if(isNaN(reorderLevel)||reorderLevel<0){errors.push(\"Row \".concat(rowIndex,\": Reorder Level must be a valid non-negative integer\"));}else{product.reorderLevel=reorderLevel;}}else{product.reorderLevel=10;// Default value\n}// Has Expiry (optional)\nconst hasExpiryValue=rowData['Has Expiry'];if(hasExpiryValue!==undefined&&hasExpiryValue!==null&&hasExpiryValue!==''){const hasExpiryStr=String(hasExpiryValue).toLowerCase().trim();product.hasExpiry=['true','1','yes','y'].includes(hasExpiryStr);}else{product.hasExpiry=false;}// Expiry Date (optional)\nconst expiryDateValue=rowData['Expiry Date'];if(expiryDateValue&&product.hasExpiry){try{let expiryDate;if(typeof expiryDateValue==='number'){// Excel date serial number\nexpiryDate=XLSX.SSF.parse_date_code(expiryDateValue);}else{// String date\nexpiryDate=new Date(String(expiryDateValue));}if(isNaN(expiryDate.getTime())){errors.push(\"Row \".concat(rowIndex,\": Invalid expiry date format\"));}else{product.expiryDate=expiryDate;}}catch(error){errors.push(\"Row \".concat(rowIndex,\": Invalid expiry date format\"));}}// Is Active (optional)\nconst isActiveValue=rowData['Is Active'];if(isActiveValue!==undefined&&isActiveValue!==null&&isActiveValue!==''){const isActiveStr=String(isActiveValue).toLowerCase().trim();product.isActive=!['false','0','no','n','inactive','disabled'].includes(isActiveStr);}else{product.isActive=true;// Default value\n}return{product:errors.length===0?product:null,errors};}catch(error){errors.push(\"Row \".concat(rowIndex,\": Error parsing data - \").concat(error instanceof Error?error.message:'Unknown error'));return{product:null,errors};}}/**\n * Parse Excel file and extract product data\n */export async function parseExcelFile(file){try{const arrayBuffer=await file.arrayBuffer();const workbook=XLSX.read(arrayBuffer,{type:'array'});// Use the first worksheet\nconst firstSheetName=workbook.SheetNames[0];if(!firstSheetName){return{success:false,errors:['No worksheets found in the Excel file']};}const worksheet=workbook.Sheets[firstSheetName];// Convert to JSON with header row\nconst jsonData=XLSX.utils.sheet_to_json(worksheet,{header:1,defval:'',blankrows:false});if(jsonData.length===0){return{success:false,errors:['The Excel file appears to be empty']};}// Extract headers from first row\nconst headers=jsonData[0].map(header=>String(header).trim());const normalizedHeaders=normalizeHeaders(headers);// Validate headers\nconst headerValidation=validateHeaders(headers);if(!headerValidation.isValid){return{success:false,errors:headerValidation.errors,warnings:headerValidation.warnings};}// Process data rows\nconst products=[];const errors=[...headerValidation.errors];const warnings=[...headerValidation.warnings];for(let i=1;i<jsonData.length;i++){const row=jsonData[i];// Skip empty rows\nif(row.every(cell=>!cell||String(cell).trim()==='')){continue;}// Create row object with normalized headers\nconst rowData={};normalizedHeaders.forEach((header,index)=>{rowData[header]=row[index];});// Parse and validate the row\nconst{product,errors:rowErrors}=parseProductRow(rowData,i+1);if(product){products.push(product);}errors.push(...rowErrors);}return{success:errors.length===0,data:products,errors:errors.length>0?errors:undefined,warnings:warnings.length>0?warnings:undefined};}catch(error){return{success:false,errors:[\"Failed to parse Excel file: \".concat(error instanceof Error?error.message:'Unknown error')]};}}/**\n * Parse CSV file and extract product data\n */export async function parseCSVFile(file){try{const text=await file.text();const lines=text.split('\\n').filter(line=>line.trim());if(lines.length===0){return{success:false,errors:['The CSV file appears to be empty']};}// Parse CSV headers\nconst headers=lines[0].split(',').map(h=>h.replace(/\"/g,'').trim());const normalizedHeaders=normalizeHeaders(headers);// Validate headers\nconst headerValidation=validateHeaders(headers);if(!headerValidation.isValid){return{success:false,errors:headerValidation.errors,warnings:headerValidation.warnings};}// Process data rows\nconst products=[];const errors=[...headerValidation.errors];const warnings=[...headerValidation.warnings];for(let i=1;i<lines.length;i++){const line=lines[i].trim();if(!line)continue;// Simple CSV parsing (handles quoted values)\nconst values=line.split(',').map(v=>v.replace(/\"/g,'').trim());// Create row object with normalized headers\nconst rowData={};normalizedHeaders.forEach((header,index)=>{rowData[header]=values[index]||'';});// Parse and validate the row\nconst{product,errors:rowErrors}=parseProductRow(rowData,i+1);if(product){products.push(product);}errors.push(...rowErrors);}return{success:errors.length===0,data:products,errors:errors.length>0?errors:undefined,warnings:warnings.length>0?warnings:undefined};}catch(error){return{success:false,errors:[\"Failed to parse CSV file: \".concat(error instanceof Error?error.message:'Unknown error')]};}}/**\n * Main function to parse import file (Excel or CSV)\n */export async function parseImportFile(file){const fileName=file.name.toLowerCase();if(fileName.endsWith('.xlsx')||fileName.endsWith('.xls')){return parseExcelFile(file);}else if(fileName.endsWith('.csv')){return parseCSVFile(file);}else{return{success:false,errors:['Unsupported file format. Please use Excel (.xlsx, .xls) or CSV (.csv) files.']};}}/**\n * Generate a sample Excel template for product import\n */export function generateImportTemplate(){const headers=['Name','Description','Category','Price','Stock Quantity','Reorder Level','Has Expiry','Expiry Date','Is Active'];const sampleData=[['A4 Paper (Ream)','500 sheets of A4 paper','Paper',450,25,10,'No','','Yes'],['Blue Pen','Ballpoint pen - blue ink','Writing',20,50,20,'No','','Yes'],['Printer Ink Cartridge','Black ink cartridge for HP printers','Printer',1200,5,3,'Yes','2025-12-31','Yes']];// Create workbook and worksheet\nconst workbook=XLSX.utils.book_new();const worksheet=XLSX.utils.aoa_to_sheet([headers,...sampleData]);// Add worksheet to workbook\nXLSX.utils.book_append_sheet(workbook,worksheet,'Products');// Generate and download file\nXLSX.writeFile(workbook,\"product-import-template-\".concat(new Date().toISOString().split('T')[0],\".xlsx\"));}", "map": {"version": 3, "names": ["XLSX", "REQUIRED_COLUMNS", "OPTIONAL_COLUMNS", "COLUMN_MAPPINGS", "normalizeHeaders", "headers", "map", "header", "normalized", "toLowerCase", "trim", "validateHeaders", "normalizedHeaders", "errors", "warnings", "missingRequired", "filter", "required", "includes", "length", "push", "concat", "join", "duplicates", "index", "indexOf", "<PERSON><PERSON><PERSON><PERSON>", "parseProductRow", "rowData", "rowIndex", "product", "name", "String", "category", "priceValue", "undefined", "price", "parseFloat", "replace", "isNaN", "stockValue", "stockQuantity", "parseInt", "description", "reorderValue", "reorderLevel", "hasExpiryValue", "hasExpiryStr", "hasEx<PERSON>ry", "expiryDateValue", "expiryDate", "SSF", "parse_date_code", "Date", "getTime", "error", "isActiveValue", "isActiveStr", "isActive", "Error", "message", "parseExcelFile", "file", "arrayBuffer", "workbook", "read", "type", "firstSheetName", "SheetNames", "success", "worksheet", "Sheets", "jsonData", "utils", "sheet_to_json", "defval", "blankrows", "headerValidation", "products", "i", "row", "every", "cell", "for<PERSON>ach", "rowErrors", "data", "parseCSVFile", "text", "lines", "split", "line", "h", "values", "v", "parseImportFile", "fileName", "endsWith", "generateImportTemplate", "sampleData", "book_new", "aoa_to_sheet", "book_append_sheet", "writeFile", "toISOString"], "sources": ["E:/FX/Cyber POS/cyber-pos-system/src/utils/excelImport.ts"], "sourcesContent": ["import * as XLSX from 'xlsx';\nimport { Product } from '../types';\n\nexport interface ImportResult {\n  success: boolean;\n  data?: Partial<Product>[];\n  errors?: string[];\n  warnings?: string[];\n}\n\nexport interface ImportValidationResult {\n  isValid: boolean;\n  errors: string[];\n  warnings: string[];\n}\n\n// Required columns for product import\nconst REQUIRED_COLUMNS = ['Name', 'Category', 'Price', 'Stock Quantity'];\n\n// Optional columns with their default values\nconst OPTIONAL_COLUMNS = {\n  'Description': '',\n  'Reorder Level': 10,\n  'Has Expiry': false,\n  'Expiry Date': null,\n  'Is Active': true\n};\n\n// Column mapping for different naming conventions\nconst COLUMN_MAPPINGS: { [key: string]: string } = {\n  'product name': 'Name',\n  'product_name': 'Name',\n  'productname': 'Name',\n  'item name': 'Name',\n  'item_name': 'Name',\n  'itemname': 'Name',\n  'title': 'Name',\n  \n  'product category': 'Category',\n  'product_category': 'Category',\n  'productcategory': 'Category',\n  'item category': 'Category',\n  'item_category': 'Category',\n  'itemcategory': 'Category',\n  'cat': 'Category',\n  \n  'unit price': 'Price',\n  'unit_price': 'Price',\n  'unitprice': 'Price',\n  'cost': 'Price',\n  'amount': 'Price',\n  \n  'stock': 'Stock Quantity',\n  'quantity': 'Stock Quantity',\n  'qty': 'Stock Quantity',\n  'inventory': 'Stock Quantity',\n  'stock_quantity': 'Stock Quantity',\n  'stockquantity': 'Stock Quantity',\n  \n  'desc': 'Description',\n  'product description': 'Description',\n  'product_description': 'Description',\n  'productdescription': 'Description',\n  'item description': 'Description',\n  'item_description': 'Description',\n  'itemdescription': 'Description',\n  \n  'reorder': 'Reorder Level',\n  'reorder_level': 'Reorder Level',\n  'reorderlevel': 'Reorder Level',\n  'min stock': 'Reorder Level',\n  'min_stock': 'Reorder Level',\n  'minstock': 'Reorder Level',\n  'minimum': 'Reorder Level',\n  \n  'expiry': 'Has Expiry',\n  'has_expiry': 'Has Expiry',\n  'hasexpiry': 'Has Expiry',\n  'expires': 'Has Expiry',\n  'perishable': 'Has Expiry',\n  \n  'expiry_date': 'Expiry Date',\n  'expirydate': 'Expiry Date',\n  'expiration': 'Expiry Date',\n  'expiration_date': 'Expiry Date',\n  'expirationdate': 'Expiry Date',\n  'expires_on': 'Expiry Date',\n  'expireson': 'Expiry Date',\n  \n  'active': 'Is Active',\n  'is_active': 'Is Active',\n  'isactive': 'Is Active',\n  'enabled': 'Is Active',\n  'status': 'Is Active'\n};\n\n/**\n * Normalize column headers to match expected format\n */\nfunction normalizeHeaders(headers: string[]): string[] {\n  return headers.map(header => {\n    const normalized = header.toLowerCase().trim();\n    return COLUMN_MAPPINGS[normalized] || header;\n  });\n}\n\n/**\n * Validate that required columns are present\n */\nfunction validateHeaders(headers: string[]): ImportValidationResult {\n  const normalizedHeaders = normalizeHeaders(headers);\n  const errors: string[] = [];\n  const warnings: string[] = [];\n  \n  // Check for required columns\n  const missingRequired = REQUIRED_COLUMNS.filter(\n    required => !normalizedHeaders.includes(required)\n  );\n  \n  if (missingRequired.length > 0) {\n    errors.push(`Missing required columns: ${missingRequired.join(', ')}`);\n  }\n  \n  // Check for duplicate columns\n  const duplicates = normalizedHeaders.filter(\n    (header, index) => normalizedHeaders.indexOf(header) !== index\n  );\n  \n  if (duplicates.length > 0) {\n    warnings.push(`Duplicate columns found: ${duplicates.join(', ')}`);\n  }\n  \n  return {\n    isValid: errors.length === 0,\n    errors,\n    warnings\n  };\n}\n\n/**\n * Parse and validate a single row of product data\n */\nfunction parseProductRow(\n  rowData: { [key: string]: any }, \n  rowIndex: number\n): { product: Partial<Product> | null; errors: string[] } {\n  const errors: string[] = [];\n  const product: Partial<Product> = {};\n  \n  try {\n    // Name (required)\n    const name = String(rowData['Name'] || '').trim();\n    if (!name) {\n      errors.push(`Row ${rowIndex}: Name is required`);\n    } else {\n      product.name = name;\n    }\n    \n    // Category (required)\n    const category = String(rowData['Category'] || '').trim();\n    if (!category) {\n      errors.push(`Row ${rowIndex}: Category is required`);\n    } else {\n      product.category = category;\n    }\n    \n    // Price (required)\n    const priceValue = rowData['Price'];\n    if (priceValue === undefined || priceValue === null || priceValue === '') {\n      errors.push(`Row ${rowIndex}: Price is required`);\n    } else {\n      const price = parseFloat(String(priceValue).replace(/[^\\d.-]/g, ''));\n      if (isNaN(price) || price < 0) {\n        errors.push(`Row ${rowIndex}: Price must be a valid positive number`);\n      } else {\n        product.price = price;\n      }\n    }\n    \n    // Stock Quantity (required)\n    const stockValue = rowData['Stock Quantity'];\n    if (stockValue === undefined || stockValue === null || stockValue === '') {\n      errors.push(`Row ${rowIndex}: Stock Quantity is required`);\n    } else {\n      const stockQuantity = parseInt(String(stockValue));\n      if (isNaN(stockQuantity) || stockQuantity < 0) {\n        errors.push(`Row ${rowIndex}: Stock Quantity must be a valid non-negative integer`);\n      } else {\n        product.stockQuantity = stockQuantity;\n      }\n    }\n    \n    // Description (optional)\n    const description = String(rowData['Description'] || '').trim();\n    product.description = description;\n    \n    // Reorder Level (optional)\n    const reorderValue = rowData['Reorder Level'];\n    if (reorderValue !== undefined && reorderValue !== null && reorderValue !== '') {\n      const reorderLevel = parseInt(String(reorderValue));\n      if (isNaN(reorderLevel) || reorderLevel < 0) {\n        errors.push(`Row ${rowIndex}: Reorder Level must be a valid non-negative integer`);\n      } else {\n        product.reorderLevel = reorderLevel;\n      }\n    } else {\n      product.reorderLevel = 10; // Default value\n    }\n    \n    // Has Expiry (optional)\n    const hasExpiryValue = rowData['Has Expiry'];\n    if (hasExpiryValue !== undefined && hasExpiryValue !== null && hasExpiryValue !== '') {\n      const hasExpiryStr = String(hasExpiryValue).toLowerCase().trim();\n      product.hasExpiry = ['true', '1', 'yes', 'y'].includes(hasExpiryStr);\n    } else {\n      product.hasExpiry = false;\n    }\n    \n    // Expiry Date (optional)\n    const expiryDateValue = rowData['Expiry Date'];\n    if (expiryDateValue && product.hasExpiry) {\n      try {\n        let expiryDate: Date;\n        \n        if (typeof expiryDateValue === 'number') {\n          // Excel date serial number\n          expiryDate = XLSX.SSF.parse_date_code(expiryDateValue);\n        } else {\n          // String date\n          expiryDate = new Date(String(expiryDateValue));\n        }\n        \n        if (isNaN(expiryDate.getTime())) {\n          errors.push(`Row ${rowIndex}: Invalid expiry date format`);\n        } else {\n          product.expiryDate = expiryDate;\n        }\n      } catch (error) {\n        errors.push(`Row ${rowIndex}: Invalid expiry date format`);\n      }\n    }\n    \n    // Is Active (optional)\n    const isActiveValue = rowData['Is Active'];\n    if (isActiveValue !== undefined && isActiveValue !== null && isActiveValue !== '') {\n      const isActiveStr = String(isActiveValue).toLowerCase().trim();\n      product.isActive = !['false', '0', 'no', 'n', 'inactive', 'disabled'].includes(isActiveStr);\n    } else {\n      product.isActive = true; // Default value\n    }\n    \n    return { product: errors.length === 0 ? product : null, errors };\n\n  } catch (error) {\n    errors.push(`Row ${rowIndex}: Error parsing data - ${error instanceof Error ? error.message : 'Unknown error'}`);\n    return { product: null, errors };\n  }\n}\n\n/**\n * Parse Excel file and extract product data\n */\nexport async function parseExcelFile(file: File): Promise<ImportResult> {\n  try {\n    const arrayBuffer = await file.arrayBuffer();\n    const workbook = XLSX.read(arrayBuffer, { type: 'array' });\n\n    // Use the first worksheet\n    const firstSheetName = workbook.SheetNames[0];\n    if (!firstSheetName) {\n      return {\n        success: false,\n        errors: ['No worksheets found in the Excel file']\n      };\n    }\n\n    const worksheet = workbook.Sheets[firstSheetName];\n\n    // Convert to JSON with header row\n    const jsonData = XLSX.utils.sheet_to_json(worksheet, {\n      header: 1,\n      defval: '',\n      blankrows: false\n    }) as any[][];\n\n    if (jsonData.length === 0) {\n      return {\n        success: false,\n        errors: ['The Excel file appears to be empty']\n      };\n    }\n\n    // Extract headers from first row\n    const headers = jsonData[0].map(header => String(header).trim());\n    const normalizedHeaders = normalizeHeaders(headers);\n\n    // Validate headers\n    const headerValidation = validateHeaders(headers);\n    if (!headerValidation.isValid) {\n      return {\n        success: false,\n        errors: headerValidation.errors,\n        warnings: headerValidation.warnings\n      };\n    }\n\n    // Process data rows\n    const products: Partial<Product>[] = [];\n    const errors: string[] = [...headerValidation.errors];\n    const warnings: string[] = [...headerValidation.warnings];\n\n    for (let i = 1; i < jsonData.length; i++) {\n      const row = jsonData[i];\n\n      // Skip empty rows\n      if (row.every(cell => !cell || String(cell).trim() === '')) {\n        continue;\n      }\n\n      // Create row object with normalized headers\n      const rowData: { [key: string]: any } = {};\n      normalizedHeaders.forEach((header, index) => {\n        rowData[header] = row[index];\n      });\n\n      // Parse and validate the row\n      const { product, errors: rowErrors } = parseProductRow(rowData, i + 1);\n\n      if (product) {\n        products.push(product);\n      }\n\n      errors.push(...rowErrors);\n    }\n\n    return {\n      success: errors.length === 0,\n      data: products,\n      errors: errors.length > 0 ? errors : undefined,\n      warnings: warnings.length > 0 ? warnings : undefined\n    };\n\n  } catch (error) {\n    return {\n      success: false,\n      errors: [`Failed to parse Excel file: ${error instanceof Error ? error.message : 'Unknown error'}`]\n    };\n  }\n}\n\n/**\n * Parse CSV file and extract product data\n */\nexport async function parseCSVFile(file: File): Promise<ImportResult> {\n  try {\n    const text = await file.text();\n    const lines = text.split('\\n').filter(line => line.trim());\n\n    if (lines.length === 0) {\n      return {\n        success: false,\n        errors: ['The CSV file appears to be empty']\n      };\n    }\n\n    // Parse CSV headers\n    const headers = lines[0].split(',').map(h => h.replace(/\"/g, '').trim());\n    const normalizedHeaders = normalizeHeaders(headers);\n\n    // Validate headers\n    const headerValidation = validateHeaders(headers);\n    if (!headerValidation.isValid) {\n      return {\n        success: false,\n        errors: headerValidation.errors,\n        warnings: headerValidation.warnings\n      };\n    }\n\n    // Process data rows\n    const products: Partial<Product>[] = [];\n    const errors: string[] = [...headerValidation.errors];\n    const warnings: string[] = [...headerValidation.warnings];\n\n    for (let i = 1; i < lines.length; i++) {\n      const line = lines[i].trim();\n      if (!line) continue;\n\n      // Simple CSV parsing (handles quoted values)\n      const values = line.split(',').map(v => v.replace(/\"/g, '').trim());\n\n      // Create row object with normalized headers\n      const rowData: { [key: string]: any } = {};\n      normalizedHeaders.forEach((header, index) => {\n        rowData[header] = values[index] || '';\n      });\n\n      // Parse and validate the row\n      const { product, errors: rowErrors } = parseProductRow(rowData, i + 1);\n\n      if (product) {\n        products.push(product);\n      }\n\n      errors.push(...rowErrors);\n    }\n\n    return {\n      success: errors.length === 0,\n      data: products,\n      errors: errors.length > 0 ? errors : undefined,\n      warnings: warnings.length > 0 ? warnings : undefined\n    };\n\n  } catch (error) {\n    return {\n      success: false,\n      errors: [`Failed to parse CSV file: ${error instanceof Error ? error.message : 'Unknown error'}`]\n    };\n  }\n}\n\n/**\n * Main function to parse import file (Excel or CSV)\n */\nexport async function parseImportFile(file: File): Promise<ImportResult> {\n  const fileName = file.name.toLowerCase();\n\n  if (fileName.endsWith('.xlsx') || fileName.endsWith('.xls')) {\n    return parseExcelFile(file);\n  } else if (fileName.endsWith('.csv')) {\n    return parseCSVFile(file);\n  } else {\n    return {\n      success: false,\n      errors: ['Unsupported file format. Please use Excel (.xlsx, .xls) or CSV (.csv) files.']\n    };\n  }\n}\n\n/**\n * Generate a sample Excel template for product import\n */\nexport function generateImportTemplate(): void {\n  const headers = [\n    'Name',\n    'Description',\n    'Category',\n    'Price',\n    'Stock Quantity',\n    'Reorder Level',\n    'Has Expiry',\n    'Expiry Date',\n    'Is Active'\n  ];\n\n  const sampleData = [\n    [\n      'A4 Paper (Ream)',\n      '500 sheets of A4 paper',\n      'Paper',\n      450,\n      25,\n      10,\n      'No',\n      '',\n      'Yes'\n    ],\n    [\n      'Blue Pen',\n      'Ballpoint pen - blue ink',\n      'Writing',\n      20,\n      50,\n      20,\n      'No',\n      '',\n      'Yes'\n    ],\n    [\n      'Printer Ink Cartridge',\n      'Black ink cartridge for HP printers',\n      'Printer',\n      1200,\n      5,\n      3,\n      'Yes',\n      '2025-12-31',\n      'Yes'\n    ]\n  ];\n\n  // Create workbook and worksheet\n  const workbook = XLSX.utils.book_new();\n  const worksheet = XLSX.utils.aoa_to_sheet([headers, ...sampleData]);\n\n  // Add worksheet to workbook\n  XLSX.utils.book_append_sheet(workbook, worksheet, 'Products');\n\n  // Generate and download file\n  XLSX.writeFile(workbook, `product-import-template-${new Date().toISOString().split('T')[0]}.xlsx`);\n}\n"], "mappings": "AAAA,MAAO,GAAK,CAAAA,IAAI,KAAM,MAAM,CAgB5B;AACA,KAAM,CAAAC,gBAAgB,CAAG,CAAC,MAAM,CAAE,UAAU,CAAE,OAAO,CAAE,gBAAgB,CAAC,CAExE;AACA,KAAM,CAAAC,gBAAgB,CAAG,CACvB,aAAa,CAAE,EAAE,CACjB,eAAe,CAAE,EAAE,CACnB,YAAY,CAAE,KAAK,CACnB,aAAa,CAAE,IAAI,CACnB,WAAW,CAAE,IACf,CAAC,CAED;AACA,KAAM,CAAAC,eAA0C,CAAG,CACjD,cAAc,CAAE,MAAM,CACtB,cAAc,CAAE,MAAM,CACtB,aAAa,CAAE,MAAM,CACrB,WAAW,CAAE,MAAM,CACnB,WAAW,CAAE,MAAM,CACnB,UAAU,CAAE,MAAM,CAClB,OAAO,CAAE,MAAM,CAEf,kBAAkB,CAAE,UAAU,CAC9B,kBAAkB,CAAE,UAAU,CAC9B,iBAAiB,CAAE,UAAU,CAC7B,eAAe,CAAE,UAAU,CAC3B,eAAe,CAAE,UAAU,CAC3B,cAAc,CAAE,UAAU,CAC1B,KAAK,CAAE,UAAU,CAEjB,YAAY,CAAE,OAAO,CACrB,YAAY,CAAE,OAAO,CACrB,WAAW,CAAE,OAAO,CACpB,MAAM,CAAE,OAAO,CACf,QAAQ,CAAE,OAAO,CAEjB,OAAO,CAAE,gBAAgB,CACzB,UAAU,CAAE,gBAAgB,CAC5B,KAAK,CAAE,gBAAgB,CACvB,WAAW,CAAE,gBAAgB,CAC7B,gBAAgB,CAAE,gBAAgB,CAClC,eAAe,CAAE,gBAAgB,CAEjC,MAAM,CAAE,aAAa,CACrB,qBAAqB,CAAE,aAAa,CACpC,qBAAqB,CAAE,aAAa,CACpC,oBAAoB,CAAE,aAAa,CACnC,kBAAkB,CAAE,aAAa,CACjC,kBAAkB,CAAE,aAAa,CACjC,iBAAiB,CAAE,aAAa,CAEhC,SAAS,CAAE,eAAe,CAC1B,eAAe,CAAE,eAAe,CAChC,cAAc,CAAE,eAAe,CAC/B,WAAW,CAAE,eAAe,CAC5B,WAAW,CAAE,eAAe,CAC5B,UAAU,CAAE,eAAe,CAC3B,SAAS,CAAE,eAAe,CAE1B,QAAQ,CAAE,YAAY,CACtB,YAAY,CAAE,YAAY,CAC1B,WAAW,CAAE,YAAY,CACzB,SAAS,CAAE,YAAY,CACvB,YAAY,CAAE,YAAY,CAE1B,aAAa,CAAE,aAAa,CAC5B,YAAY,CAAE,aAAa,CAC3B,YAAY,CAAE,aAAa,CAC3B,iBAAiB,CAAE,aAAa,CAChC,gBAAgB,CAAE,aAAa,CAC/B,YAAY,CAAE,aAAa,CAC3B,WAAW,CAAE,aAAa,CAE1B,QAAQ,CAAE,WAAW,CACrB,WAAW,CAAE,WAAW,CACxB,UAAU,CAAE,WAAW,CACvB,SAAS,CAAE,WAAW,CACtB,QAAQ,CAAE,WACZ,CAAC,CAED;AACA;AACA,GACA,QAAS,CAAAC,gBAAgBA,CAACC,OAAiB,CAAY,CACrD,MAAO,CAAAA,OAAO,CAACC,GAAG,CAACC,MAAM,EAAI,CAC3B,KAAM,CAAAC,UAAU,CAAGD,MAAM,CAACE,WAAW,CAAC,CAAC,CAACC,IAAI,CAAC,CAAC,CAC9C,MAAO,CAAAP,eAAe,CAACK,UAAU,CAAC,EAAID,MAAM,CAC9C,CAAC,CAAC,CACJ,CAEA;AACA;AACA,GACA,QAAS,CAAAI,eAAeA,CAACN,OAAiB,CAA0B,CAClE,KAAM,CAAAO,iBAAiB,CAAGR,gBAAgB,CAACC,OAAO,CAAC,CACnD,KAAM,CAAAQ,MAAgB,CAAG,EAAE,CAC3B,KAAM,CAAAC,QAAkB,CAAG,EAAE,CAE7B;AACA,KAAM,CAAAC,eAAe,CAAGd,gBAAgB,CAACe,MAAM,CAC7CC,QAAQ,EAAI,CAACL,iBAAiB,CAACM,QAAQ,CAACD,QAAQ,CAClD,CAAC,CAED,GAAIF,eAAe,CAACI,MAAM,CAAG,CAAC,CAAE,CAC9BN,MAAM,CAACO,IAAI,8BAAAC,MAAA,CAA8BN,eAAe,CAACO,IAAI,CAAC,IAAI,CAAC,CAAE,CAAC,CACxE,CAEA;AACA,KAAM,CAAAC,UAAU,CAAGX,iBAAiB,CAACI,MAAM,CACzC,CAACT,MAAM,CAAEiB,KAAK,GAAKZ,iBAAiB,CAACa,OAAO,CAAClB,MAAM,CAAC,GAAKiB,KAC3D,CAAC,CAED,GAAID,UAAU,CAACJ,MAAM,CAAG,CAAC,CAAE,CACzBL,QAAQ,CAACM,IAAI,6BAAAC,MAAA,CAA6BE,UAAU,CAACD,IAAI,CAAC,IAAI,CAAC,CAAE,CAAC,CACpE,CAEA,MAAO,CACLI,OAAO,CAAEb,MAAM,CAACM,MAAM,GAAK,CAAC,CAC5BN,MAAM,CACNC,QACF,CAAC,CACH,CAEA;AACA;AACA,GACA,QAAS,CAAAa,eAAeA,CACtBC,OAA+B,CAC/BC,QAAgB,CACwC,CACxD,KAAM,CAAAhB,MAAgB,CAAG,EAAE,CAC3B,KAAM,CAAAiB,OAAyB,CAAG,CAAC,CAAC,CAEpC,GAAI,CACF;AACA,KAAM,CAAAC,IAAI,CAAGC,MAAM,CAACJ,OAAO,CAAC,MAAM,CAAC,EAAI,EAAE,CAAC,CAAClB,IAAI,CAAC,CAAC,CACjD,GAAI,CAACqB,IAAI,CAAE,CACTlB,MAAM,CAACO,IAAI,QAAAC,MAAA,CAAQQ,QAAQ,sBAAoB,CAAC,CAClD,CAAC,IAAM,CACLC,OAAO,CAACC,IAAI,CAAGA,IAAI,CACrB,CAEA;AACA,KAAM,CAAAE,QAAQ,CAAGD,MAAM,CAACJ,OAAO,CAAC,UAAU,CAAC,EAAI,EAAE,CAAC,CAAClB,IAAI,CAAC,CAAC,CACzD,GAAI,CAACuB,QAAQ,CAAE,CACbpB,MAAM,CAACO,IAAI,QAAAC,MAAA,CAAQQ,QAAQ,0BAAwB,CAAC,CACtD,CAAC,IAAM,CACLC,OAAO,CAACG,QAAQ,CAAGA,QAAQ,CAC7B,CAEA;AACA,KAAM,CAAAC,UAAU,CAAGN,OAAO,CAAC,OAAO,CAAC,CACnC,GAAIM,UAAU,GAAKC,SAAS,EAAID,UAAU,GAAK,IAAI,EAAIA,UAAU,GAAK,EAAE,CAAE,CACxErB,MAAM,CAACO,IAAI,QAAAC,MAAA,CAAQQ,QAAQ,uBAAqB,CAAC,CACnD,CAAC,IAAM,CACL,KAAM,CAAAO,KAAK,CAAGC,UAAU,CAACL,MAAM,CAACE,UAAU,CAAC,CAACI,OAAO,CAAC,UAAU,CAAE,EAAE,CAAC,CAAC,CACpE,GAAIC,KAAK,CAACH,KAAK,CAAC,EAAIA,KAAK,CAAG,CAAC,CAAE,CAC7BvB,MAAM,CAACO,IAAI,QAAAC,MAAA,CAAQQ,QAAQ,2CAAyC,CAAC,CACvE,CAAC,IAAM,CACLC,OAAO,CAACM,KAAK,CAAGA,KAAK,CACvB,CACF,CAEA;AACA,KAAM,CAAAI,UAAU,CAAGZ,OAAO,CAAC,gBAAgB,CAAC,CAC5C,GAAIY,UAAU,GAAKL,SAAS,EAAIK,UAAU,GAAK,IAAI,EAAIA,UAAU,GAAK,EAAE,CAAE,CACxE3B,MAAM,CAACO,IAAI,QAAAC,MAAA,CAAQQ,QAAQ,gCAA8B,CAAC,CAC5D,CAAC,IAAM,CACL,KAAM,CAAAY,aAAa,CAAGC,QAAQ,CAACV,MAAM,CAACQ,UAAU,CAAC,CAAC,CAClD,GAAID,KAAK,CAACE,aAAa,CAAC,EAAIA,aAAa,CAAG,CAAC,CAAE,CAC7C5B,MAAM,CAACO,IAAI,QAAAC,MAAA,CAAQQ,QAAQ,yDAAuD,CAAC,CACrF,CAAC,IAAM,CACLC,OAAO,CAACW,aAAa,CAAGA,aAAa,CACvC,CACF,CAEA;AACA,KAAM,CAAAE,WAAW,CAAGX,MAAM,CAACJ,OAAO,CAAC,aAAa,CAAC,EAAI,EAAE,CAAC,CAAClB,IAAI,CAAC,CAAC,CAC/DoB,OAAO,CAACa,WAAW,CAAGA,WAAW,CAEjC;AACA,KAAM,CAAAC,YAAY,CAAGhB,OAAO,CAAC,eAAe,CAAC,CAC7C,GAAIgB,YAAY,GAAKT,SAAS,EAAIS,YAAY,GAAK,IAAI,EAAIA,YAAY,GAAK,EAAE,CAAE,CAC9E,KAAM,CAAAC,YAAY,CAAGH,QAAQ,CAACV,MAAM,CAACY,YAAY,CAAC,CAAC,CACnD,GAAIL,KAAK,CAACM,YAAY,CAAC,EAAIA,YAAY,CAAG,CAAC,CAAE,CAC3ChC,MAAM,CAACO,IAAI,QAAAC,MAAA,CAAQQ,QAAQ,wDAAsD,CAAC,CACpF,CAAC,IAAM,CACLC,OAAO,CAACe,YAAY,CAAGA,YAAY,CACrC,CACF,CAAC,IAAM,CACLf,OAAO,CAACe,YAAY,CAAG,EAAE,CAAE;AAC7B,CAEA;AACA,KAAM,CAAAC,cAAc,CAAGlB,OAAO,CAAC,YAAY,CAAC,CAC5C,GAAIkB,cAAc,GAAKX,SAAS,EAAIW,cAAc,GAAK,IAAI,EAAIA,cAAc,GAAK,EAAE,CAAE,CACpF,KAAM,CAAAC,YAAY,CAAGf,MAAM,CAACc,cAAc,CAAC,CAACrC,WAAW,CAAC,CAAC,CAACC,IAAI,CAAC,CAAC,CAChEoB,OAAO,CAACkB,SAAS,CAAG,CAAC,MAAM,CAAE,GAAG,CAAE,KAAK,CAAE,GAAG,CAAC,CAAC9B,QAAQ,CAAC6B,YAAY,CAAC,CACtE,CAAC,IAAM,CACLjB,OAAO,CAACkB,SAAS,CAAG,KAAK,CAC3B,CAEA;AACA,KAAM,CAAAC,eAAe,CAAGrB,OAAO,CAAC,aAAa,CAAC,CAC9C,GAAIqB,eAAe,EAAInB,OAAO,CAACkB,SAAS,CAAE,CACxC,GAAI,CACF,GAAI,CAAAE,UAAgB,CAEpB,GAAI,MAAO,CAAAD,eAAe,GAAK,QAAQ,CAAE,CACvC;AACAC,UAAU,CAAGlD,IAAI,CAACmD,GAAG,CAACC,eAAe,CAACH,eAAe,CAAC,CACxD,CAAC,IAAM,CACL;AACAC,UAAU,CAAG,GAAI,CAAAG,IAAI,CAACrB,MAAM,CAACiB,eAAe,CAAC,CAAC,CAChD,CAEA,GAAIV,KAAK,CAACW,UAAU,CAACI,OAAO,CAAC,CAAC,CAAC,CAAE,CAC/BzC,MAAM,CAACO,IAAI,QAAAC,MAAA,CAAQQ,QAAQ,gCAA8B,CAAC,CAC5D,CAAC,IAAM,CACLC,OAAO,CAACoB,UAAU,CAAGA,UAAU,CACjC,CACF,CAAE,MAAOK,KAAK,CAAE,CACd1C,MAAM,CAACO,IAAI,QAAAC,MAAA,CAAQQ,QAAQ,gCAA8B,CAAC,CAC5D,CACF,CAEA;AACA,KAAM,CAAA2B,aAAa,CAAG5B,OAAO,CAAC,WAAW,CAAC,CAC1C,GAAI4B,aAAa,GAAKrB,SAAS,EAAIqB,aAAa,GAAK,IAAI,EAAIA,aAAa,GAAK,EAAE,CAAE,CACjF,KAAM,CAAAC,WAAW,CAAGzB,MAAM,CAACwB,aAAa,CAAC,CAAC/C,WAAW,CAAC,CAAC,CAACC,IAAI,CAAC,CAAC,CAC9DoB,OAAO,CAAC4B,QAAQ,CAAG,CAAC,CAAC,OAAO,CAAE,GAAG,CAAE,IAAI,CAAE,GAAG,CAAE,UAAU,CAAE,UAAU,CAAC,CAACxC,QAAQ,CAACuC,WAAW,CAAC,CAC7F,CAAC,IAAM,CACL3B,OAAO,CAAC4B,QAAQ,CAAG,IAAI,CAAE;AAC3B,CAEA,MAAO,CAAE5B,OAAO,CAAEjB,MAAM,CAACM,MAAM,GAAK,CAAC,CAAGW,OAAO,CAAG,IAAI,CAAEjB,MAAO,CAAC,CAElE,CAAE,MAAO0C,KAAK,CAAE,CACd1C,MAAM,CAACO,IAAI,QAAAC,MAAA,CAAQQ,QAAQ,4BAAAR,MAAA,CAA0BkC,KAAK,WAAY,CAAAI,KAAK,CAAGJ,KAAK,CAACK,OAAO,CAAG,eAAe,CAAE,CAAC,CAChH,MAAO,CAAE9B,OAAO,CAAE,IAAI,CAAEjB,MAAO,CAAC,CAClC,CACF,CAEA;AACA;AACA,GACA,MAAO,eAAe,CAAAgD,cAAcA,CAACC,IAAU,CAAyB,CACtE,GAAI,CACF,KAAM,CAAAC,WAAW,CAAG,KAAM,CAAAD,IAAI,CAACC,WAAW,CAAC,CAAC,CAC5C,KAAM,CAAAC,QAAQ,CAAGhE,IAAI,CAACiE,IAAI,CAACF,WAAW,CAAE,CAAEG,IAAI,CAAE,OAAQ,CAAC,CAAC,CAE1D;AACA,KAAM,CAAAC,cAAc,CAAGH,QAAQ,CAACI,UAAU,CAAC,CAAC,CAAC,CAC7C,GAAI,CAACD,cAAc,CAAE,CACnB,MAAO,CACLE,OAAO,CAAE,KAAK,CACdxD,MAAM,CAAE,CAAC,uCAAuC,CAClD,CAAC,CACH,CAEA,KAAM,CAAAyD,SAAS,CAAGN,QAAQ,CAACO,MAAM,CAACJ,cAAc,CAAC,CAEjD;AACA,KAAM,CAAAK,QAAQ,CAAGxE,IAAI,CAACyE,KAAK,CAACC,aAAa,CAACJ,SAAS,CAAE,CACnD/D,MAAM,CAAE,CAAC,CACToE,MAAM,CAAE,EAAE,CACVC,SAAS,CAAE,KACb,CAAC,CAAY,CAEb,GAAIJ,QAAQ,CAACrD,MAAM,GAAK,CAAC,CAAE,CACzB,MAAO,CACLkD,OAAO,CAAE,KAAK,CACdxD,MAAM,CAAE,CAAC,oCAAoC,CAC/C,CAAC,CACH,CAEA;AACA,KAAM,CAAAR,OAAO,CAAGmE,QAAQ,CAAC,CAAC,CAAC,CAAClE,GAAG,CAACC,MAAM,EAAIyB,MAAM,CAACzB,MAAM,CAAC,CAACG,IAAI,CAAC,CAAC,CAAC,CAChE,KAAM,CAAAE,iBAAiB,CAAGR,gBAAgB,CAACC,OAAO,CAAC,CAEnD;AACA,KAAM,CAAAwE,gBAAgB,CAAGlE,eAAe,CAACN,OAAO,CAAC,CACjD,GAAI,CAACwE,gBAAgB,CAACnD,OAAO,CAAE,CAC7B,MAAO,CACL2C,OAAO,CAAE,KAAK,CACdxD,MAAM,CAAEgE,gBAAgB,CAAChE,MAAM,CAC/BC,QAAQ,CAAE+D,gBAAgB,CAAC/D,QAC7B,CAAC,CACH,CAEA;AACA,KAAM,CAAAgE,QAA4B,CAAG,EAAE,CACvC,KAAM,CAAAjE,MAAgB,CAAG,CAAC,GAAGgE,gBAAgB,CAAChE,MAAM,CAAC,CACrD,KAAM,CAAAC,QAAkB,CAAG,CAAC,GAAG+D,gBAAgB,CAAC/D,QAAQ,CAAC,CAEzD,IAAK,GAAI,CAAAiE,CAAC,CAAG,CAAC,CAAEA,CAAC,CAAGP,QAAQ,CAACrD,MAAM,CAAE4D,CAAC,EAAE,CAAE,CACxC,KAAM,CAAAC,GAAG,CAAGR,QAAQ,CAACO,CAAC,CAAC,CAEvB;AACA,GAAIC,GAAG,CAACC,KAAK,CAACC,IAAI,EAAI,CAACA,IAAI,EAAIlD,MAAM,CAACkD,IAAI,CAAC,CAACxE,IAAI,CAAC,CAAC,GAAK,EAAE,CAAC,CAAE,CAC1D,SACF,CAEA;AACA,KAAM,CAAAkB,OAA+B,CAAG,CAAC,CAAC,CAC1ChB,iBAAiB,CAACuE,OAAO,CAAC,CAAC5E,MAAM,CAAEiB,KAAK,GAAK,CAC3CI,OAAO,CAACrB,MAAM,CAAC,CAAGyE,GAAG,CAACxD,KAAK,CAAC,CAC9B,CAAC,CAAC,CAEF;AACA,KAAM,CAAEM,OAAO,CAAEjB,MAAM,CAAEuE,SAAU,CAAC,CAAGzD,eAAe,CAACC,OAAO,CAAEmD,CAAC,CAAG,CAAC,CAAC,CAEtE,GAAIjD,OAAO,CAAE,CACXgD,QAAQ,CAAC1D,IAAI,CAACU,OAAO,CAAC,CACxB,CAEAjB,MAAM,CAACO,IAAI,CAAC,GAAGgE,SAAS,CAAC,CAC3B,CAEA,MAAO,CACLf,OAAO,CAAExD,MAAM,CAACM,MAAM,GAAK,CAAC,CAC5BkE,IAAI,CAAEP,QAAQ,CACdjE,MAAM,CAAEA,MAAM,CAACM,MAAM,CAAG,CAAC,CAAGN,MAAM,CAAGsB,SAAS,CAC9CrB,QAAQ,CAAEA,QAAQ,CAACK,MAAM,CAAG,CAAC,CAAGL,QAAQ,CAAGqB,SAC7C,CAAC,CAEH,CAAE,MAAOoB,KAAK,CAAE,CACd,MAAO,CACLc,OAAO,CAAE,KAAK,CACdxD,MAAM,CAAE,gCAAAQ,MAAA,CAAgCkC,KAAK,WAAY,CAAAI,KAAK,CAAGJ,KAAK,CAACK,OAAO,CAAG,eAAe,EAClG,CAAC,CACH,CACF,CAEA;AACA;AACA,GACA,MAAO,eAAe,CAAA0B,YAAYA,CAACxB,IAAU,CAAyB,CACpE,GAAI,CACF,KAAM,CAAAyB,IAAI,CAAG,KAAM,CAAAzB,IAAI,CAACyB,IAAI,CAAC,CAAC,CAC9B,KAAM,CAAAC,KAAK,CAAGD,IAAI,CAACE,KAAK,CAAC,IAAI,CAAC,CAACzE,MAAM,CAAC0E,IAAI,EAAIA,IAAI,CAAChF,IAAI,CAAC,CAAC,CAAC,CAE1D,GAAI8E,KAAK,CAACrE,MAAM,GAAK,CAAC,CAAE,CACtB,MAAO,CACLkD,OAAO,CAAE,KAAK,CACdxD,MAAM,CAAE,CAAC,kCAAkC,CAC7C,CAAC,CACH,CAEA;AACA,KAAM,CAAAR,OAAO,CAAGmF,KAAK,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAACnF,GAAG,CAACqF,CAAC,EAAIA,CAAC,CAACrD,OAAO,CAAC,IAAI,CAAE,EAAE,CAAC,CAAC5B,IAAI,CAAC,CAAC,CAAC,CACxE,KAAM,CAAAE,iBAAiB,CAAGR,gBAAgB,CAACC,OAAO,CAAC,CAEnD;AACA,KAAM,CAAAwE,gBAAgB,CAAGlE,eAAe,CAACN,OAAO,CAAC,CACjD,GAAI,CAACwE,gBAAgB,CAACnD,OAAO,CAAE,CAC7B,MAAO,CACL2C,OAAO,CAAE,KAAK,CACdxD,MAAM,CAAEgE,gBAAgB,CAAChE,MAAM,CAC/BC,QAAQ,CAAE+D,gBAAgB,CAAC/D,QAC7B,CAAC,CACH,CAEA;AACA,KAAM,CAAAgE,QAA4B,CAAG,EAAE,CACvC,KAAM,CAAAjE,MAAgB,CAAG,CAAC,GAAGgE,gBAAgB,CAAChE,MAAM,CAAC,CACrD,KAAM,CAAAC,QAAkB,CAAG,CAAC,GAAG+D,gBAAgB,CAAC/D,QAAQ,CAAC,CAEzD,IAAK,GAAI,CAAAiE,CAAC,CAAG,CAAC,CAAEA,CAAC,CAAGS,KAAK,CAACrE,MAAM,CAAE4D,CAAC,EAAE,CAAE,CACrC,KAAM,CAAAW,IAAI,CAAGF,KAAK,CAACT,CAAC,CAAC,CAACrE,IAAI,CAAC,CAAC,CAC5B,GAAI,CAACgF,IAAI,CAAE,SAEX;AACA,KAAM,CAAAE,MAAM,CAAGF,IAAI,CAACD,KAAK,CAAC,GAAG,CAAC,CAACnF,GAAG,CAACuF,CAAC,EAAIA,CAAC,CAACvD,OAAO,CAAC,IAAI,CAAE,EAAE,CAAC,CAAC5B,IAAI,CAAC,CAAC,CAAC,CAEnE;AACA,KAAM,CAAAkB,OAA+B,CAAG,CAAC,CAAC,CAC1ChB,iBAAiB,CAACuE,OAAO,CAAC,CAAC5E,MAAM,CAAEiB,KAAK,GAAK,CAC3CI,OAAO,CAACrB,MAAM,CAAC,CAAGqF,MAAM,CAACpE,KAAK,CAAC,EAAI,EAAE,CACvC,CAAC,CAAC,CAEF;AACA,KAAM,CAAEM,OAAO,CAAEjB,MAAM,CAAEuE,SAAU,CAAC,CAAGzD,eAAe,CAACC,OAAO,CAAEmD,CAAC,CAAG,CAAC,CAAC,CAEtE,GAAIjD,OAAO,CAAE,CACXgD,QAAQ,CAAC1D,IAAI,CAACU,OAAO,CAAC,CACxB,CAEAjB,MAAM,CAACO,IAAI,CAAC,GAAGgE,SAAS,CAAC,CAC3B,CAEA,MAAO,CACLf,OAAO,CAAExD,MAAM,CAACM,MAAM,GAAK,CAAC,CAC5BkE,IAAI,CAAEP,QAAQ,CACdjE,MAAM,CAAEA,MAAM,CAACM,MAAM,CAAG,CAAC,CAAGN,MAAM,CAAGsB,SAAS,CAC9CrB,QAAQ,CAAEA,QAAQ,CAACK,MAAM,CAAG,CAAC,CAAGL,QAAQ,CAAGqB,SAC7C,CAAC,CAEH,CAAE,MAAOoB,KAAK,CAAE,CACd,MAAO,CACLc,OAAO,CAAE,KAAK,CACdxD,MAAM,CAAE,8BAAAQ,MAAA,CAA8BkC,KAAK,WAAY,CAAAI,KAAK,CAAGJ,KAAK,CAACK,OAAO,CAAG,eAAe,EAChG,CAAC,CACH,CACF,CAEA;AACA;AACA,GACA,MAAO,eAAe,CAAAkC,eAAeA,CAAChC,IAAU,CAAyB,CACvE,KAAM,CAAAiC,QAAQ,CAAGjC,IAAI,CAAC/B,IAAI,CAACtB,WAAW,CAAC,CAAC,CAExC,GAAIsF,QAAQ,CAACC,QAAQ,CAAC,OAAO,CAAC,EAAID,QAAQ,CAACC,QAAQ,CAAC,MAAM,CAAC,CAAE,CAC3D,MAAO,CAAAnC,cAAc,CAACC,IAAI,CAAC,CAC7B,CAAC,IAAM,IAAIiC,QAAQ,CAACC,QAAQ,CAAC,MAAM,CAAC,CAAE,CACpC,MAAO,CAAAV,YAAY,CAACxB,IAAI,CAAC,CAC3B,CAAC,IAAM,CACL,MAAO,CACLO,OAAO,CAAE,KAAK,CACdxD,MAAM,CAAE,CAAC,8EAA8E,CACzF,CAAC,CACH,CACF,CAEA;AACA;AACA,GACA,MAAO,SAAS,CAAAoF,sBAAsBA,CAAA,CAAS,CAC7C,KAAM,CAAA5F,OAAO,CAAG,CACd,MAAM,CACN,aAAa,CACb,UAAU,CACV,OAAO,CACP,gBAAgB,CAChB,eAAe,CACf,YAAY,CACZ,aAAa,CACb,WAAW,CACZ,CAED,KAAM,CAAA6F,UAAU,CAAG,CACjB,CACE,iBAAiB,CACjB,wBAAwB,CACxB,OAAO,CACP,GAAG,CACH,EAAE,CACF,EAAE,CACF,IAAI,CACJ,EAAE,CACF,KAAK,CACN,CACD,CACE,UAAU,CACV,0BAA0B,CAC1B,SAAS,CACT,EAAE,CACF,EAAE,CACF,EAAE,CACF,IAAI,CACJ,EAAE,CACF,KAAK,CACN,CACD,CACE,uBAAuB,CACvB,qCAAqC,CACrC,SAAS,CACT,IAAI,CACJ,CAAC,CACD,CAAC,CACD,KAAK,CACL,YAAY,CACZ,KAAK,CACN,CACF,CAED;AACA,KAAM,CAAAlC,QAAQ,CAAGhE,IAAI,CAACyE,KAAK,CAAC0B,QAAQ,CAAC,CAAC,CACtC,KAAM,CAAA7B,SAAS,CAAGtE,IAAI,CAACyE,KAAK,CAAC2B,YAAY,CAAC,CAAC/F,OAAO,CAAE,GAAG6F,UAAU,CAAC,CAAC,CAEnE;AACAlG,IAAI,CAACyE,KAAK,CAAC4B,iBAAiB,CAACrC,QAAQ,CAAEM,SAAS,CAAE,UAAU,CAAC,CAE7D;AACAtE,IAAI,CAACsG,SAAS,CAACtC,QAAQ,4BAAA3C,MAAA,CAA6B,GAAI,CAAAgC,IAAI,CAAC,CAAC,CAACkD,WAAW,CAAC,CAAC,CAACd,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,SAAO,CAAC,CACpG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}