{"ast": null, "code": "import _objectSpread from\"E:/FX/Cyber POS/cyber-pos-system/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import React,{useState,useEffect}from'react';import{X,Package,DollarSign,Calendar,AlertTriangle}from'lucide-react';import{useProducts}from'../../hooks/useProducts';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const ProductModal=_ref=>{let{product,onClose}=_ref;const{createProduct,updateProduct,getProductCategories}=useProducts();const[loading,setLoading]=useState(false);const[error,setError]=useState('');const[formData,setFormData]=useState({name:'',description:'',price:0,category:'',stockQuantity:0,reorderLevel:0,hasExpiry:false,expiryDate:'',isActive:true});const isEditing=!!product;const categories=getProductCategories();const predefinedCategories=['Paper','Writing','Office','Printer','Electronics','Stationery','Other'];const allCategories=[...new Set([...predefinedCategories,...categories])].sort();useEffect(()=>{if(product){setFormData({name:product.name,description:product.description,price:product.price,category:product.category,stockQuantity:product.stockQuantity,reorderLevel:product.reorderLevel,hasExpiry:product.hasExpiry,expiryDate:product.expiryDate?product.expiryDate.toISOString().split('T')[0]:'',isActive:product.isActive});}},[product]);const handleSubmit=async e=>{e.preventDefault();setError('');setLoading(true);try{const productData=_objectSpread({},formData);// Only include expiryDate if hasExpiry is true and expiryDate is provided\nif(formData.hasExpiry&&formData.expiryDate){productData.expiryDate=new Date(formData.expiryDate);}// Remove expiryDate field entirely if not needed to avoid undefined values\nelse{delete productData.expiryDate;}if(isEditing){await updateProduct(product.id,productData);}else{await createProduct(productData);}onClose();}catch(error){setError(error.message||\"Failed to \".concat(isEditing?'update':'create',\" product\"));}finally{setLoading(false);}};const validateForm=()=>{if(!formData.name.trim())return false;if(!formData.category.trim())return false;if(formData.price<0)return false;if(formData.stockQuantity<0)return false;if(formData.reorderLevel<0)return false;if(formData.hasExpiry&&!formData.expiryDate)return false;return true;};return/*#__PURE__*/_jsx(\"div\",{className:\"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50\",children:/*#__PURE__*/_jsx(\"div\",{className:\"relative top-20 mx-auto p-5 border w-full max-w-2xl shadow-lg rounded-md bg-white\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"mt-3\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between mb-6\",children:[/*#__PURE__*/_jsxs(\"h3\",{className:\"text-lg font-medium text-gray-900 flex items-center\",children:[/*#__PURE__*/_jsx(Package,{className:\"h-5 w-5 mr-2\"}),isEditing?'Edit Product':'Add New Product']}),/*#__PURE__*/_jsx(\"button\",{onClick:onClose,className:\"text-gray-400 hover:text-gray-600\",children:/*#__PURE__*/_jsx(X,{className:\"h-5 w-5\"})})]}),error&&/*#__PURE__*/_jsx(\"div\",{className:\"mb-4 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded\",children:error}),/*#__PURE__*/_jsxs(\"form\",{onSubmit:handleSubmit,className:\"space-y-6\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-1 md:grid-cols-2 gap-4\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700 mb-1\",children:\"Product Name *\"}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",required:true,value:formData.name,onChange:e=>setFormData(_objectSpread(_objectSpread({},formData),{},{name:e.target.value})),className:\"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-primary-500 focus:border-primary-500\",placeholder:\"e.g., A4 Paper\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700 mb-1\",children:\"Category *\"}),/*#__PURE__*/_jsxs(\"select\",{required:true,value:formData.category,onChange:e=>setFormData(_objectSpread(_objectSpread({},formData),{},{category:e.target.value})),className:\"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-primary-500 focus:border-primary-500\",children:[/*#__PURE__*/_jsx(\"option\",{value:\"\",children:\"Select Category\"}),allCategories.map(category=>/*#__PURE__*/_jsx(\"option\",{value:category,children:category},category))]})]})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700 mb-1\",children:\"Description\"}),/*#__PURE__*/_jsx(\"textarea\",{value:formData.description,onChange:e=>setFormData(_objectSpread(_objectSpread({},formData),{},{description:e.target.value})),rows:3,className:\"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-primary-500 focus:border-primary-500\",placeholder:\"Brief description of the product\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"bg-gray-50 p-4 rounded-lg\",children:[/*#__PURE__*/_jsxs(\"h4\",{className:\"text-sm font-medium text-gray-900 mb-3 flex items-center\",children:[/*#__PURE__*/_jsx(DollarSign,{className:\"h-4 w-4 mr-2\"}),\"Pricing Information\"]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700 mb-1\",children:\"Unit Price (KSh) *\"}),/*#__PURE__*/_jsx(\"input\",{type:\"number\",required:true,min:\"0\",step:\"0.01\",value:formData.price,onChange:e=>setFormData(_objectSpread(_objectSpread({},formData),{},{price:parseFloat(e.target.value)||0})),className:\"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-primary-500 focus:border-primary-500\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"bg-blue-50 p-4 rounded-lg\",children:[/*#__PURE__*/_jsxs(\"h4\",{className:\"text-sm font-medium text-gray-900 mb-3 flex items-center\",children:[/*#__PURE__*/_jsx(Package,{className:\"h-4 w-4 mr-2\"}),\"Inventory Management\"]}),/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-1 md:grid-cols-2 gap-4\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700 mb-1\",children:\"Initial Stock Quantity *\"}),/*#__PURE__*/_jsx(\"input\",{type:\"number\",required:true,min:\"0\",value:formData.stockQuantity,onChange:e=>setFormData(_objectSpread(_objectSpread({},formData),{},{stockQuantity:parseInt(e.target.value)||0})),className:\"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-primary-500 focus:border-primary-500\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700 mb-1\",children:\"Reorder Level *\"}),/*#__PURE__*/_jsx(\"input\",{type:\"number\",required:true,min:\"0\",value:formData.reorderLevel,onChange:e=>setFormData(_objectSpread(_objectSpread({},formData),{},{reorderLevel:parseInt(e.target.value)||0})),className:\"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-primary-500 focus:border-primary-500\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-xs text-gray-500 mt-1\",children:\"Alert when stock falls below this level\"})]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"bg-orange-50 p-4 rounded-lg\",children:[/*#__PURE__*/_jsxs(\"h4\",{className:\"text-sm font-medium text-gray-900 mb-3 flex items-center\",children:[/*#__PURE__*/_jsx(Calendar,{className:\"h-4 w-4 mr-2\"}),\"Expiry Management\"]}),/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-3\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsx(\"input\",{type:\"checkbox\",id:\"hasExpiry\",checked:formData.hasExpiry,onChange:e=>setFormData(_objectSpread(_objectSpread({},formData),{},{hasExpiry:e.target.checked,expiryDate:e.target.checked?formData.expiryDate:''})),className:\"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded\"}),/*#__PURE__*/_jsx(\"label\",{htmlFor:\"hasExpiry\",className:\"ml-2 block text-sm text-gray-700\",children:\"This product has an expiry date\"})]}),formData.hasExpiry&&/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700 mb-1\",children:\"Expiry Date *\"}),/*#__PURE__*/_jsx(\"input\",{type:\"date\",required:formData.hasExpiry,value:formData.expiryDate,onChange:e=>setFormData(_objectSpread(_objectSpread({},formData),{},{expiryDate:e.target.value})),className:\"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-primary-500 focus:border-primary-500\",min:new Date().toISOString().split('T')[0]})]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsx(\"input\",{type:\"checkbox\",id:\"isActive\",checked:formData.isActive,onChange:e=>setFormData(_objectSpread(_objectSpread({},formData),{},{isActive:e.target.checked})),className:\"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded\"}),/*#__PURE__*/_jsx(\"label\",{htmlFor:\"isActive\",className:\"ml-2 block text-sm text-gray-700\",children:\"Product is active and available for sale\"})]}),formData.reorderLevel>formData.stockQuantity&&/*#__PURE__*/_jsx(\"div\",{className:\"bg-yellow-50 border border-yellow-200 rounded-md p-3\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex\",children:[/*#__PURE__*/_jsx(AlertTriangle,{className:\"h-5 w-5 text-yellow-400 mr-2\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-sm text-yellow-700\",children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Warning:\"}),\" Reorder level is higher than current stock. This product will immediately show as low stock.\"]})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex justify-end space-x-3 pt-6 border-t\",children:[/*#__PURE__*/_jsx(\"button\",{type:\"button\",onClick:onClose,className:\"px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500\",children:\"Cancel\"}),/*#__PURE__*/_jsx(\"button\",{type:\"submit\",disabled:loading||!validateForm(),className:\"px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed\",children:loading?'Saving...':isEditing?'Update Product':'Create Product'})]})]})]})})});};export default ProductModal;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "X", "Package", "DollarSign", "Calendar", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "useProducts", "jsx", "_jsx", "jsxs", "_jsxs", "ProductModal", "_ref", "product", "onClose", "createProduct", "updateProduct", "getProductCategories", "loading", "setLoading", "error", "setError", "formData", "setFormData", "name", "description", "price", "category", "stockQuantity", "reorderLevel", "hasEx<PERSON>ry", "expiryDate", "isActive", "isEditing", "categories", "predefinedCategories", "allCategories", "Set", "sort", "toISOString", "split", "handleSubmit", "e", "preventDefault", "productData", "_objectSpread", "Date", "id", "message", "concat", "validateForm", "trim", "className", "children", "onClick", "onSubmit", "type", "required", "value", "onChange", "target", "placeholder", "map", "rows", "min", "step", "parseFloat", "parseInt", "checked", "htmlFor", "disabled"], "sources": ["E:/FX/Cyber POS/cyber-pos-system/src/components/inventory/ProductModal.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { X, Package, DollarSign, Calendar, AlertTriangle } from 'lucide-react';\nimport { useProducts } from '../../hooks/useProducts';\nimport { Product } from '../../types';\n\ninterface ProductModalProps {\n  product: Product | null;\n  onClose: () => void;\n}\n\nconst ProductModal: React.FC<ProductModalProps> = ({ product, onClose }) => {\n  const { createProduct, updateProduct, getProductCategories } = useProducts();\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n\n  const [formData, setFormData] = useState({\n    name: '',\n    description: '',\n    price: 0,\n    category: '',\n    stockQuantity: 0,\n    reorderLevel: 0,\n    hasExpiry: false,\n    expiryDate: '',\n    isActive: true\n  });\n\n  const isEditing = !!product;\n  const categories = getProductCategories();\n  \n  const predefinedCategories = [\n    'Paper',\n    'Writing',\n    'Office',\n    'Printer',\n    'Electronics',\n    'Stationery',\n    'Other'\n  ];\n\n  const allCategories = [...new Set([...predefinedCategories, ...categories])].sort();\n\n  useEffect(() => {\n    if (product) {\n      setFormData({\n        name: product.name,\n        description: product.description,\n        price: product.price,\n        category: product.category,\n        stockQuantity: product.stockQuantity,\n        reorderLevel: product.reorderLevel,\n        hasExpiry: product.hasExpiry,\n        expiryDate: product.expiryDate ? product.expiryDate.toISOString().split('T')[0] : '',\n        isActive: product.isActive\n      });\n    }\n  }, [product]);\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    setError('');\n    setLoading(true);\n\n    try {\n      const productData: any = {\n        ...formData\n      };\n\n      // Only include expiryDate if hasExpiry is true and expiryDate is provided\n      if (formData.hasExpiry && formData.expiryDate) {\n        productData.expiryDate = new Date(formData.expiryDate);\n      }\n      // Remove expiryDate field entirely if not needed to avoid undefined values\n      else {\n        delete productData.expiryDate;\n      }\n\n      if (isEditing) {\n        await updateProduct(product.id, productData);\n      } else {\n        await createProduct(productData);\n      }\n\n      onClose();\n    } catch (error: any) {\n      setError(error.message || `Failed to ${isEditing ? 'update' : 'create'} product`);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const validateForm = () => {\n    if (!formData.name.trim()) return false;\n    if (!formData.category.trim()) return false;\n    if (formData.price < 0) return false;\n    if (formData.stockQuantity < 0) return false;\n    if (formData.reorderLevel < 0) return false;\n    if (formData.hasExpiry && !formData.expiryDate) return false;\n    return true;\n  };\n\n  return (\n    <div className=\"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50\">\n      <div className=\"relative top-20 mx-auto p-5 border w-full max-w-2xl shadow-lg rounded-md bg-white\">\n        <div className=\"mt-3\">\n          <div className=\"flex items-center justify-between mb-6\">\n            <h3 className=\"text-lg font-medium text-gray-900 flex items-center\">\n              <Package className=\"h-5 w-5 mr-2\" />\n              {isEditing ? 'Edit Product' : 'Add New Product'}\n            </h3>\n            <button\n              onClick={onClose}\n              className=\"text-gray-400 hover:text-gray-600\"\n            >\n              <X className=\"h-5 w-5\" />\n            </button>\n          </div>\n\n          {error && (\n            <div className=\"mb-4 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded\">\n              {error}\n            </div>\n          )}\n\n          <form onSubmit={handleSubmit} className=\"space-y-6\">\n            {/* Basic Information */}\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                  Product Name *\n                </label>\n                <input\n                  type=\"text\"\n                  required\n                  value={formData.name}\n                  onChange={(e) => setFormData({ ...formData, name: e.target.value })}\n                  className=\"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-primary-500 focus:border-primary-500\"\n                  placeholder=\"e.g., A4 Paper\"\n                />\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                  Category *\n                </label>\n                <select\n                  required\n                  value={formData.category}\n                  onChange={(e) => setFormData({ ...formData, category: e.target.value })}\n                  className=\"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-primary-500 focus:border-primary-500\"\n                >\n                  <option value=\"\">Select Category</option>\n                  {allCategories.map(category => (\n                    <option key={category} value={category}>{category}</option>\n                  ))}\n                </select>\n              </div>\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                Description\n              </label>\n              <textarea\n                value={formData.description}\n                onChange={(e) => setFormData({ ...formData, description: e.target.value })}\n                rows={3}\n                className=\"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-primary-500 focus:border-primary-500\"\n                placeholder=\"Brief description of the product\"\n              />\n            </div>\n\n            {/* Pricing */}\n            <div className=\"bg-gray-50 p-4 rounded-lg\">\n              <h4 className=\"text-sm font-medium text-gray-900 mb-3 flex items-center\">\n                <DollarSign className=\"h-4 w-4 mr-2\" />\n                Pricing Information\n              </h4>\n              \n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                  Unit Price (KSh) *\n                </label>\n                <input\n                  type=\"number\"\n                  required\n                  min=\"0\"\n                  step=\"0.01\"\n                  value={formData.price}\n                  onChange={(e) => setFormData({ ...formData, price: parseFloat(e.target.value) || 0 })}\n                  className=\"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-primary-500 focus:border-primary-500\"\n                />\n              </div>\n            </div>\n\n            {/* Inventory Management */}\n            <div className=\"bg-blue-50 p-4 rounded-lg\">\n              <h4 className=\"text-sm font-medium text-gray-900 mb-3 flex items-center\">\n                <Package className=\"h-4 w-4 mr-2\" />\n                Inventory Management\n              </h4>\n              \n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                    Initial Stock Quantity *\n                  </label>\n                  <input\n                    type=\"number\"\n                    required\n                    min=\"0\"\n                    value={formData.stockQuantity}\n                    onChange={(e) => setFormData({ ...formData, stockQuantity: parseInt(e.target.value) || 0 })}\n                    className=\"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-primary-500 focus:border-primary-500\"\n                  />\n                </div>\n                \n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                    Reorder Level *\n                  </label>\n                  <input\n                    type=\"number\"\n                    required\n                    min=\"0\"\n                    value={formData.reorderLevel}\n                    onChange={(e) => setFormData({ ...formData, reorderLevel: parseInt(e.target.value) || 0 })}\n                    className=\"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-primary-500 focus:border-primary-500\"\n                  />\n                  <p className=\"text-xs text-gray-500 mt-1\">\n                    Alert when stock falls below this level\n                  </p>\n                </div>\n              </div>\n            </div>\n\n            {/* Expiry Management */}\n            <div className=\"bg-orange-50 p-4 rounded-lg\">\n              <h4 className=\"text-sm font-medium text-gray-900 mb-3 flex items-center\">\n                <Calendar className=\"h-4 w-4 mr-2\" />\n                Expiry Management\n              </h4>\n              \n              <div className=\"space-y-3\">\n                <div className=\"flex items-center\">\n                  <input\n                    type=\"checkbox\"\n                    id=\"hasExpiry\"\n                    checked={formData.hasExpiry}\n                    onChange={(e) => setFormData({ \n                      ...formData, \n                      hasExpiry: e.target.checked,\n                      expiryDate: e.target.checked ? formData.expiryDate : ''\n                    })}\n                    className=\"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded\"\n                  />\n                  <label htmlFor=\"hasExpiry\" className=\"ml-2 block text-sm text-gray-700\">\n                    This product has an expiry date\n                  </label>\n                </div>\n                \n                {formData.hasExpiry && (\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                      Expiry Date *\n                    </label>\n                    <input\n                      type=\"date\"\n                      required={formData.hasExpiry}\n                      value={formData.expiryDate}\n                      onChange={(e) => setFormData({ ...formData, expiryDate: e.target.value })}\n                      className=\"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-primary-500 focus:border-primary-500\"\n                      min={new Date().toISOString().split('T')[0]}\n                    />\n                  </div>\n                )}\n              </div>\n            </div>\n\n            {/* Status */}\n            <div className=\"flex items-center\">\n              <input\n                type=\"checkbox\"\n                id=\"isActive\"\n                checked={formData.isActive}\n                onChange={(e) => setFormData({ ...formData, isActive: e.target.checked })}\n                className=\"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded\"\n              />\n              <label htmlFor=\"isActive\" className=\"ml-2 block text-sm text-gray-700\">\n                Product is active and available for sale\n              </label>\n            </div>\n\n            {/* Validation Warning */}\n            {formData.reorderLevel > formData.stockQuantity && (\n              <div className=\"bg-yellow-50 border border-yellow-200 rounded-md p-3\">\n                <div className=\"flex\">\n                  <AlertTriangle className=\"h-5 w-5 text-yellow-400 mr-2\" />\n                  <div className=\"text-sm text-yellow-700\">\n                    <strong>Warning:</strong> Reorder level is higher than current stock. \n                    This product will immediately show as low stock.\n                  </div>\n                </div>\n              </div>\n            )}\n\n            {/* Actions */}\n            <div className=\"flex justify-end space-x-3 pt-6 border-t\">\n              <button\n                type=\"button\"\n                onClick={onClose}\n                className=\"px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500\"\n              >\n                Cancel\n              </button>\n              <button\n                type=\"submit\"\n                disabled={loading || !validateForm()}\n                className=\"px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed\"\n              >\n                {loading ? 'Saving...' : (isEditing ? 'Update Product' : 'Create Product')}\n              </button>\n            </div>\n          </form>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default ProductModal;\n"], "mappings": "qHAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OAASC,CAAC,CAAEC,OAAO,CAAEC,UAAU,CAAEC,QAAQ,CAAEC,aAAa,KAAQ,cAAc,CAC9E,OAASC,WAAW,KAAQ,yBAAyB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAQtD,KAAM,CAAAC,YAAyC,CAAGC,IAAA,EAA0B,IAAzB,CAAEC,OAAO,CAAEC,OAAQ,CAAC,CAAAF,IAAA,CACrE,KAAM,CAAEG,aAAa,CAAEC,aAAa,CAAEC,oBAAqB,CAAC,CAAGX,WAAW,CAAC,CAAC,CAC5E,KAAM,CAACY,OAAO,CAAEC,UAAU,CAAC,CAAGpB,QAAQ,CAAC,KAAK,CAAC,CAC7C,KAAM,CAACqB,KAAK,CAAEC,QAAQ,CAAC,CAAGtB,QAAQ,CAAC,EAAE,CAAC,CAEtC,KAAM,CAACuB,QAAQ,CAAEC,WAAW,CAAC,CAAGxB,QAAQ,CAAC,CACvCyB,IAAI,CAAE,EAAE,CACRC,WAAW,CAAE,EAAE,CACfC,KAAK,CAAE,CAAC,CACRC,QAAQ,CAAE,EAAE,CACZC,aAAa,CAAE,CAAC,CAChBC,YAAY,CAAE,CAAC,CACfC,SAAS,CAAE,KAAK,CAChBC,UAAU,CAAE,EAAE,CACdC,QAAQ,CAAE,IACZ,CAAC,CAAC,CAEF,KAAM,CAAAC,SAAS,CAAG,CAAC,CAACpB,OAAO,CAC3B,KAAM,CAAAqB,UAAU,CAAGjB,oBAAoB,CAAC,CAAC,CAEzC,KAAM,CAAAkB,oBAAoB,CAAG,CAC3B,OAAO,CACP,SAAS,CACT,QAAQ,CACR,SAAS,CACT,aAAa,CACb,YAAY,CACZ,OAAO,CACR,CAED,KAAM,CAAAC,aAAa,CAAG,CAAC,GAAG,GAAI,CAAAC,GAAG,CAAC,CAAC,GAAGF,oBAAoB,CAAE,GAAGD,UAAU,CAAC,CAAC,CAAC,CAACI,IAAI,CAAC,CAAC,CAEnFtC,SAAS,CAAC,IAAM,CACd,GAAIa,OAAO,CAAE,CACXU,WAAW,CAAC,CACVC,IAAI,CAAEX,OAAO,CAACW,IAAI,CAClBC,WAAW,CAAEZ,OAAO,CAACY,WAAW,CAChCC,KAAK,CAAEb,OAAO,CAACa,KAAK,CACpBC,QAAQ,CAAEd,OAAO,CAACc,QAAQ,CAC1BC,aAAa,CAAEf,OAAO,CAACe,aAAa,CACpCC,YAAY,CAAEhB,OAAO,CAACgB,YAAY,CAClCC,SAAS,CAAEjB,OAAO,CAACiB,SAAS,CAC5BC,UAAU,CAAElB,OAAO,CAACkB,UAAU,CAAGlB,OAAO,CAACkB,UAAU,CAACQ,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAG,EAAE,CACpFR,QAAQ,CAAEnB,OAAO,CAACmB,QACpB,CAAC,CAAC,CACJ,CACF,CAAC,CAAE,CAACnB,OAAO,CAAC,CAAC,CAEb,KAAM,CAAA4B,YAAY,CAAG,KAAO,CAAAC,CAAkB,EAAK,CACjDA,CAAC,CAACC,cAAc,CAAC,CAAC,CAClBtB,QAAQ,CAAC,EAAE,CAAC,CACZF,UAAU,CAAC,IAAI,CAAC,CAEhB,GAAI,CACF,KAAM,CAAAyB,WAAgB,CAAAC,aAAA,IACjBvB,QAAQ,CACZ,CAED;AACA,GAAIA,QAAQ,CAACQ,SAAS,EAAIR,QAAQ,CAACS,UAAU,CAAE,CAC7Ca,WAAW,CAACb,UAAU,CAAG,GAAI,CAAAe,IAAI,CAACxB,QAAQ,CAACS,UAAU,CAAC,CACxD,CACA;AAAA,IACK,CACH,MAAO,CAAAa,WAAW,CAACb,UAAU,CAC/B,CAEA,GAAIE,SAAS,CAAE,CACb,KAAM,CAAAjB,aAAa,CAACH,OAAO,CAACkC,EAAE,CAAEH,WAAW,CAAC,CAC9C,CAAC,IAAM,CACL,KAAM,CAAA7B,aAAa,CAAC6B,WAAW,CAAC,CAClC,CAEA9B,OAAO,CAAC,CAAC,CACX,CAAE,MAAOM,KAAU,CAAE,CACnBC,QAAQ,CAACD,KAAK,CAAC4B,OAAO,eAAAC,MAAA,CAAiBhB,SAAS,CAAG,QAAQ,CAAG,QAAQ,YAAU,CAAC,CACnF,CAAC,OAAS,CACRd,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED,KAAM,CAAA+B,YAAY,CAAGA,CAAA,GAAM,CACzB,GAAI,CAAC5B,QAAQ,CAACE,IAAI,CAAC2B,IAAI,CAAC,CAAC,CAAE,MAAO,MAAK,CACvC,GAAI,CAAC7B,QAAQ,CAACK,QAAQ,CAACwB,IAAI,CAAC,CAAC,CAAE,MAAO,MAAK,CAC3C,GAAI7B,QAAQ,CAACI,KAAK,CAAG,CAAC,CAAE,MAAO,MAAK,CACpC,GAAIJ,QAAQ,CAACM,aAAa,CAAG,CAAC,CAAE,MAAO,MAAK,CAC5C,GAAIN,QAAQ,CAACO,YAAY,CAAG,CAAC,CAAE,MAAO,MAAK,CAC3C,GAAIP,QAAQ,CAACQ,SAAS,EAAI,CAACR,QAAQ,CAACS,UAAU,CAAE,MAAO,MAAK,CAC5D,MAAO,KAAI,CACb,CAAC,CAED,mBACEvB,IAAA,QAAK4C,SAAS,CAAC,4EAA4E,CAAAC,QAAA,cACzF7C,IAAA,QAAK4C,SAAS,CAAC,mFAAmF,CAAAC,QAAA,cAChG3C,KAAA,QAAK0C,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnB3C,KAAA,QAAK0C,SAAS,CAAC,wCAAwC,CAAAC,QAAA,eACrD3C,KAAA,OAAI0C,SAAS,CAAC,qDAAqD,CAAAC,QAAA,eACjE7C,IAAA,CAACN,OAAO,EAACkD,SAAS,CAAC,cAAc,CAAE,CAAC,CACnCnB,SAAS,CAAG,cAAc,CAAG,iBAAiB,EAC7C,CAAC,cACLzB,IAAA,WACE8C,OAAO,CAAExC,OAAQ,CACjBsC,SAAS,CAAC,mCAAmC,CAAAC,QAAA,cAE7C7C,IAAA,CAACP,CAAC,EAACmD,SAAS,CAAC,SAAS,CAAE,CAAC,CACnB,CAAC,EACN,CAAC,CAELhC,KAAK,eACJZ,IAAA,QAAK4C,SAAS,CAAC,qEAAqE,CAAAC,QAAA,CACjFjC,KAAK,CACH,CACN,cAEDV,KAAA,SAAM6C,QAAQ,CAAEd,YAAa,CAACW,SAAS,CAAC,WAAW,CAAAC,QAAA,eAEjD3C,KAAA,QAAK0C,SAAS,CAAC,uCAAuC,CAAAC,QAAA,eACpD3C,KAAA,QAAA2C,QAAA,eACE7C,IAAA,UAAO4C,SAAS,CAAC,8CAA8C,CAAAC,QAAA,CAAC,gBAEhE,CAAO,CAAC,cACR7C,IAAA,UACEgD,IAAI,CAAC,MAAM,CACXC,QAAQ,MACRC,KAAK,CAAEpC,QAAQ,CAACE,IAAK,CACrBmC,QAAQ,CAAGjB,CAAC,EAAKnB,WAAW,CAAAsB,aAAA,CAAAA,aAAA,IAAMvB,QAAQ,MAAEE,IAAI,CAAEkB,CAAC,CAACkB,MAAM,CAACF,KAAK,EAAE,CAAE,CACpEN,SAAS,CAAC,uHAAuH,CACjIS,WAAW,CAAC,gBAAgB,CAC7B,CAAC,EACC,CAAC,cAENnD,KAAA,QAAA2C,QAAA,eACE7C,IAAA,UAAO4C,SAAS,CAAC,8CAA8C,CAAAC,QAAA,CAAC,YAEhE,CAAO,CAAC,cACR3C,KAAA,WACE+C,QAAQ,MACRC,KAAK,CAAEpC,QAAQ,CAACK,QAAS,CACzBgC,QAAQ,CAAGjB,CAAC,EAAKnB,WAAW,CAAAsB,aAAA,CAAAA,aAAA,IAAMvB,QAAQ,MAAEK,QAAQ,CAAEe,CAAC,CAACkB,MAAM,CAACF,KAAK,EAAE,CAAE,CACxEN,SAAS,CAAC,uHAAuH,CAAAC,QAAA,eAEjI7C,IAAA,WAAQkD,KAAK,CAAC,EAAE,CAAAL,QAAA,CAAC,iBAAe,CAAQ,CAAC,CACxCjB,aAAa,CAAC0B,GAAG,CAACnC,QAAQ,eACzBnB,IAAA,WAAuBkD,KAAK,CAAE/B,QAAS,CAAA0B,QAAA,CAAE1B,QAAQ,EAApCA,QAA6C,CAC3D,CAAC,EACI,CAAC,EACN,CAAC,EACH,CAAC,cAENjB,KAAA,QAAA2C,QAAA,eACE7C,IAAA,UAAO4C,SAAS,CAAC,8CAA8C,CAAAC,QAAA,CAAC,aAEhE,CAAO,CAAC,cACR7C,IAAA,aACEkD,KAAK,CAAEpC,QAAQ,CAACG,WAAY,CAC5BkC,QAAQ,CAAGjB,CAAC,EAAKnB,WAAW,CAAAsB,aAAA,CAAAA,aAAA,IAAMvB,QAAQ,MAAEG,WAAW,CAAEiB,CAAC,CAACkB,MAAM,CAACF,KAAK,EAAE,CAAE,CAC3EK,IAAI,CAAE,CAAE,CACRX,SAAS,CAAC,uHAAuH,CACjIS,WAAW,CAAC,kCAAkC,CAC/C,CAAC,EACC,CAAC,cAGNnD,KAAA,QAAK0C,SAAS,CAAC,2BAA2B,CAAAC,QAAA,eACxC3C,KAAA,OAAI0C,SAAS,CAAC,0DAA0D,CAAAC,QAAA,eACtE7C,IAAA,CAACL,UAAU,EAACiD,SAAS,CAAC,cAAc,CAAE,CAAC,sBAEzC,EAAI,CAAC,cAEL1C,KAAA,QAAA2C,QAAA,eACE7C,IAAA,UAAO4C,SAAS,CAAC,8CAA8C,CAAAC,QAAA,CAAC,oBAEhE,CAAO,CAAC,cACR7C,IAAA,UACEgD,IAAI,CAAC,QAAQ,CACbC,QAAQ,MACRO,GAAG,CAAC,GAAG,CACPC,IAAI,CAAC,MAAM,CACXP,KAAK,CAAEpC,QAAQ,CAACI,KAAM,CACtBiC,QAAQ,CAAGjB,CAAC,EAAKnB,WAAW,CAAAsB,aAAA,CAAAA,aAAA,IAAMvB,QAAQ,MAAEI,KAAK,CAAEwC,UAAU,CAACxB,CAAC,CAACkB,MAAM,CAACF,KAAK,CAAC,EAAI,CAAC,EAAE,CAAE,CACtFN,SAAS,CAAC,uHAAuH,CAClI,CAAC,EACC,CAAC,EACH,CAAC,cAGN1C,KAAA,QAAK0C,SAAS,CAAC,2BAA2B,CAAAC,QAAA,eACxC3C,KAAA,OAAI0C,SAAS,CAAC,0DAA0D,CAAAC,QAAA,eACtE7C,IAAA,CAACN,OAAO,EAACkD,SAAS,CAAC,cAAc,CAAE,CAAC,uBAEtC,EAAI,CAAC,cAEL1C,KAAA,QAAK0C,SAAS,CAAC,uCAAuC,CAAAC,QAAA,eACpD3C,KAAA,QAAA2C,QAAA,eACE7C,IAAA,UAAO4C,SAAS,CAAC,8CAA8C,CAAAC,QAAA,CAAC,0BAEhE,CAAO,CAAC,cACR7C,IAAA,UACEgD,IAAI,CAAC,QAAQ,CACbC,QAAQ,MACRO,GAAG,CAAC,GAAG,CACPN,KAAK,CAAEpC,QAAQ,CAACM,aAAc,CAC9B+B,QAAQ,CAAGjB,CAAC,EAAKnB,WAAW,CAAAsB,aAAA,CAAAA,aAAA,IAAMvB,QAAQ,MAAEM,aAAa,CAAEuC,QAAQ,CAACzB,CAAC,CAACkB,MAAM,CAACF,KAAK,CAAC,EAAI,CAAC,EAAE,CAAE,CAC5FN,SAAS,CAAC,uHAAuH,CAClI,CAAC,EACC,CAAC,cAEN1C,KAAA,QAAA2C,QAAA,eACE7C,IAAA,UAAO4C,SAAS,CAAC,8CAA8C,CAAAC,QAAA,CAAC,iBAEhE,CAAO,CAAC,cACR7C,IAAA,UACEgD,IAAI,CAAC,QAAQ,CACbC,QAAQ,MACRO,GAAG,CAAC,GAAG,CACPN,KAAK,CAAEpC,QAAQ,CAACO,YAAa,CAC7B8B,QAAQ,CAAGjB,CAAC,EAAKnB,WAAW,CAAAsB,aAAA,CAAAA,aAAA,IAAMvB,QAAQ,MAAEO,YAAY,CAAEsC,QAAQ,CAACzB,CAAC,CAACkB,MAAM,CAACF,KAAK,CAAC,EAAI,CAAC,EAAE,CAAE,CAC3FN,SAAS,CAAC,uHAAuH,CAClI,CAAC,cACF5C,IAAA,MAAG4C,SAAS,CAAC,4BAA4B,CAAAC,QAAA,CAAC,yCAE1C,CAAG,CAAC,EACD,CAAC,EACH,CAAC,EACH,CAAC,cAGN3C,KAAA,QAAK0C,SAAS,CAAC,6BAA6B,CAAAC,QAAA,eAC1C3C,KAAA,OAAI0C,SAAS,CAAC,0DAA0D,CAAAC,QAAA,eACtE7C,IAAA,CAACJ,QAAQ,EAACgD,SAAS,CAAC,cAAc,CAAE,CAAC,oBAEvC,EAAI,CAAC,cAEL1C,KAAA,QAAK0C,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxB3C,KAAA,QAAK0C,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChC7C,IAAA,UACEgD,IAAI,CAAC,UAAU,CACfT,EAAE,CAAC,WAAW,CACdqB,OAAO,CAAE9C,QAAQ,CAACQ,SAAU,CAC5B6B,QAAQ,CAAGjB,CAAC,EAAKnB,WAAW,CAAAsB,aAAA,CAAAA,aAAA,IACvBvB,QAAQ,MACXQ,SAAS,CAAEY,CAAC,CAACkB,MAAM,CAACQ,OAAO,CAC3BrC,UAAU,CAAEW,CAAC,CAACkB,MAAM,CAACQ,OAAO,CAAG9C,QAAQ,CAACS,UAAU,CAAG,EAAE,EACxD,CAAE,CACHqB,SAAS,CAAC,yEAAyE,CACpF,CAAC,cACF5C,IAAA,UAAO6D,OAAO,CAAC,WAAW,CAACjB,SAAS,CAAC,kCAAkC,CAAAC,QAAA,CAAC,iCAExE,CAAO,CAAC,EACL,CAAC,CAEL/B,QAAQ,CAACQ,SAAS,eACjBpB,KAAA,QAAA2C,QAAA,eACE7C,IAAA,UAAO4C,SAAS,CAAC,8CAA8C,CAAAC,QAAA,CAAC,eAEhE,CAAO,CAAC,cACR7C,IAAA,UACEgD,IAAI,CAAC,MAAM,CACXC,QAAQ,CAAEnC,QAAQ,CAACQ,SAAU,CAC7B4B,KAAK,CAAEpC,QAAQ,CAACS,UAAW,CAC3B4B,QAAQ,CAAGjB,CAAC,EAAKnB,WAAW,CAAAsB,aAAA,CAAAA,aAAA,IAAMvB,QAAQ,MAAES,UAAU,CAAEW,CAAC,CAACkB,MAAM,CAACF,KAAK,EAAE,CAAE,CAC1EN,SAAS,CAAC,uHAAuH,CACjIY,GAAG,CAAE,GAAI,CAAAlB,IAAI,CAAC,CAAC,CAACP,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAE,CAC7C,CAAC,EACC,CACN,EACE,CAAC,EACH,CAAC,cAGN9B,KAAA,QAAK0C,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChC7C,IAAA,UACEgD,IAAI,CAAC,UAAU,CACfT,EAAE,CAAC,UAAU,CACbqB,OAAO,CAAE9C,QAAQ,CAACU,QAAS,CAC3B2B,QAAQ,CAAGjB,CAAC,EAAKnB,WAAW,CAAAsB,aAAA,CAAAA,aAAA,IAAMvB,QAAQ,MAAEU,QAAQ,CAAEU,CAAC,CAACkB,MAAM,CAACQ,OAAO,EAAE,CAAE,CAC1EhB,SAAS,CAAC,yEAAyE,CACpF,CAAC,cACF5C,IAAA,UAAO6D,OAAO,CAAC,UAAU,CAACjB,SAAS,CAAC,kCAAkC,CAAAC,QAAA,CAAC,0CAEvE,CAAO,CAAC,EACL,CAAC,CAGL/B,QAAQ,CAACO,YAAY,CAAGP,QAAQ,CAACM,aAAa,eAC7CpB,IAAA,QAAK4C,SAAS,CAAC,sDAAsD,CAAAC,QAAA,cACnE3C,KAAA,QAAK0C,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnB7C,IAAA,CAACH,aAAa,EAAC+C,SAAS,CAAC,8BAA8B,CAAE,CAAC,cAC1D1C,KAAA,QAAK0C,SAAS,CAAC,yBAAyB,CAAAC,QAAA,eACtC7C,IAAA,WAAA6C,QAAA,CAAQ,UAAQ,CAAQ,CAAC,gGAE3B,EAAK,CAAC,EACH,CAAC,CACH,CACN,cAGD3C,KAAA,QAAK0C,SAAS,CAAC,0CAA0C,CAAAC,QAAA,eACvD7C,IAAA,WACEgD,IAAI,CAAC,QAAQ,CACbF,OAAO,CAAExC,OAAQ,CACjBsC,SAAS,CAAC,uJAAuJ,CAAAC,QAAA,CAClK,QAED,CAAQ,CAAC,cACT7C,IAAA,WACEgD,IAAI,CAAC,QAAQ,CACbc,QAAQ,CAAEpD,OAAO,EAAI,CAACgC,YAAY,CAAC,CAAE,CACrCE,SAAS,CAAC,gMAAgM,CAAAC,QAAA,CAEzMnC,OAAO,CAAG,WAAW,CAAIe,SAAS,CAAG,gBAAgB,CAAG,gBAAiB,CACpE,CAAC,EACN,CAAC,EACF,CAAC,EACJ,CAAC,CACH,CAAC,CACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAAtB,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}