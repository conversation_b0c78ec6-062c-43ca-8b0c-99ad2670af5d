{"ast": null, "code": "import{getBusinessSettings}from'../services/businessSettingsService';export const generateReceiptHTML=receiptData=>{const{transaction,attendant,businessInfo,businessSettings}=receiptData;const receiptDate=transaction.createdAt.toLocaleString();// Use business settings if available, otherwise fall back to businessInfo\nconst companyName=(businessSettings===null||businessSettings===void 0?void 0:businessSettings.businessName)||(businessInfo===null||businessInfo===void 0?void 0:businessInfo.name)||'Business Name';const companyAddress=(businessSettings===null||businessSettings===void 0?void 0:businessSettings.address)||(businessInfo===null||businessInfo===void 0?void 0:businessInfo.address)||'Business Address';const companyPhone=(businessSettings===null||businessSettings===void 0?void 0:businessSettings.phone)||(businessInfo===null||businessInfo===void 0?void 0:businessInfo.phone)||'Phone Number';const companyEmail=(businessSettings===null||businessSettings===void 0?void 0:businessSettings.email)||(businessInfo===null||businessInfo===void 0?void 0:businessInfo.email);const currency=(businessSettings===null||businessSettings===void 0?void 0:businessSettings.currency)||'KSh';const logoUrl=businessSettings===null||businessSettings===void 0?void 0:businessSettings.logoUrl;const receiptHeader=businessSettings===null||businessSettings===void 0?void 0:businessSettings.receiptHeader;const receiptFooter=(businessSettings===null||businessSettings===void 0?void 0:businessSettings.receiptFooter)||'Thank you for your business!';const operatingHours=businessSettings===null||businessSettings===void 0?void 0:businessSettings.operatingHours;return\"\\n    <!DOCTYPE html>\\n    <html>\\n    <head>\\n      <meta charset=\\\"utf-8\\\">\\n      <title>Receipt - \".concat(transaction.id,\"</title>\\n      <style>\\n        @media print {\\n          body { margin: 0; }\\n          .no-print { display: none; }\\n        }\\n        \\n        body {\\n          font-family: 'Courier New', monospace;\\n          font-size: 12px;\\n          line-height: 1.4;\\n          max-width: 300px;\\n          margin: 0 auto;\\n          padding: 10px;\\n        }\\n        \\n        .header {\\n          text-align: center;\\n          border-bottom: 2px solid #000;\\n          padding-bottom: 10px;\\n          margin-bottom: 15px;\\n        }\\n\\n        .logo {\\n          max-width: 80px;\\n          max-height: 80px;\\n          margin: 0 auto 10px auto;\\n          display: block;\\n        }\\n\\n        .business-name {\\n          font-size: 16px;\\n          font-weight: bold;\\n          margin-bottom: 5px;\\n        }\\n\\n        .business-info {\\n          font-size: 10px;\\n          margin-bottom: 2px;\\n        }\\n\\n        .receipt-header-message {\\n          font-size: 11px;\\n          font-style: italic;\\n          margin-bottom: 5px;\\n          color: #666;\\n        }\\n        \\n        .receipt-info {\\n          margin-bottom: 15px;\\n          font-size: 10px;\\n        }\\n        \\n        .items-table {\\n          width: 100%;\\n          border-collapse: collapse;\\n          margin-bottom: 15px;\\n        }\\n        \\n        .items-table th,\\n        .items-table td {\\n          text-align: left;\\n          padding: 2px 0;\\n          border-bottom: 1px dotted #ccc;\\n        }\\n        \\n        .items-table th {\\n          font-weight: bold;\\n          border-bottom: 1px solid #000;\\n        }\\n        \\n        .item-name {\\n          width: 60%;\\n        }\\n        \\n        .item-qty {\\n          width: 15%;\\n          text-align: center;\\n        }\\n        \\n        .item-price {\\n          width: 25%;\\n          text-align: right;\\n        }\\n        \\n        .totals {\\n          border-top: 2px solid #000;\\n          padding-top: 10px;\\n          margin-bottom: 15px;\\n        }\\n        \\n        .total-line {\\n          display: flex;\\n          justify-content: space-between;\\n          margin-bottom: 3px;\\n        }\\n        \\n        .total-line.final {\\n          font-weight: bold;\\n          font-size: 14px;\\n          border-top: 1px solid #000;\\n          padding-top: 5px;\\n          margin-top: 5px;\\n        }\\n        \\n        .payment-info {\\n          margin-bottom: 15px;\\n          font-size: 10px;\\n        }\\n        \\n        .footer {\\n          text-align: center;\\n          border-top: 1px dotted #ccc;\\n          padding-top: 10px;\\n          font-size: 10px;\\n        }\\n        \\n        .print-button {\\n          background: #007bff;\\n          color: white;\\n          border: none;\\n          padding: 10px 20px;\\n          border-radius: 5px;\\n          cursor: pointer;\\n          margin: 20px auto;\\n          display: block;\\n        }\\n        \\n        .print-button:hover {\\n          background: #0056b3;\\n        }\\n      </style>\\n    </head>\\n    <body>\\n      <div class=\\\"header\\\">\\n        \").concat(logoUrl?\"<img src=\\\"\".concat(logoUrl,\"\\\" alt=\\\"Company Logo\\\" class=\\\"logo\\\" />\"):'',\"\\n        <div class=\\\"business-name\\\">\").concat(companyName,\"</div>\\n        \").concat(receiptHeader?\"<div class=\\\"receipt-header-message\\\">\".concat(receiptHeader,\"</div>\"):'',\"\\n        <div class=\\\"business-info\\\">\").concat(companyAddress,\"</div>\\n        <div class=\\\"business-info\\\">Tel: \").concat(companyPhone,\"</div>\\n        \").concat(companyEmail?\"<div class=\\\"business-info\\\">Email: \".concat(companyEmail,\"</div>\"):'',\"\\n        \").concat(operatingHours?\"<div class=\\\"business-info\\\">\".concat(operatingHours.replace(/\\n/g,'<br>'),\"</div>\"):'',\"\\n      </div>\\n      \\n      <div class=\\\"receipt-info\\\">\\n        <div><strong>Receipt #:</strong> \").concat(transaction.id.substring(0,8).toUpperCase(),\"</div>\\n        <div><strong>Date:</strong> \").concat(receiptDate,\"</div>\\n        <div><strong>Attendant:</strong> \").concat(attendant.name,\"</div>\\n        \").concat(transaction.customerId?\"<div><strong>Customer:</strong> Credit Sale</div>\":'',\"\\n      </div>\\n      \\n      <table class=\\\"items-table\\\">\\n        <thead>\\n          <tr>\\n            <th class=\\\"item-name\\\">Item</th>\\n            <th class=\\\"item-qty\\\">Qty</th>\\n            <th class=\\\"item-price\\\">Amount</th>\\n          </tr>\\n        </thead>\\n        <tbody>\\n          \").concat(transaction.items.map(item=>\"\\n            <tr>\\n              <td class=\\\"item-name\\\">\\n                \".concat(item.name,\"\\n                \").concat(item.notes?\"<br><small style=\\\"font-size: 9px; color: #666;\\\">\".concat(item.notes,\"</small>\"):'',\"\\n              </td>\\n              <td class=\\\"item-qty\\\">\").concat(item.quantity,\"</td>\\n              <td class=\\\"item-price\\\">\").concat(currency,\" \").concat(item.totalPrice.toLocaleString(),\"</td>\\n            </tr>\\n          \")).join(''),\"\\n        </tbody>\\n      </table>\\n      \\n      <div class=\\\"totals\\\">\\n        <div class=\\\"total-line\\\">\\n          <span>Subtotal:</span>\\n          <span>\").concat(currency,\" \").concat(transaction.subtotal.toLocaleString(),\"</span>\\n        </div>\\n        \").concat(transaction.discount&&transaction.discount>0?\"\\n          <div class=\\\"total-line\\\">\\n            <span>Discount:</span>\\n            <span>-\".concat(currency,\" \").concat(transaction.discount.toLocaleString(),\"</span>\\n          </div>\\n        \"):'',\"\\n        <div class=\\\"total-line final\\\">\\n          <span>TOTAL:</span>\\n          <span>\").concat(currency,\" \").concat(transaction.total.toLocaleString(),\"</span>\\n        </div>\\n      </div>\\n      \\n      <div class=\\\"payment-info\\\">\\n        <div><strong>Payment Method:</strong> \").concat(getPaymentMethodName(transaction.paymentMethod),\"</div>\\n        \").concat(transaction.notes?\"<div><strong>Notes:</strong> \".concat(transaction.notes,\"</div>\"):'',\"\\n      </div>\\n      \\n      <div class=\\\"footer\\\">\\n        <div>\").concat(receiptFooter.replace(/\\n/g,'<br>'),\"</div>\\n        <div style=\\\"margin-top: 10px; font-size: 9px;\\\">\\n          Powered by Cyber POS System\\n        </div>\\n      </div>\\n      \\n      <button class=\\\"print-button no-print\\\" onclick=\\\"window.print()\\\">\\n        Print Receipt\\n      </button>\\n      \\n      <script>\\n        // Auto-focus for printing\\n        window.onload = function() {\\n          window.focus();\\n        };\\n      </script>\\n    </body>\\n    </html>\\n  \");};const getPaymentMethodName=method=>{switch(method){case'cash':return'Cash';case'mpesa':return'M-PESA';case'debt':return'Credit/Debt';default:return method;}};export const printReceipt=receiptData=>{const receiptHTML=generateReceiptHTML(receiptData);const printWindow=window.open('','_blank','width=400,height=600');if(printWindow){printWindow.document.write(receiptHTML);printWindow.document.close();// Wait for content to load then print\nprintWindow.onload=()=>{setTimeout(()=>{printWindow.print();},250);};}else{alert('Please allow popups to print receipts');}};export const downloadReceiptPDF=async receiptData=>{// This would require a PDF library like jsPDF or html2pdf\n// For now, we'll just open the receipt in a new window\nconst receiptHTML=generateReceiptHTML(receiptData);const printWindow=window.open('','_blank');if(printWindow){printWindow.document.write(receiptHTML);printWindow.document.close();}};/**\n * Create receipt data with business settings\n */export const createReceiptData=async(transaction,attendant)=>{try{const businessSettings=await getBusinessSettings();return{transaction,attendant,businessSettings:businessSettings||undefined};}catch(error){console.error('Error loading business settings for receipt:',error);// Fallback to default business info\nreturn{transaction,attendant,businessInfo:{name:'Cyber Services & Stationery',address:'Your Business Address',phone:'+254 700 000 000',email:'<EMAIL>'}};}};/**\n * Print receipt with business settings\n */export const printReceiptWithSettings=async(transaction,attendant)=>{const receiptData=await createReceiptData(transaction,attendant);printReceipt(receiptData);};/**\n * Download receipt PDF with business settings\n */export const downloadReceiptPDFWithSettings=async(transaction,attendant)=>{const receiptData=await createReceiptData(transaction,attendant);await downloadReceiptPDF(receiptData);};", "map": {"version": 3, "names": ["getBusinessSettings", "generateReceiptHTML", "receiptData", "transaction", "attendant", "businessInfo", "businessSettings", "receiptDate", "createdAt", "toLocaleString", "companyName", "businessName", "name", "companyAddress", "address", "companyPhone", "phone", "companyEmail", "email", "currency", "logoUrl", "receiptHeader", "receiptFooter", "operatingHours", "concat", "id", "replace", "substring", "toUpperCase", "customerId", "items", "map", "item", "notes", "quantity", "totalPrice", "join", "subtotal", "discount", "total", "getPaymentMethodName", "paymentMethod", "method", "printReceipt", "receiptHTML", "printWindow", "window", "open", "document", "write", "close", "onload", "setTimeout", "print", "alert", "downloadReceiptPDF", "createReceiptData", "undefined", "error", "console", "printReceiptWithSettings", "downloadReceiptPDFWithSettings"], "sources": ["E:/FX/Cyber POS/cyber-pos-system/src/utils/receiptGenerator.ts"], "sourcesContent": ["import { Transaction, User, BusinessSettings } from '../types';\nimport { getBusinessSettings } from '../services/businessSettingsService';\n\nexport interface ReceiptData {\n  transaction: Transaction;\n  attendant: User;\n  businessInfo?: {\n    name: string;\n    address: string;\n    phone: string;\n    email?: string;\n  };\n  businessSettings?: BusinessSettings;\n}\n\nexport const generateReceiptHTML = (receiptData: ReceiptData): string => {\n  const { transaction, attendant, businessInfo, businessSettings } = receiptData;\n  const receiptDate = transaction.createdAt.toLocaleString();\n\n  // Use business settings if available, otherwise fall back to businessInfo\n  const companyName = businessSettings?.businessName || businessInfo?.name || 'Business Name';\n  const companyAddress = businessSettings?.address || businessInfo?.address || 'Business Address';\n  const companyPhone = businessSettings?.phone || businessInfo?.phone || 'Phone Number';\n  const companyEmail = businessSettings?.email || businessInfo?.email;\n  const currency = businessSettings?.currency || 'KSh';\n  const logoUrl = businessSettings?.logoUrl;\n  const receiptHeader = businessSettings?.receiptHeader;\n  const receiptFooter = businessSettings?.receiptFooter || 'Thank you for your business!';\n  const operatingHours = businessSettings?.operatingHours;\n\n  return `\n    <!DOCTYPE html>\n    <html>\n    <head>\n      <meta charset=\"utf-8\">\n      <title>Receipt - ${transaction.id}</title>\n      <style>\n        @media print {\n          body { margin: 0; }\n          .no-print { display: none; }\n        }\n        \n        body {\n          font-family: 'Courier New', monospace;\n          font-size: 12px;\n          line-height: 1.4;\n          max-width: 300px;\n          margin: 0 auto;\n          padding: 10px;\n        }\n        \n        .header {\n          text-align: center;\n          border-bottom: 2px solid #000;\n          padding-bottom: 10px;\n          margin-bottom: 15px;\n        }\n\n        .logo {\n          max-width: 80px;\n          max-height: 80px;\n          margin: 0 auto 10px auto;\n          display: block;\n        }\n\n        .business-name {\n          font-size: 16px;\n          font-weight: bold;\n          margin-bottom: 5px;\n        }\n\n        .business-info {\n          font-size: 10px;\n          margin-bottom: 2px;\n        }\n\n        .receipt-header-message {\n          font-size: 11px;\n          font-style: italic;\n          margin-bottom: 5px;\n          color: #666;\n        }\n        \n        .receipt-info {\n          margin-bottom: 15px;\n          font-size: 10px;\n        }\n        \n        .items-table {\n          width: 100%;\n          border-collapse: collapse;\n          margin-bottom: 15px;\n        }\n        \n        .items-table th,\n        .items-table td {\n          text-align: left;\n          padding: 2px 0;\n          border-bottom: 1px dotted #ccc;\n        }\n        \n        .items-table th {\n          font-weight: bold;\n          border-bottom: 1px solid #000;\n        }\n        \n        .item-name {\n          width: 60%;\n        }\n        \n        .item-qty {\n          width: 15%;\n          text-align: center;\n        }\n        \n        .item-price {\n          width: 25%;\n          text-align: right;\n        }\n        \n        .totals {\n          border-top: 2px solid #000;\n          padding-top: 10px;\n          margin-bottom: 15px;\n        }\n        \n        .total-line {\n          display: flex;\n          justify-content: space-between;\n          margin-bottom: 3px;\n        }\n        \n        .total-line.final {\n          font-weight: bold;\n          font-size: 14px;\n          border-top: 1px solid #000;\n          padding-top: 5px;\n          margin-top: 5px;\n        }\n        \n        .payment-info {\n          margin-bottom: 15px;\n          font-size: 10px;\n        }\n        \n        .footer {\n          text-align: center;\n          border-top: 1px dotted #ccc;\n          padding-top: 10px;\n          font-size: 10px;\n        }\n        \n        .print-button {\n          background: #007bff;\n          color: white;\n          border: none;\n          padding: 10px 20px;\n          border-radius: 5px;\n          cursor: pointer;\n          margin: 20px auto;\n          display: block;\n        }\n        \n        .print-button:hover {\n          background: #0056b3;\n        }\n      </style>\n    </head>\n    <body>\n      <div class=\"header\">\n        ${logoUrl ? `<img src=\"${logoUrl}\" alt=\"Company Logo\" class=\"logo\" />` : ''}\n        <div class=\"business-name\">${companyName}</div>\n        ${receiptHeader ? `<div class=\"receipt-header-message\">${receiptHeader}</div>` : ''}\n        <div class=\"business-info\">${companyAddress}</div>\n        <div class=\"business-info\">Tel: ${companyPhone}</div>\n        ${companyEmail ? `<div class=\"business-info\">Email: ${companyEmail}</div>` : ''}\n        ${operatingHours ? `<div class=\"business-info\">${operatingHours.replace(/\\n/g, '<br>')}</div>` : ''}\n      </div>\n      \n      <div class=\"receipt-info\">\n        <div><strong>Receipt #:</strong> ${transaction.id.substring(0, 8).toUpperCase()}</div>\n        <div><strong>Date:</strong> ${receiptDate}</div>\n        <div><strong>Attendant:</strong> ${attendant.name}</div>\n        ${transaction.customerId ? `<div><strong>Customer:</strong> Credit Sale</div>` : ''}\n      </div>\n      \n      <table class=\"items-table\">\n        <thead>\n          <tr>\n            <th class=\"item-name\">Item</th>\n            <th class=\"item-qty\">Qty</th>\n            <th class=\"item-price\">Amount</th>\n          </tr>\n        </thead>\n        <tbody>\n          ${transaction.items.map(item => `\n            <tr>\n              <td class=\"item-name\">\n                ${item.name}\n                ${item.notes ? `<br><small style=\"font-size: 9px; color: #666;\">${item.notes}</small>` : ''}\n              </td>\n              <td class=\"item-qty\">${item.quantity}</td>\n              <td class=\"item-price\">${currency} ${item.totalPrice.toLocaleString()}</td>\n            </tr>\n          `).join('')}\n        </tbody>\n      </table>\n      \n      <div class=\"totals\">\n        <div class=\"total-line\">\n          <span>Subtotal:</span>\n          <span>${currency} ${transaction.subtotal.toLocaleString()}</span>\n        </div>\n        ${transaction.discount && transaction.discount > 0 ? `\n          <div class=\"total-line\">\n            <span>Discount:</span>\n            <span>-${currency} ${transaction.discount.toLocaleString()}</span>\n          </div>\n        ` : ''}\n        <div class=\"total-line final\">\n          <span>TOTAL:</span>\n          <span>${currency} ${transaction.total.toLocaleString()}</span>\n        </div>\n      </div>\n      \n      <div class=\"payment-info\">\n        <div><strong>Payment Method:</strong> ${getPaymentMethodName(transaction.paymentMethod)}</div>\n        ${transaction.notes ? `<div><strong>Notes:</strong> ${transaction.notes}</div>` : ''}\n      </div>\n      \n      <div class=\"footer\">\n        <div>${receiptFooter.replace(/\\n/g, '<br>')}</div>\n        <div style=\"margin-top: 10px; font-size: 9px;\">\n          Powered by Cyber POS System\n        </div>\n      </div>\n      \n      <button class=\"print-button no-print\" onclick=\"window.print()\">\n        Print Receipt\n      </button>\n      \n      <script>\n        // Auto-focus for printing\n        window.onload = function() {\n          window.focus();\n        };\n      </script>\n    </body>\n    </html>\n  `;\n};\n\nconst getPaymentMethodName = (method: string): string => {\n  switch (method) {\n    case 'cash':\n      return 'Cash';\n    case 'mpesa':\n      return 'M-PESA';\n    case 'debt':\n      return 'Credit/Debt';\n    default:\n      return method;\n  }\n};\n\nexport const printReceipt = (receiptData: ReceiptData): void => {\n  const receiptHTML = generateReceiptHTML(receiptData);\n  const printWindow = window.open('', '_blank', 'width=400,height=600');\n  \n  if (printWindow) {\n    printWindow.document.write(receiptHTML);\n    printWindow.document.close();\n    \n    // Wait for content to load then print\n    printWindow.onload = () => {\n      setTimeout(() => {\n        printWindow.print();\n      }, 250);\n    };\n  } else {\n    alert('Please allow popups to print receipts');\n  }\n};\n\nexport const downloadReceiptPDF = async (receiptData: ReceiptData): Promise<void> => {\n  // This would require a PDF library like jsPDF or html2pdf\n  // For now, we'll just open the receipt in a new window\n  const receiptHTML = generateReceiptHTML(receiptData);\n  const printWindow = window.open('', '_blank');\n\n  if (printWindow) {\n    printWindow.document.write(receiptHTML);\n    printWindow.document.close();\n  }\n};\n\n/**\n * Create receipt data with business settings\n */\nexport const createReceiptData = async (\n  transaction: Transaction,\n  attendant: User\n): Promise<ReceiptData> => {\n  try {\n    const businessSettings = await getBusinessSettings();\n    return {\n      transaction,\n      attendant,\n      businessSettings: businessSettings || undefined,\n    };\n  } catch (error) {\n    console.error('Error loading business settings for receipt:', error);\n    // Fallback to default business info\n    return {\n      transaction,\n      attendant,\n      businessInfo: {\n        name: 'Cyber Services & Stationery',\n        address: 'Your Business Address',\n        phone: '+254 700 000 000',\n        email: '<EMAIL>',\n      },\n    };\n  }\n};\n\n/**\n * Print receipt with business settings\n */\nexport const printReceiptWithSettings = async (\n  transaction: Transaction,\n  attendant: User\n): Promise<void> => {\n  const receiptData = await createReceiptData(transaction, attendant);\n  printReceipt(receiptData);\n};\n\n/**\n * Download receipt PDF with business settings\n */\nexport const downloadReceiptPDFWithSettings = async (\n  transaction: Transaction,\n  attendant: User\n): Promise<void> => {\n  const receiptData = await createReceiptData(transaction, attendant);\n  await downloadReceiptPDF(receiptData);\n};\n"], "mappings": "AACA,OAASA,mBAAmB,KAAQ,qCAAqC,CAczE,MAAO,MAAM,CAAAC,mBAAmB,CAAIC,WAAwB,EAAa,CACvE,KAAM,CAAEC,WAAW,CAAEC,SAAS,CAAEC,YAAY,CAAEC,gBAAiB,CAAC,CAAGJ,WAAW,CAC9E,KAAM,CAAAK,WAAW,CAAGJ,WAAW,CAACK,SAAS,CAACC,cAAc,CAAC,CAAC,CAE1D;AACA,KAAM,CAAAC,WAAW,CAAG,CAAAJ,gBAAgB,SAAhBA,gBAAgB,iBAAhBA,gBAAgB,CAAEK,YAAY,IAAIN,YAAY,SAAZA,YAAY,iBAAZA,YAAY,CAAEO,IAAI,GAAI,eAAe,CAC3F,KAAM,CAAAC,cAAc,CAAG,CAAAP,gBAAgB,SAAhBA,gBAAgB,iBAAhBA,gBAAgB,CAAEQ,OAAO,IAAIT,YAAY,SAAZA,YAAY,iBAAZA,YAAY,CAAES,OAAO,GAAI,kBAAkB,CAC/F,KAAM,CAAAC,YAAY,CAAG,CAAAT,gBAAgB,SAAhBA,gBAAgB,iBAAhBA,gBAAgB,CAAEU,KAAK,IAAIX,YAAY,SAAZA,YAAY,iBAAZA,YAAY,CAAEW,KAAK,GAAI,cAAc,CACrF,KAAM,CAAAC,YAAY,CAAG,CAAAX,gBAAgB,SAAhBA,gBAAgB,iBAAhBA,gBAAgB,CAAEY,KAAK,IAAIb,YAAY,SAAZA,YAAY,iBAAZA,YAAY,CAAEa,KAAK,EACnE,KAAM,CAAAC,QAAQ,CAAG,CAAAb,gBAAgB,SAAhBA,gBAAgB,iBAAhBA,gBAAgB,CAAEa,QAAQ,GAAI,KAAK,CACpD,KAAM,CAAAC,OAAO,CAAGd,gBAAgB,SAAhBA,gBAAgB,iBAAhBA,gBAAgB,CAAEc,OAAO,CACzC,KAAM,CAAAC,aAAa,CAAGf,gBAAgB,SAAhBA,gBAAgB,iBAAhBA,gBAAgB,CAAEe,aAAa,CACrD,KAAM,CAAAC,aAAa,CAAG,CAAAhB,gBAAgB,SAAhBA,gBAAgB,iBAAhBA,gBAAgB,CAAEgB,aAAa,GAAI,8BAA8B,CACvF,KAAM,CAAAC,cAAc,CAAGjB,gBAAgB,SAAhBA,gBAAgB,iBAAhBA,gBAAgB,CAAEiB,cAAc,CAEvD,+GAAAC,MAAA,CAKuBrB,WAAW,CAACsB,EAAE,sgGAAAD,MAAA,CAuI7BJ,OAAO,eAAAI,MAAA,CAAgBJ,OAAO,8CAAyC,EAAE,4CAAAI,MAAA,CAC9Cd,WAAW,qBAAAc,MAAA,CACtCH,aAAa,0CAAAG,MAAA,CAA0CH,aAAa,WAAW,EAAE,4CAAAG,MAAA,CACtDX,cAAc,uDAAAW,MAAA,CACTT,YAAY,qBAAAS,MAAA,CAC5CP,YAAY,wCAAAO,MAAA,CAAwCP,YAAY,WAAW,EAAE,eAAAO,MAAA,CAC7ED,cAAc,iCAAAC,MAAA,CAAiCD,cAAc,CAACG,OAAO,CAAC,KAAK,CAAE,MAAM,CAAC,WAAW,EAAE,0GAAAF,MAAA,CAIhErB,WAAW,CAACsB,EAAE,CAACE,SAAS,CAAC,CAAC,CAAE,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,iDAAAJ,MAAA,CACjDjB,WAAW,sDAAAiB,MAAA,CACNpB,SAAS,CAACQ,IAAI,qBAAAY,MAAA,CAC/CrB,WAAW,CAAC0B,UAAU,qDAAyD,EAAE,+SAAAL,MAAA,CAY/ErB,WAAW,CAAC2B,KAAK,CAACC,GAAG,CAACC,IAAI,iFAAAR,MAAA,CAGpBQ,IAAI,CAACpB,IAAI,uBAAAY,MAAA,CACTQ,IAAI,CAACC,KAAK,sDAAAT,MAAA,CAAsDQ,IAAI,CAACC,KAAK,aAAa,EAAE,iEAAAT,MAAA,CAEtEQ,IAAI,CAACE,QAAQ,mDAAAV,MAAA,CACXL,QAAQ,MAAAK,MAAA,CAAIQ,IAAI,CAACG,UAAU,CAAC1B,cAAc,CAAC,CAAC,wCAExE,CAAC,CAAC2B,IAAI,CAAC,EAAE,CAAC,qKAAAZ,MAAA,CAOHL,QAAQ,MAAAK,MAAA,CAAIrB,WAAW,CAACkC,QAAQ,CAAC5B,cAAc,CAAC,CAAC,sCAAAe,MAAA,CAEzDrB,WAAW,CAACmC,QAAQ,EAAInC,WAAW,CAACmC,QAAQ,CAAG,CAAC,mGAAAd,MAAA,CAGrCL,QAAQ,MAAAK,MAAA,CAAIrB,WAAW,CAACmC,QAAQ,CAAC7B,cAAc,CAAC,CAAC,wCAE1D,EAAE,gGAAAe,MAAA,CAGIL,QAAQ,MAAAK,MAAA,CAAIrB,WAAW,CAACoC,KAAK,CAAC9B,cAAc,CAAC,CAAC,sIAAAe,MAAA,CAKhBgB,oBAAoB,CAACrC,WAAW,CAACsC,aAAa,CAAC,qBAAAjB,MAAA,CACrFrB,WAAW,CAAC8B,KAAK,iCAAAT,MAAA,CAAmCrB,WAAW,CAAC8B,KAAK,WAAW,EAAE,wEAAAT,MAAA,CAI7EF,aAAa,CAACI,OAAO,CAAC,KAAK,CAAE,MAAM,CAAC,8bAmBnD,CAAC,CAED,KAAM,CAAAc,oBAAoB,CAAIE,MAAc,EAAa,CACvD,OAAQA,MAAM,EACZ,IAAK,MAAM,CACT,MAAO,MAAM,CACf,IAAK,OAAO,CACV,MAAO,QAAQ,CACjB,IAAK,MAAM,CACT,MAAO,aAAa,CACtB,QACE,MAAO,CAAAA,MAAM,CACjB,CACF,CAAC,CAED,MAAO,MAAM,CAAAC,YAAY,CAAIzC,WAAwB,EAAW,CAC9D,KAAM,CAAA0C,WAAW,CAAG3C,mBAAmB,CAACC,WAAW,CAAC,CACpD,KAAM,CAAA2C,WAAW,CAAGC,MAAM,CAACC,IAAI,CAAC,EAAE,CAAE,QAAQ,CAAE,sBAAsB,CAAC,CAErE,GAAIF,WAAW,CAAE,CACfA,WAAW,CAACG,QAAQ,CAACC,KAAK,CAACL,WAAW,CAAC,CACvCC,WAAW,CAACG,QAAQ,CAACE,KAAK,CAAC,CAAC,CAE5B;AACAL,WAAW,CAACM,MAAM,CAAG,IAAM,CACzBC,UAAU,CAAC,IAAM,CACfP,WAAW,CAACQ,KAAK,CAAC,CAAC,CACrB,CAAC,CAAE,GAAG,CAAC,CACT,CAAC,CACH,CAAC,IAAM,CACLC,KAAK,CAAC,uCAAuC,CAAC,CAChD,CACF,CAAC,CAED,MAAO,MAAM,CAAAC,kBAAkB,CAAG,KAAO,CAAArD,WAAwB,EAAoB,CACnF;AACA;AACA,KAAM,CAAA0C,WAAW,CAAG3C,mBAAmB,CAACC,WAAW,CAAC,CACpD,KAAM,CAAA2C,WAAW,CAAGC,MAAM,CAACC,IAAI,CAAC,EAAE,CAAE,QAAQ,CAAC,CAE7C,GAAIF,WAAW,CAAE,CACfA,WAAW,CAACG,QAAQ,CAACC,KAAK,CAACL,WAAW,CAAC,CACvCC,WAAW,CAACG,QAAQ,CAACE,KAAK,CAAC,CAAC,CAC9B,CACF,CAAC,CAED;AACA;AACA,GACA,MAAO,MAAM,CAAAM,iBAAiB,CAAG,KAAAA,CAC/BrD,WAAwB,CACxBC,SAAe,GACU,CACzB,GAAI,CACF,KAAM,CAAAE,gBAAgB,CAAG,KAAM,CAAAN,mBAAmB,CAAC,CAAC,CACpD,MAAO,CACLG,WAAW,CACXC,SAAS,CACTE,gBAAgB,CAAEA,gBAAgB,EAAImD,SACxC,CAAC,CACH,CAAE,MAAOC,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,8CAA8C,CAAEA,KAAK,CAAC,CACpE;AACA,MAAO,CACLvD,WAAW,CACXC,SAAS,CACTC,YAAY,CAAE,CACZO,IAAI,CAAE,6BAA6B,CACnCE,OAAO,CAAE,uBAAuB,CAChCE,KAAK,CAAE,kBAAkB,CACzBE,KAAK,CAAE,uBACT,CACF,CAAC,CACH,CACF,CAAC,CAED;AACA;AACA,GACA,MAAO,MAAM,CAAA0C,wBAAwB,CAAG,KAAAA,CACtCzD,WAAwB,CACxBC,SAAe,GACG,CAClB,KAAM,CAAAF,WAAW,CAAG,KAAM,CAAAsD,iBAAiB,CAACrD,WAAW,CAAEC,SAAS,CAAC,CACnEuC,YAAY,CAACzC,WAAW,CAAC,CAC3B,CAAC,CAED;AACA;AACA,GACA,MAAO,MAAM,CAAA2D,8BAA8B,CAAG,KAAAA,CAC5C1D,WAAwB,CACxBC,SAAe,GACG,CAClB,KAAM,CAAAF,WAAW,CAAG,KAAM,CAAAsD,iBAAiB,CAACrD,WAAW,CAAEC,SAAS,CAAC,CACnE,KAAM,CAAAmD,kBAAkB,CAACrD,WAAW,CAAC,CACvC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}