// Service Worker for Cyber POS System
const CACHE_NAME = 'cyber-pos-v1.0.0';
const STATIC_CACHE_NAME = 'cyber-pos-static-v1.0.0';
const DYNAMIC_CACHE_NAME = 'cyber-pos-dynamic-v1.0.0';

// Files to cache for offline functionality
const STATIC_FILES = [
  '/',
  '/static/js/bundle.js',
  '/static/css/main.css',
  '/manifest.json',
  '/favicon.ico'
];

// Install event - cache static files
self.addEventListener('install', (event) => {
  console.log('Service Worker: Installing...');

  event.waitUntil(
    caches.open(STATIC_CACHE_NAME)
      .then((cache) => {
        console.log('Service Worker: Caching static files');
        return cache.addAll(STATIC_FILES);
      })
      .then(() => {
        console.log('Service Worker: Static files cached');
        return self.skipWaiting();
      })
      .catch((error) => {
        console.error('Service Worker: Error caching static files', error);
      })
  );
});

// Activate event - clean up old caches
self.addEventListener('activate', (event) => {
  console.log('Service Worker: Activating...');

  event.waitUntil(
    caches.keys()
      .then((cacheNames) => {
        return Promise.all(
          cacheNames.map((cacheName) => {
            if (cacheName !== STATIC_CACHE_NAME &&
                cacheName !== DYNAMIC_CACHE_NAME &&
                cacheName !== CACHE_NAME) {
              console.log('Service Worker: Deleting old cache', cacheName);
              return caches.delete(cacheName);
            }
          })
        );
      })
      .then(() => {
        console.log('Service Worker: Activated');
        return self.clients.claim();
      })
  );
});

// Fetch event - serve cached content when offline
self.addEventListener('fetch', (event) => {
  const { request } = event;
  const url = new URL(request.url);

  // Handle different types of requests
  if (request.method === 'GET') {
    // Static files - cache first strategy
    if (STATIC_FILES.some(file => request.url.includes(file))) {
      event.respondWith(cacheFirst(request));
    }
    // API requests - network first with cache fallback
    else if (url.pathname.startsWith('/api/') ||
             request.url.includes('firestore.googleapis.com') ||
             request.url.includes('firebase.googleapis.com')) {
      event.respondWith(networkFirstWithCache(request));
    }
    // Other requests - network first
    else {
      event.respondWith(networkFirst(request));
    }
  }
  // POST/PUT/DELETE requests - handle offline queue
  else if (['POST', 'PUT', 'DELETE'].includes(request.method)) {
    event.respondWith(handleOfflineWrite(request));
  }
});

// Cache first strategy - for static files
async function cacheFirst(request) {
  try {
    const cachedResponse = await caches.match(request);
    if (cachedResponse) {
      return cachedResponse;
    }

    const networkResponse = await fetch(request);
    if (networkResponse.ok) {
      const cache = await caches.open(STATIC_CACHE_NAME);
      cache.put(request, networkResponse.clone());
    }
    return networkResponse;
  } catch (error) {
    console.error('Cache first strategy failed:', error);
    return new Response('Offline - content not available', { status: 503 });
  }
}

// Network first with cache fallback - for API requests
async function networkFirstWithCache(request) {
  try {
    const networkResponse = await fetch(request);

    if (networkResponse.ok) {
      // Cache successful responses
      const cache = await caches.open(DYNAMIC_CACHE_NAME);
      cache.put(request, networkResponse.clone());
      return networkResponse;
    }

    // If network fails, try cache
    const cachedResponse = await caches.match(request);
    if (cachedResponse) {
      const response = cachedResponse.clone();
      response.headers.set('X-Served-From', 'cache');
      return response;
    }

    return networkResponse;
  } catch (error) {
    // Network failed, try cache
    const cachedResponse = await caches.match(request);
    if (cachedResponse) {
      const response = cachedResponse.clone();
      response.headers.set('X-Served-From', 'cache');
      return response;
    }

    // Return offline response
    return new Response(
      JSON.stringify({
        error: 'Offline - data not available',
        offline: true
      }),
      {
        status: 503,
        headers: { 'Content-Type': 'application/json' }
      }
    );
  }
}

// Network first strategy - for other requests
async function networkFirst(request) {
  try {
    const networkResponse = await fetch(request);
    return networkResponse;
  } catch (error) {
    const cachedResponse = await caches.match(request);
    if (cachedResponse) {
      return cachedResponse;
    }

    return new Response('Offline - content not available', { status: 503 });
  }
}

// Handle offline write operations
async function handleOfflineWrite(request) {
  try {
    // Try network first
    const networkResponse = await fetch(request);
    return networkResponse;
  } catch (error) {
    // If offline, queue the request
    await queueOfflineRequest(request);

    // Return a response indicating the request was queued
    return new Response(
      JSON.stringify({
        success: true,
        message: 'Request queued for when online',
        queued: true,
        timestamp: Date.now()
      }),
      {
        status: 202, // Accepted
        headers: { 'Content-Type': 'application/json' }
      }
    );
  }
}

// Queue offline requests for later sync
async function queueOfflineRequest(request) {
  const requestData = {
    url: request.url,
    method: request.method,
    headers: Object.fromEntries(request.headers.entries()),
    body: request.method !== 'GET' ? await request.text() : null,
    timestamp: Date.now()
  };

  // Store in localStorage
  const queue = await getOfflineQueue();
  queue.push(requestData);
  await saveOfflineQueue(queue);
}

// Get offline queue from storage
async function getOfflineQueue() {
  try {
    const stored = localStorage.getItem('offline-queue');
    return stored ? JSON.parse(stored) : [];
  } catch (error) {
    console.error('Error getting offline queue:', error);
    return [];
  }
}

// Save offline queue to storage
async function saveOfflineQueue(queue) {
  try {
    localStorage.setItem('offline-queue', JSON.stringify(queue));
  } catch (error) {
    console.error('Error saving offline queue:', error);
  }
}

// Process offline queue when back online
async function processOfflineQueue() {
  const queue = await getOfflineQueue();
  const processedRequests = [];

  for (const requestData of queue) {
    try {
      const request = new Request(requestData.url, {
        method: requestData.method,
        headers: requestData.headers,
        body: requestData.body
      });

      const response = await fetch(request);
      if (response.ok) {
        processedRequests.push(requestData);
        console.log('Offline request processed:', requestData.url);
      }
    } catch (error) {
      console.error('Error processing offline request:', error);
    }
  }

  // Remove processed requests from queue
  const remainingQueue = queue.filter(req =>
    !processedRequests.some(processed =>
      processed.url === req.url && processed.timestamp === req.timestamp
    )
  );

  await saveOfflineQueue(remainingQueue);

  // Notify clients about sync completion
  if (processedRequests.length > 0) {
    self.clients.matchAll().then(clients => {
      clients.forEach(client => {
        client.postMessage({
          type: 'OFFLINE_SYNC_COMPLETE',
          processedCount: processedRequests.length
        });
      });
    });
  }
}

// Background sync event
self.addEventListener('sync', (event) => {
  if (event.tag === 'offline-sync') {
    event.waitUntil(processOfflineQueue());
  }
});

// Message event - handle messages from main thread
self.addEventListener('message', (event) => {
  if (event.data && event.data.type === 'SKIP_WAITING') {
    self.skipWaiting();
  } else if (event.data && event.data.type === 'PROCESS_OFFLINE_QUEUE') {
    processOfflineQueue();
  }
});

console.log('Service Worker: Loaded');
