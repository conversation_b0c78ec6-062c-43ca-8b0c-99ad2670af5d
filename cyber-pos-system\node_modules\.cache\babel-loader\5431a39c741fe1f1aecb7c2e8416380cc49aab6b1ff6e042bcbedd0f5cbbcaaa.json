{"ast": null, "code": "import { doc, getDoc, setDoc } from 'firebase/firestore';\nimport { ref, uploadBytes, getDownloadURL, deleteObject } from 'firebase/storage';\nimport { db, storage } from '../config/firebase';\nconst SETTINGS_COLLECTION = 'settings';\nconst BUSINESS_SETTINGS_DOC = 'business';\nconst LOGO_STORAGE_PATH = 'business/logos';\n\n/**\n * Get business settings from Firestore\n */\nexport const getBusinessSettings = async () => {\n  try {\n    const docRef = doc(db, SETTINGS_COLLECTION, BUSINESS_SETTINGS_DOC);\n    const docSnap = await getDoc(docRef);\n    if (docSnap.exists()) {\n      var _data$createdAt, _data$updatedAt;\n      const data = docSnap.data();\n      return {\n        ...data,\n        createdAt: ((_data$createdAt = data.createdAt) === null || _data$createdAt === void 0 ? void 0 : _data$createdAt.toDate()) || new Date(),\n        updatedAt: ((_data$updatedAt = data.updatedAt) === null || _data$updatedAt === void 0 ? void 0 : _data$updatedAt.toDate()) || new Date()\n      };\n    }\n    return null;\n  } catch (error) {\n    console.error('Error fetching business settings:', error);\n    throw new Error('Failed to fetch business settings');\n  }\n};\n\n/**\n * Save business settings to Firestore\n */\nexport const saveBusinessSettings = async settings => {\n  try {\n    var _existingDoc$data, _existingDoc$data$cre;\n    const now = new Date();\n    const docRef = doc(db, SETTINGS_COLLECTION, BUSINESS_SETTINGS_DOC);\n\n    // Check if document exists to determine if this is an update or create\n    const existingDoc = await getDoc(docRef);\n    const isUpdate = existingDoc.exists();\n    const businessSettings = {\n      id: BUSINESS_SETTINGS_DOC,\n      ...settings,\n      createdAt: isUpdate ? ((_existingDoc$data = existingDoc.data()) === null || _existingDoc$data === void 0 ? void 0 : (_existingDoc$data$cre = _existingDoc$data.createdAt) === null || _existingDoc$data$cre === void 0 ? void 0 : _existingDoc$data$cre.toDate()) || now : now,\n      updatedAt: now\n    };\n    await setDoc(docRef, {\n      ...businessSettings,\n      createdAt: businessSettings.createdAt,\n      updatedAt: businessSettings.updatedAt\n    });\n    return businessSettings;\n  } catch (error) {\n    console.error('Error saving business settings:', error);\n    throw new Error('Failed to save business settings');\n  }\n};\n\n/**\n * Upload logo file to Firebase Storage\n */\nexport const uploadLogo = async file => {\n  try {\n    // Validate file type\n    if (!file.type.startsWith('image/')) {\n      throw new Error('Please select an image file');\n    }\n\n    // Validate file size (max 5MB)\n    const maxSize = 5 * 1024 * 1024; // 5MB\n    if (file.size > maxSize) {\n      throw new Error('Image file size must be less than 5MB');\n    }\n\n    // Generate unique filename\n    const timestamp = Date.now();\n    const fileName = `logo_${timestamp}_${file.name}`;\n    const logoRef = ref(storage, `${LOGO_STORAGE_PATH}/${fileName}`);\n\n    // Upload file\n    const uploadResult = await uploadBytes(logoRef, file);\n    const downloadURL = await getDownloadURL(uploadResult.ref);\n    return {\n      url: downloadURL,\n      fileName: fileName\n    };\n  } catch (error) {\n    console.error('Error uploading logo:', error);\n    throw new Error(error instanceof Error ? error.message : 'Failed to upload logo');\n  }\n};\n\n/**\n * Delete logo from Firebase Storage\n */\nexport const deleteLogo = async fileName => {\n  try {\n    const logoRef = ref(storage, `${LOGO_STORAGE_PATH}/${fileName}`);\n    await deleteObject(logoRef);\n  } catch (error) {\n    console.error('Error deleting logo:', error);\n    // Don't throw error for delete operations as the file might not exist\n  }\n};\n\n/**\n * Get default business settings\n */\nexport const getDefaultBusinessSettings = () => {\n  return {\n    businessName: 'Cyber Services & Stationery',\n    address: 'Your Business Address',\n    phone: '+*********** 000',\n    email: '<EMAIL>',\n    currency: 'KSh',\n    taxRate: 16,\n    receiptFooter: 'Thank you for your business!\\nVisit us again soon',\n    operatingHours: 'Mon-Fri: 8:00 AM - 6:00 PM\\nSat: 9:00 AM - 4:00 PM'\n  };\n};\n\n/**\n * Initialize business settings with defaults if none exist\n */\nexport const initializeBusinessSettings = async () => {\n  try {\n    const existing = await getBusinessSettings();\n    if (existing) {\n      return existing;\n    }\n    const defaultSettings = getDefaultBusinessSettings();\n    return await saveBusinessSettings(defaultSettings);\n  } catch (error) {\n    console.error('Error initializing business settings:', error);\n    throw new Error('Failed to initialize business settings');\n  }\n};", "map": {"version": 3, "names": ["doc", "getDoc", "setDoc", "ref", "uploadBytes", "getDownloadURL", "deleteObject", "db", "storage", "SETTINGS_COLLECTION", "BUSINESS_SETTINGS_DOC", "LOGO_STORAGE_PATH", "getBusinessSettings", "doc<PERSON>ef", "docSnap", "exists", "_data$createdAt", "_data$updatedAt", "data", "createdAt", "toDate", "Date", "updatedAt", "error", "console", "Error", "saveBusinessSettings", "settings", "_existingDoc$data", "_existingDoc$data$cre", "now", "existingDoc", "isUpdate", "businessSettings", "id", "uploadLogo", "file", "type", "startsWith", "maxSize", "size", "timestamp", "fileName", "name", "logoRef", "uploadResult", "downloadURL", "url", "message", "deleteLogo", "getDefaultBusinessSettings", "businessName", "address", "phone", "email", "currency", "taxRate", "receiptFooter", "operatingHours", "initializeBusinessSettings", "existing", "defaultSettings"], "sources": ["E:/FX/Cyber POS/cyber-pos-system/src/services/businessSettingsService.ts"], "sourcesContent": ["import { \n  doc, \n  getDoc, \n  setDoc, \n  collection, \n  query, \n  getDocs, \n  limit \n} from 'firebase/firestore';\nimport { \n  ref, \n  uploadBytes, \n  getDownloadURL, \n  deleteObject \n} from 'firebase/storage';\nimport { db, storage } from '../config/firebase';\nimport { BusinessSettings } from '../types';\n\nconst SETTINGS_COLLECTION = 'settings';\nconst BUSINESS_SETTINGS_DOC = 'business';\nconst LOGO_STORAGE_PATH = 'business/logos';\n\n/**\n * Get business settings from Firestore\n */\nexport const getBusinessSettings = async (): Promise<BusinessSettings | null> => {\n  try {\n    const docRef = doc(db, SETTINGS_COLLECTION, BUSINESS_SETTINGS_DOC);\n    const docSnap = await getDoc(docRef);\n    \n    if (docSnap.exists()) {\n      const data = docSnap.data();\n      return {\n        ...data,\n        createdAt: data.createdAt?.toDate() || new Date(),\n        updatedAt: data.updatedAt?.toDate() || new Date(),\n      } as BusinessSettings;\n    }\n    \n    return null;\n  } catch (error) {\n    console.error('Error fetching business settings:', error);\n    throw new Error('Failed to fetch business settings');\n  }\n};\n\n/**\n * Save business settings to Firestore\n */\nexport const saveBusinessSettings = async (settings: Omit<BusinessSettings, 'id' | 'createdAt' | 'updatedAt'>): Promise<BusinessSettings> => {\n  try {\n    const now = new Date();\n    const docRef = doc(db, SETTINGS_COLLECTION, BUSINESS_SETTINGS_DOC);\n    \n    // Check if document exists to determine if this is an update or create\n    const existingDoc = await getDoc(docRef);\n    const isUpdate = existingDoc.exists();\n    \n    const businessSettings: BusinessSettings = {\n      id: BUSINESS_SETTINGS_DOC,\n      ...settings,\n      createdAt: isUpdate ? existingDoc.data()?.createdAt?.toDate() || now : now,\n      updatedAt: now,\n    };\n    \n    await setDoc(docRef, {\n      ...businessSettings,\n      createdAt: businessSettings.createdAt,\n      updatedAt: businessSettings.updatedAt,\n    });\n    \n    return businessSettings;\n  } catch (error) {\n    console.error('Error saving business settings:', error);\n    throw new Error('Failed to save business settings');\n  }\n};\n\n/**\n * Upload logo file to Firebase Storage\n */\nexport const uploadLogo = async (file: File): Promise<{ url: string; fileName: string }> => {\n  try {\n    // Validate file type\n    if (!file.type.startsWith('image/')) {\n      throw new Error('Please select an image file');\n    }\n    \n    // Validate file size (max 5MB)\n    const maxSize = 5 * 1024 * 1024; // 5MB\n    if (file.size > maxSize) {\n      throw new Error('Image file size must be less than 5MB');\n    }\n    \n    // Generate unique filename\n    const timestamp = Date.now();\n    const fileName = `logo_${timestamp}_${file.name}`;\n    const logoRef = ref(storage, `${LOGO_STORAGE_PATH}/${fileName}`);\n    \n    // Upload file\n    const uploadResult = await uploadBytes(logoRef, file);\n    const downloadURL = await getDownloadURL(uploadResult.ref);\n    \n    return {\n      url: downloadURL,\n      fileName: fileName,\n    };\n  } catch (error) {\n    console.error('Error uploading logo:', error);\n    throw new Error(error instanceof Error ? error.message : 'Failed to upload logo');\n  }\n};\n\n/**\n * Delete logo from Firebase Storage\n */\nexport const deleteLogo = async (fileName: string): Promise<void> => {\n  try {\n    const logoRef = ref(storage, `${LOGO_STORAGE_PATH}/${fileName}`);\n    await deleteObject(logoRef);\n  } catch (error) {\n    console.error('Error deleting logo:', error);\n    // Don't throw error for delete operations as the file might not exist\n  }\n};\n\n/**\n * Get default business settings\n */\nexport const getDefaultBusinessSettings = (): Omit<BusinessSettings, 'id' | 'createdAt' | 'updatedAt'> => {\n  return {\n    businessName: 'Cyber Services & Stationery',\n    address: 'Your Business Address',\n    phone: '+*********** 000',\n    email: '<EMAIL>',\n    currency: 'KSh',\n    taxRate: 16,\n    receiptFooter: 'Thank you for your business!\\nVisit us again soon',\n    operatingHours: 'Mon-Fri: 8:00 AM - 6:00 PM\\nSat: 9:00 AM - 4:00 PM',\n  };\n};\n\n/**\n * Initialize business settings with defaults if none exist\n */\nexport const initializeBusinessSettings = async (): Promise<BusinessSettings> => {\n  try {\n    const existing = await getBusinessSettings();\n    if (existing) {\n      return existing;\n    }\n    \n    const defaultSettings = getDefaultBusinessSettings();\n    return await saveBusinessSettings(defaultSettings);\n  } catch (error) {\n    console.error('Error initializing business settings:', error);\n    throw new Error('Failed to initialize business settings');\n  }\n};\n"], "mappings": "AAAA,SACEA,GAAG,EACHC,MAAM,EACNC,MAAM,QAKD,oBAAoB;AAC3B,SACEC,GAAG,EACHC,WAAW,EACXC,cAAc,EACdC,YAAY,QACP,kBAAkB;AACzB,SAASC,EAAE,EAAEC,OAAO,QAAQ,oBAAoB;AAGhD,MAAMC,mBAAmB,GAAG,UAAU;AACtC,MAAMC,qBAAqB,GAAG,UAAU;AACxC,MAAMC,iBAAiB,GAAG,gBAAgB;;AAE1C;AACA;AACA;AACA,OAAO,MAAMC,mBAAmB,GAAG,MAAAA,CAAA,KAA8C;EAC/E,IAAI;IACF,MAAMC,MAAM,GAAGb,GAAG,CAACO,EAAE,EAAEE,mBAAmB,EAAEC,qBAAqB,CAAC;IAClE,MAAMI,OAAO,GAAG,MAAMb,MAAM,CAACY,MAAM,CAAC;IAEpC,IAAIC,OAAO,CAACC,MAAM,CAAC,CAAC,EAAE;MAAA,IAAAC,eAAA,EAAAC,eAAA;MACpB,MAAMC,IAAI,GAAGJ,OAAO,CAACI,IAAI,CAAC,CAAC;MAC3B,OAAO;QACL,GAAGA,IAAI;QACPC,SAAS,EAAE,EAAAH,eAAA,GAAAE,IAAI,CAACC,SAAS,cAAAH,eAAA,uBAAdA,eAAA,CAAgBI,MAAM,CAAC,CAAC,KAAI,IAAIC,IAAI,CAAC,CAAC;QACjDC,SAAS,EAAE,EAAAL,eAAA,GAAAC,IAAI,CAACI,SAAS,cAAAL,eAAA,uBAAdA,eAAA,CAAgBG,MAAM,CAAC,CAAC,KAAI,IAAIC,IAAI,CAAC;MAClD,CAAC;IACH;IAEA,OAAO,IAAI;EACb,CAAC,CAAC,OAAOE,KAAK,EAAE;IACdC,OAAO,CAACD,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;IACzD,MAAM,IAAIE,KAAK,CAAC,mCAAmC,CAAC;EACtD;AACF,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAMC,oBAAoB,GAAG,MAAOC,QAAkE,IAAgC;EAC3I,IAAI;IAAA,IAAAC,iBAAA,EAAAC,qBAAA;IACF,MAAMC,GAAG,GAAG,IAAIT,IAAI,CAAC,CAAC;IACtB,MAAMR,MAAM,GAAGb,GAAG,CAACO,EAAE,EAAEE,mBAAmB,EAAEC,qBAAqB,CAAC;;IAElE;IACA,MAAMqB,WAAW,GAAG,MAAM9B,MAAM,CAACY,MAAM,CAAC;IACxC,MAAMmB,QAAQ,GAAGD,WAAW,CAAChB,MAAM,CAAC,CAAC;IAErC,MAAMkB,gBAAkC,GAAG;MACzCC,EAAE,EAAExB,qBAAqB;MACzB,GAAGiB,QAAQ;MACXR,SAAS,EAAEa,QAAQ,GAAG,EAAAJ,iBAAA,GAAAG,WAAW,CAACb,IAAI,CAAC,CAAC,cAAAU,iBAAA,wBAAAC,qBAAA,GAAlBD,iBAAA,CAAoBT,SAAS,cAAAU,qBAAA,uBAA7BA,qBAAA,CAA+BT,MAAM,CAAC,CAAC,KAAIU,GAAG,GAAGA,GAAG;MAC1ER,SAAS,EAAEQ;IACb,CAAC;IAED,MAAM5B,MAAM,CAACW,MAAM,EAAE;MACnB,GAAGoB,gBAAgB;MACnBd,SAAS,EAAEc,gBAAgB,CAACd,SAAS;MACrCG,SAAS,EAAEW,gBAAgB,CAACX;IAC9B,CAAC,CAAC;IAEF,OAAOW,gBAAgB;EACzB,CAAC,CAAC,OAAOV,KAAK,EAAE;IACdC,OAAO,CAACD,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;IACvD,MAAM,IAAIE,KAAK,CAAC,kCAAkC,CAAC;EACrD;AACF,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAMU,UAAU,GAAG,MAAOC,IAAU,IAAiD;EAC1F,IAAI;IACF;IACA,IAAI,CAACA,IAAI,CAACC,IAAI,CAACC,UAAU,CAAC,QAAQ,CAAC,EAAE;MACnC,MAAM,IAAIb,KAAK,CAAC,6BAA6B,CAAC;IAChD;;IAEA;IACA,MAAMc,OAAO,GAAG,CAAC,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC;IACjC,IAAIH,IAAI,CAACI,IAAI,GAAGD,OAAO,EAAE;MACvB,MAAM,IAAId,KAAK,CAAC,uCAAuC,CAAC;IAC1D;;IAEA;IACA,MAAMgB,SAAS,GAAGpB,IAAI,CAACS,GAAG,CAAC,CAAC;IAC5B,MAAMY,QAAQ,GAAG,QAAQD,SAAS,IAAIL,IAAI,CAACO,IAAI,EAAE;IACjD,MAAMC,OAAO,GAAGzC,GAAG,CAACK,OAAO,EAAE,GAAGG,iBAAiB,IAAI+B,QAAQ,EAAE,CAAC;;IAEhE;IACA,MAAMG,YAAY,GAAG,MAAMzC,WAAW,CAACwC,OAAO,EAAER,IAAI,CAAC;IACrD,MAAMU,WAAW,GAAG,MAAMzC,cAAc,CAACwC,YAAY,CAAC1C,GAAG,CAAC;IAE1D,OAAO;MACL4C,GAAG,EAAED,WAAW;MAChBJ,QAAQ,EAAEA;IACZ,CAAC;EACH,CAAC,CAAC,OAAOnB,KAAK,EAAE;IACdC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;IAC7C,MAAM,IAAIE,KAAK,CAACF,KAAK,YAAYE,KAAK,GAAGF,KAAK,CAACyB,OAAO,GAAG,uBAAuB,CAAC;EACnF;AACF,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAMC,UAAU,GAAG,MAAOP,QAAgB,IAAoB;EACnE,IAAI;IACF,MAAME,OAAO,GAAGzC,GAAG,CAACK,OAAO,EAAE,GAAGG,iBAAiB,IAAI+B,QAAQ,EAAE,CAAC;IAChE,MAAMpC,YAAY,CAACsC,OAAO,CAAC;EAC7B,CAAC,CAAC,OAAOrB,KAAK,EAAE;IACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;IAC5C;EACF;AACF,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAM2B,0BAA0B,GAAGA,CAAA,KAAgE;EACxG,OAAO;IACLC,YAAY,EAAE,6BAA6B;IAC3CC,OAAO,EAAE,uBAAuB;IAChCC,KAAK,EAAE,kBAAkB;IACzBC,KAAK,EAAE,uBAAuB;IAC9BC,QAAQ,EAAE,KAAK;IACfC,OAAO,EAAE,EAAE;IACXC,aAAa,EAAE,mDAAmD;IAClEC,cAAc,EAAE;EAClB,CAAC;AACH,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAMC,0BAA0B,GAAG,MAAAA,CAAA,KAAuC;EAC/E,IAAI;IACF,MAAMC,QAAQ,GAAG,MAAMhD,mBAAmB,CAAC,CAAC;IAC5C,IAAIgD,QAAQ,EAAE;MACZ,OAAOA,QAAQ;IACjB;IAEA,MAAMC,eAAe,GAAGX,0BAA0B,CAAC,CAAC;IACpD,OAAO,MAAMxB,oBAAoB,CAACmC,eAAe,CAAC;EACpD,CAAC,CAAC,OAAOtC,KAAK,EAAE;IACdC,OAAO,CAACD,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;IAC7D,MAAM,IAAIE,KAAK,CAAC,wCAAwC,CAAC;EAC3D;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}