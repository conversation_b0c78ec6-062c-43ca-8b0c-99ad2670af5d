{"ast": null, "code": "import { getBusinessSettings } from '../services/businessSettingsService';\nexport const generateReceiptHTML = receiptData => {\n  const {\n    transaction,\n    attendant,\n    businessInfo,\n    businessSettings\n  } = receiptData;\n  const receiptDate = transaction.createdAt.toLocaleString();\n\n  // Use business settings if available, otherwise fall back to businessInfo\n  const companyName = (businessSettings === null || businessSettings === void 0 ? void 0 : businessSettings.businessName) || (businessInfo === null || businessInfo === void 0 ? void 0 : businessInfo.name) || 'Business Name';\n  const companyAddress = (businessSettings === null || businessSettings === void 0 ? void 0 : businessSettings.address) || (businessInfo === null || businessInfo === void 0 ? void 0 : businessInfo.address) || 'Business Address';\n  const companyPhone = (businessSettings === null || businessSettings === void 0 ? void 0 : businessSettings.phone) || (businessInfo === null || businessInfo === void 0 ? void 0 : businessInfo.phone) || 'Phone Number';\n  const companyEmail = (businessSettings === null || businessSettings === void 0 ? void 0 : businessSettings.email) || (businessInfo === null || businessInfo === void 0 ? void 0 : businessInfo.email);\n  const currency = (businessSettings === null || businessSettings === void 0 ? void 0 : businessSettings.currency) || 'KSh';\n  const logoUrl = businessSettings === null || businessSettings === void 0 ? void 0 : businessSettings.logoUrl;\n  const receiptHeader = businessSettings === null || businessSettings === void 0 ? void 0 : businessSettings.receiptHeader;\n  const receiptFooter = (businessSettings === null || businessSettings === void 0 ? void 0 : businessSettings.receiptFooter) || 'Thank you for your business!';\n  const operatingHours = businessSettings === null || businessSettings === void 0 ? void 0 : businessSettings.operatingHours;\n  return `\n    <!DOCTYPE html>\n    <html>\n    <head>\n      <meta charset=\"utf-8\">\n      <title>Receipt - ${transaction.id}</title>\n      <style>\n        @media print {\n          body { margin: 0; }\n          .no-print { display: none; }\n        }\n        \n        body {\n          font-family: 'Courier New', monospace;\n          font-size: 12px;\n          line-height: 1.4;\n          max-width: 300px;\n          margin: 0 auto;\n          padding: 10px;\n        }\n        \n        .header {\n          text-align: center;\n          border-bottom: 2px solid #000;\n          padding-bottom: 10px;\n          margin-bottom: 15px;\n        }\n\n        .logo {\n          max-width: 80px;\n          max-height: 80px;\n          margin: 0 auto 10px auto;\n          display: block;\n        }\n\n        .business-name {\n          font-size: 16px;\n          font-weight: bold;\n          margin-bottom: 5px;\n        }\n\n        .business-info {\n          font-size: 10px;\n          margin-bottom: 2px;\n        }\n\n        .receipt-header-message {\n          font-size: 11px;\n          font-style: italic;\n          margin-bottom: 5px;\n          color: #666;\n        }\n        \n        .receipt-info {\n          margin-bottom: 15px;\n          font-size: 10px;\n        }\n        \n        .items-table {\n          width: 100%;\n          border-collapse: collapse;\n          margin-bottom: 15px;\n        }\n        \n        .items-table th,\n        .items-table td {\n          text-align: left;\n          padding: 2px 0;\n          border-bottom: 1px dotted #ccc;\n        }\n        \n        .items-table th {\n          font-weight: bold;\n          border-bottom: 1px solid #000;\n        }\n        \n        .item-name {\n          width: 60%;\n        }\n        \n        .item-qty {\n          width: 15%;\n          text-align: center;\n        }\n        \n        .item-price {\n          width: 25%;\n          text-align: right;\n        }\n        \n        .totals {\n          border-top: 2px solid #000;\n          padding-top: 10px;\n          margin-bottom: 15px;\n        }\n        \n        .total-line {\n          display: flex;\n          justify-content: space-between;\n          margin-bottom: 3px;\n        }\n        \n        .total-line.final {\n          font-weight: bold;\n          font-size: 14px;\n          border-top: 1px solid #000;\n          padding-top: 5px;\n          margin-top: 5px;\n        }\n        \n        .payment-info {\n          margin-bottom: 15px;\n          font-size: 10px;\n        }\n        \n        .footer {\n          text-align: center;\n          border-top: 1px dotted #ccc;\n          padding-top: 10px;\n          font-size: 10px;\n        }\n        \n        .print-button {\n          background: #007bff;\n          color: white;\n          border: none;\n          padding: 10px 20px;\n          border-radius: 5px;\n          cursor: pointer;\n          margin: 20px auto;\n          display: block;\n        }\n        \n        .print-button:hover {\n          background: #0056b3;\n        }\n      </style>\n    </head>\n    <body>\n      <div class=\"header\">\n        ${logoUrl ? `<img src=\"${logoUrl}\" alt=\"Company Logo\" class=\"logo\" />` : ''}\n        <div class=\"business-name\">${companyName}</div>\n        ${receiptHeader ? `<div class=\"receipt-header-message\">${receiptHeader}</div>` : ''}\n        <div class=\"business-info\">${companyAddress}</div>\n        <div class=\"business-info\">Tel: ${companyPhone}</div>\n        ${companyEmail ? `<div class=\"business-info\">Email: ${companyEmail}</div>` : ''}\n        ${operatingHours ? `<div class=\"business-info\">${operatingHours.replace(/\\n/g, '<br>')}</div>` : ''}\n      </div>\n      \n      <div class=\"receipt-info\">\n        <div><strong>Receipt #:</strong> ${transaction.id.substring(0, 8).toUpperCase()}</div>\n        <div><strong>Date:</strong> ${receiptDate}</div>\n        <div><strong>Attendant:</strong> ${attendant.name}</div>\n        ${transaction.customerId ? `<div><strong>Customer:</strong> Credit Sale</div>` : ''}\n      </div>\n      \n      <table class=\"items-table\">\n        <thead>\n          <tr>\n            <th class=\"item-name\">Item</th>\n            <th class=\"item-qty\">Qty</th>\n            <th class=\"item-price\">Amount</th>\n          </tr>\n        </thead>\n        <tbody>\n          ${transaction.items.map(item => `\n            <tr>\n              <td class=\"item-name\">\n                ${item.name}\n                ${item.notes ? `<br><small style=\"font-size: 9px; color: #666;\">${item.notes}</small>` : ''}\n              </td>\n              <td class=\"item-qty\">${item.quantity}</td>\n              <td class=\"item-price\">${currency} ${item.totalPrice.toLocaleString()}</td>\n            </tr>\n          `).join('')}\n        </tbody>\n      </table>\n      \n      <div class=\"totals\">\n        <div class=\"total-line\">\n          <span>Subtotal:</span>\n          <span>${currency} ${transaction.subtotal.toLocaleString()}</span>\n        </div>\n        ${transaction.discount && transaction.discount > 0 ? `\n          <div class=\"total-line\">\n            <span>Discount:</span>\n            <span>-${currency} ${transaction.discount.toLocaleString()}</span>\n          </div>\n        ` : ''}\n        <div class=\"total-line final\">\n          <span>TOTAL:</span>\n          <span>${currency} ${transaction.total.toLocaleString()}</span>\n        </div>\n      </div>\n      \n      <div class=\"payment-info\">\n        <div><strong>Payment Method:</strong> ${getPaymentMethodName(transaction.paymentMethod)}</div>\n        ${transaction.notes ? `<div><strong>Notes:</strong> ${transaction.notes}</div>` : ''}\n      </div>\n      \n      <div class=\"footer\">\n        <div>${receiptFooter.replace(/\\n/g, '<br>')}</div>\n        <div style=\"margin-top: 10px; font-size: 9px;\">\n          Powered by Cyber POS System\n        </div>\n      </div>\n      \n      <button class=\"print-button no-print\" onclick=\"window.print()\">\n        Print Receipt\n      </button>\n      \n      <script>\n        // Auto-focus for printing\n        window.onload = function() {\n          window.focus();\n        };\n      </script>\n    </body>\n    </html>\n  `;\n};\nconst getPaymentMethodName = method => {\n  switch (method) {\n    case 'cash':\n      return 'Cash';\n    case 'mpesa':\n      return 'M-PESA';\n    case 'debt':\n      return 'Credit/Debt';\n    default:\n      return method;\n  }\n};\nexport const printReceipt = receiptData => {\n  const receiptHTML = generateReceiptHTML(receiptData);\n  const printWindow = window.open('', '_blank', 'width=400,height=600');\n  if (printWindow) {\n    printWindow.document.write(receiptHTML);\n    printWindow.document.close();\n\n    // Wait for content to load then print\n    printWindow.onload = () => {\n      setTimeout(() => {\n        printWindow.print();\n      }, 250);\n    };\n  } else {\n    alert('Please allow popups to print receipts');\n  }\n};\nexport const downloadReceiptPDF = async receiptData => {\n  // This would require a PDF library like jsPDF or html2pdf\n  // For now, we'll just open the receipt in a new window\n  const receiptHTML = generateReceiptHTML(receiptData);\n  const printWindow = window.open('', '_blank');\n  if (printWindow) {\n    printWindow.document.write(receiptHTML);\n    printWindow.document.close();\n  }\n};\n\n/**\n * Create receipt data with business settings\n */\nexport const createReceiptData = async (transaction, attendant) => {\n  try {\n    const businessSettings = await getBusinessSettings();\n    return {\n      transaction,\n      attendant,\n      businessSettings\n    };\n  } catch (error) {\n    console.error('Error loading business settings for receipt:', error);\n    // Fallback to default business info\n    return {\n      transaction,\n      attendant,\n      businessInfo: {\n        name: 'Cyber Services & Stationery',\n        address: 'Your Business Address',\n        phone: '+254 700 000 000',\n        email: '<EMAIL>'\n      }\n    };\n  }\n};\n\n/**\n * Print receipt with business settings\n */\nexport const printReceiptWithSettings = async (transaction, attendant) => {\n  const receiptData = await createReceiptData(transaction, attendant);\n  printReceipt(receiptData);\n};\n\n/**\n * Download receipt PDF with business settings\n */\nexport const downloadReceiptPDFWithSettings = async (transaction, attendant) => {\n  const receiptData = await createReceiptData(transaction, attendant);\n  await downloadReceiptPDF(receiptData);\n};", "map": {"version": 3, "names": ["getBusinessSettings", "generateReceiptHTML", "receiptData", "transaction", "attendant", "businessInfo", "businessSettings", "receiptDate", "createdAt", "toLocaleString", "companyName", "businessName", "name", "companyAddress", "address", "companyPhone", "phone", "companyEmail", "email", "currency", "logoUrl", "receiptHeader", "receiptFooter", "operatingHours", "id", "replace", "substring", "toUpperCase", "customerId", "items", "map", "item", "notes", "quantity", "totalPrice", "join", "subtotal", "discount", "total", "getPaymentMethodName", "paymentMethod", "method", "printReceipt", "receiptHTML", "printWindow", "window", "open", "document", "write", "close", "onload", "setTimeout", "print", "alert", "downloadReceiptPDF", "createReceiptData", "error", "console", "printReceiptWithSettings", "downloadReceiptPDFWithSettings"], "sources": ["E:/FX/Cyber POS/cyber-pos-system/src/utils/receiptGenerator.ts"], "sourcesContent": ["import { Transaction, User, BusinessSettings } from '../types';\nimport { getBusinessSettings } from '../services/businessSettingsService';\n\nexport interface ReceiptData {\n  transaction: Transaction;\n  attendant: User;\n  businessInfo?: {\n    name: string;\n    address: string;\n    phone: string;\n    email?: string;\n  };\n  businessSettings?: BusinessSettings;\n}\n\nexport const generateReceiptHTML = (receiptData: ReceiptData): string => {\n  const { transaction, attendant, businessInfo, businessSettings } = receiptData;\n  const receiptDate = transaction.createdAt.toLocaleString();\n\n  // Use business settings if available, otherwise fall back to businessInfo\n  const companyName = businessSettings?.businessName || businessInfo?.name || 'Business Name';\n  const companyAddress = businessSettings?.address || businessInfo?.address || 'Business Address';\n  const companyPhone = businessSettings?.phone || businessInfo?.phone || 'Phone Number';\n  const companyEmail = businessSettings?.email || businessInfo?.email;\n  const currency = businessSettings?.currency || 'KSh';\n  const logoUrl = businessSettings?.logoUrl;\n  const receiptHeader = businessSettings?.receiptHeader;\n  const receiptFooter = businessSettings?.receiptFooter || 'Thank you for your business!';\n  const operatingHours = businessSettings?.operatingHours;\n\n  return `\n    <!DOCTYPE html>\n    <html>\n    <head>\n      <meta charset=\"utf-8\">\n      <title>Receipt - ${transaction.id}</title>\n      <style>\n        @media print {\n          body { margin: 0; }\n          .no-print { display: none; }\n        }\n        \n        body {\n          font-family: 'Courier New', monospace;\n          font-size: 12px;\n          line-height: 1.4;\n          max-width: 300px;\n          margin: 0 auto;\n          padding: 10px;\n        }\n        \n        .header {\n          text-align: center;\n          border-bottom: 2px solid #000;\n          padding-bottom: 10px;\n          margin-bottom: 15px;\n        }\n\n        .logo {\n          max-width: 80px;\n          max-height: 80px;\n          margin: 0 auto 10px auto;\n          display: block;\n        }\n\n        .business-name {\n          font-size: 16px;\n          font-weight: bold;\n          margin-bottom: 5px;\n        }\n\n        .business-info {\n          font-size: 10px;\n          margin-bottom: 2px;\n        }\n\n        .receipt-header-message {\n          font-size: 11px;\n          font-style: italic;\n          margin-bottom: 5px;\n          color: #666;\n        }\n        \n        .receipt-info {\n          margin-bottom: 15px;\n          font-size: 10px;\n        }\n        \n        .items-table {\n          width: 100%;\n          border-collapse: collapse;\n          margin-bottom: 15px;\n        }\n        \n        .items-table th,\n        .items-table td {\n          text-align: left;\n          padding: 2px 0;\n          border-bottom: 1px dotted #ccc;\n        }\n        \n        .items-table th {\n          font-weight: bold;\n          border-bottom: 1px solid #000;\n        }\n        \n        .item-name {\n          width: 60%;\n        }\n        \n        .item-qty {\n          width: 15%;\n          text-align: center;\n        }\n        \n        .item-price {\n          width: 25%;\n          text-align: right;\n        }\n        \n        .totals {\n          border-top: 2px solid #000;\n          padding-top: 10px;\n          margin-bottom: 15px;\n        }\n        \n        .total-line {\n          display: flex;\n          justify-content: space-between;\n          margin-bottom: 3px;\n        }\n        \n        .total-line.final {\n          font-weight: bold;\n          font-size: 14px;\n          border-top: 1px solid #000;\n          padding-top: 5px;\n          margin-top: 5px;\n        }\n        \n        .payment-info {\n          margin-bottom: 15px;\n          font-size: 10px;\n        }\n        \n        .footer {\n          text-align: center;\n          border-top: 1px dotted #ccc;\n          padding-top: 10px;\n          font-size: 10px;\n        }\n        \n        .print-button {\n          background: #007bff;\n          color: white;\n          border: none;\n          padding: 10px 20px;\n          border-radius: 5px;\n          cursor: pointer;\n          margin: 20px auto;\n          display: block;\n        }\n        \n        .print-button:hover {\n          background: #0056b3;\n        }\n      </style>\n    </head>\n    <body>\n      <div class=\"header\">\n        ${logoUrl ? `<img src=\"${logoUrl}\" alt=\"Company Logo\" class=\"logo\" />` : ''}\n        <div class=\"business-name\">${companyName}</div>\n        ${receiptHeader ? `<div class=\"receipt-header-message\">${receiptHeader}</div>` : ''}\n        <div class=\"business-info\">${companyAddress}</div>\n        <div class=\"business-info\">Tel: ${companyPhone}</div>\n        ${companyEmail ? `<div class=\"business-info\">Email: ${companyEmail}</div>` : ''}\n        ${operatingHours ? `<div class=\"business-info\">${operatingHours.replace(/\\n/g, '<br>')}</div>` : ''}\n      </div>\n      \n      <div class=\"receipt-info\">\n        <div><strong>Receipt #:</strong> ${transaction.id.substring(0, 8).toUpperCase()}</div>\n        <div><strong>Date:</strong> ${receiptDate}</div>\n        <div><strong>Attendant:</strong> ${attendant.name}</div>\n        ${transaction.customerId ? `<div><strong>Customer:</strong> Credit Sale</div>` : ''}\n      </div>\n      \n      <table class=\"items-table\">\n        <thead>\n          <tr>\n            <th class=\"item-name\">Item</th>\n            <th class=\"item-qty\">Qty</th>\n            <th class=\"item-price\">Amount</th>\n          </tr>\n        </thead>\n        <tbody>\n          ${transaction.items.map(item => `\n            <tr>\n              <td class=\"item-name\">\n                ${item.name}\n                ${item.notes ? `<br><small style=\"font-size: 9px; color: #666;\">${item.notes}</small>` : ''}\n              </td>\n              <td class=\"item-qty\">${item.quantity}</td>\n              <td class=\"item-price\">${currency} ${item.totalPrice.toLocaleString()}</td>\n            </tr>\n          `).join('')}\n        </tbody>\n      </table>\n      \n      <div class=\"totals\">\n        <div class=\"total-line\">\n          <span>Subtotal:</span>\n          <span>${currency} ${transaction.subtotal.toLocaleString()}</span>\n        </div>\n        ${transaction.discount && transaction.discount > 0 ? `\n          <div class=\"total-line\">\n            <span>Discount:</span>\n            <span>-${currency} ${transaction.discount.toLocaleString()}</span>\n          </div>\n        ` : ''}\n        <div class=\"total-line final\">\n          <span>TOTAL:</span>\n          <span>${currency} ${transaction.total.toLocaleString()}</span>\n        </div>\n      </div>\n      \n      <div class=\"payment-info\">\n        <div><strong>Payment Method:</strong> ${getPaymentMethodName(transaction.paymentMethod)}</div>\n        ${transaction.notes ? `<div><strong>Notes:</strong> ${transaction.notes}</div>` : ''}\n      </div>\n      \n      <div class=\"footer\">\n        <div>${receiptFooter.replace(/\\n/g, '<br>')}</div>\n        <div style=\"margin-top: 10px; font-size: 9px;\">\n          Powered by Cyber POS System\n        </div>\n      </div>\n      \n      <button class=\"print-button no-print\" onclick=\"window.print()\">\n        Print Receipt\n      </button>\n      \n      <script>\n        // Auto-focus for printing\n        window.onload = function() {\n          window.focus();\n        };\n      </script>\n    </body>\n    </html>\n  `;\n};\n\nconst getPaymentMethodName = (method: string): string => {\n  switch (method) {\n    case 'cash':\n      return 'Cash';\n    case 'mpesa':\n      return 'M-PESA';\n    case 'debt':\n      return 'Credit/Debt';\n    default:\n      return method;\n  }\n};\n\nexport const printReceipt = (receiptData: ReceiptData): void => {\n  const receiptHTML = generateReceiptHTML(receiptData);\n  const printWindow = window.open('', '_blank', 'width=400,height=600');\n  \n  if (printWindow) {\n    printWindow.document.write(receiptHTML);\n    printWindow.document.close();\n    \n    // Wait for content to load then print\n    printWindow.onload = () => {\n      setTimeout(() => {\n        printWindow.print();\n      }, 250);\n    };\n  } else {\n    alert('Please allow popups to print receipts');\n  }\n};\n\nexport const downloadReceiptPDF = async (receiptData: ReceiptData): Promise<void> => {\n  // This would require a PDF library like jsPDF or html2pdf\n  // For now, we'll just open the receipt in a new window\n  const receiptHTML = generateReceiptHTML(receiptData);\n  const printWindow = window.open('', '_blank');\n\n  if (printWindow) {\n    printWindow.document.write(receiptHTML);\n    printWindow.document.close();\n  }\n};\n\n/**\n * Create receipt data with business settings\n */\nexport const createReceiptData = async (\n  transaction: Transaction,\n  attendant: User\n): Promise<ReceiptData> => {\n  try {\n    const businessSettings = await getBusinessSettings();\n    return {\n      transaction,\n      attendant,\n      businessSettings,\n    };\n  } catch (error) {\n    console.error('Error loading business settings for receipt:', error);\n    // Fallback to default business info\n    return {\n      transaction,\n      attendant,\n      businessInfo: {\n        name: 'Cyber Services & Stationery',\n        address: 'Your Business Address',\n        phone: '+254 700 000 000',\n        email: '<EMAIL>',\n      },\n    };\n  }\n};\n\n/**\n * Print receipt with business settings\n */\nexport const printReceiptWithSettings = async (\n  transaction: Transaction,\n  attendant: User\n): Promise<void> => {\n  const receiptData = await createReceiptData(transaction, attendant);\n  printReceipt(receiptData);\n};\n\n/**\n * Download receipt PDF with business settings\n */\nexport const downloadReceiptPDFWithSettings = async (\n  transaction: Transaction,\n  attendant: User\n): Promise<void> => {\n  const receiptData = await createReceiptData(transaction, attendant);\n  await downloadReceiptPDF(receiptData);\n};\n"], "mappings": "AACA,SAASA,mBAAmB,QAAQ,qCAAqC;AAczE,OAAO,MAAMC,mBAAmB,GAAIC,WAAwB,IAAa;EACvE,MAAM;IAAEC,WAAW;IAAEC,SAAS;IAAEC,YAAY;IAAEC;EAAiB,CAAC,GAAGJ,WAAW;EAC9E,MAAMK,WAAW,GAAGJ,WAAW,CAACK,SAAS,CAACC,cAAc,CAAC,CAAC;;EAE1D;EACA,MAAMC,WAAW,GAAG,CAAAJ,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEK,YAAY,MAAIN,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEO,IAAI,KAAI,eAAe;EAC3F,MAAMC,cAAc,GAAG,CAAAP,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEQ,OAAO,MAAIT,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAES,OAAO,KAAI,kBAAkB;EAC/F,MAAMC,YAAY,GAAG,CAAAT,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEU,KAAK,MAAIX,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEW,KAAK,KAAI,cAAc;EACrF,MAAMC,YAAY,GAAG,CAAAX,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEY,KAAK,MAAIb,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEa,KAAK;EACnE,MAAMC,QAAQ,GAAG,CAAAb,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEa,QAAQ,KAAI,KAAK;EACpD,MAAMC,OAAO,GAAGd,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEc,OAAO;EACzC,MAAMC,aAAa,GAAGf,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEe,aAAa;EACrD,MAAMC,aAAa,GAAG,CAAAhB,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEgB,aAAa,KAAI,8BAA8B;EACvF,MAAMC,cAAc,GAAGjB,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEiB,cAAc;EAEvD,OAAO;AACT;AACA;AACA;AACA;AACA,yBAAyBpB,WAAW,CAACqB,EAAE;AACvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAUJ,OAAO,GAAG,aAAaA,OAAO,sCAAsC,GAAG,EAAE;AACnF,qCAAqCV,WAAW;AAChD,UAAUW,aAAa,GAAG,uCAAuCA,aAAa,QAAQ,GAAG,EAAE;AAC3F,qCAAqCR,cAAc;AACnD,0CAA0CE,YAAY;AACtD,UAAUE,YAAY,GAAG,qCAAqCA,YAAY,QAAQ,GAAG,EAAE;AACvF,UAAUM,cAAc,GAAG,8BAA8BA,cAAc,CAACE,OAAO,CAAC,KAAK,EAAE,MAAM,CAAC,QAAQ,GAAG,EAAE;AAC3G;AACA;AACA;AACA,2CAA2CtB,WAAW,CAACqB,EAAE,CAACE,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;AACvF,sCAAsCpB,WAAW;AACjD,2CAA2CH,SAAS,CAACQ,IAAI;AACzD,UAAUT,WAAW,CAACyB,UAAU,GAAG,mDAAmD,GAAG,EAAE;AAC3F;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,YAAYzB,WAAW,CAAC0B,KAAK,CAACC,GAAG,CAACC,IAAI,IAAI;AAC1C;AACA;AACA,kBAAkBA,IAAI,CAACnB,IAAI;AAC3B,kBAAkBmB,IAAI,CAACC,KAAK,GAAG,mDAAmDD,IAAI,CAACC,KAAK,UAAU,GAAG,EAAE;AAC3G;AACA,qCAAqCD,IAAI,CAACE,QAAQ;AAClD,uCAAuCd,QAAQ,IAAIY,IAAI,CAACG,UAAU,CAACzB,cAAc,CAAC,CAAC;AACnF;AACA,WAAW,CAAC,CAAC0B,IAAI,CAAC,EAAE,CAAC;AACrB;AACA;AACA;AACA;AACA;AACA;AACA,kBAAkBhB,QAAQ,IAAIhB,WAAW,CAACiC,QAAQ,CAAC3B,cAAc,CAAC,CAAC;AACnE;AACA,UAAUN,WAAW,CAACkC,QAAQ,IAAIlC,WAAW,CAACkC,QAAQ,GAAG,CAAC,GAAG;AAC7D;AACA;AACA,qBAAqBlB,QAAQ,IAAIhB,WAAW,CAACkC,QAAQ,CAAC5B,cAAc,CAAC,CAAC;AACtE;AACA,SAAS,GAAG,EAAE;AACd;AACA;AACA,kBAAkBU,QAAQ,IAAIhB,WAAW,CAACmC,KAAK,CAAC7B,cAAc,CAAC,CAAC;AAChE;AACA;AACA;AACA;AACA,gDAAgD8B,oBAAoB,CAACpC,WAAW,CAACqC,aAAa,CAAC;AAC/F,UAAUrC,WAAW,CAAC6B,KAAK,GAAG,gCAAgC7B,WAAW,CAAC6B,KAAK,QAAQ,GAAG,EAAE;AAC5F;AACA;AACA;AACA,eAAeV,aAAa,CAACG,OAAO,CAAC,KAAK,EAAE,MAAM,CAAC;AACnD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH,CAAC;AAED,MAAMc,oBAAoB,GAAIE,MAAc,IAAa;EACvD,QAAQA,MAAM;IACZ,KAAK,MAAM;MACT,OAAO,MAAM;IACf,KAAK,OAAO;MACV,OAAO,QAAQ;IACjB,KAAK,MAAM;MACT,OAAO,aAAa;IACtB;MACE,OAAOA,MAAM;EACjB;AACF,CAAC;AAED,OAAO,MAAMC,YAAY,GAAIxC,WAAwB,IAAW;EAC9D,MAAMyC,WAAW,GAAG1C,mBAAmB,CAACC,WAAW,CAAC;EACpD,MAAM0C,WAAW,GAAGC,MAAM,CAACC,IAAI,CAAC,EAAE,EAAE,QAAQ,EAAE,sBAAsB,CAAC;EAErE,IAAIF,WAAW,EAAE;IACfA,WAAW,CAACG,QAAQ,CAACC,KAAK,CAACL,WAAW,CAAC;IACvCC,WAAW,CAACG,QAAQ,CAACE,KAAK,CAAC,CAAC;;IAE5B;IACAL,WAAW,CAACM,MAAM,GAAG,MAAM;MACzBC,UAAU,CAAC,MAAM;QACfP,WAAW,CAACQ,KAAK,CAAC,CAAC;MACrB,CAAC,EAAE,GAAG,CAAC;IACT,CAAC;EACH,CAAC,MAAM;IACLC,KAAK,CAAC,uCAAuC,CAAC;EAChD;AACF,CAAC;AAED,OAAO,MAAMC,kBAAkB,GAAG,MAAOpD,WAAwB,IAAoB;EACnF;EACA;EACA,MAAMyC,WAAW,GAAG1C,mBAAmB,CAACC,WAAW,CAAC;EACpD,MAAM0C,WAAW,GAAGC,MAAM,CAACC,IAAI,CAAC,EAAE,EAAE,QAAQ,CAAC;EAE7C,IAAIF,WAAW,EAAE;IACfA,WAAW,CAACG,QAAQ,CAACC,KAAK,CAACL,WAAW,CAAC;IACvCC,WAAW,CAACG,QAAQ,CAACE,KAAK,CAAC,CAAC;EAC9B;AACF,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAMM,iBAAiB,GAAG,MAAAA,CAC/BpD,WAAwB,EACxBC,SAAe,KACU;EACzB,IAAI;IACF,MAAME,gBAAgB,GAAG,MAAMN,mBAAmB,CAAC,CAAC;IACpD,OAAO;MACLG,WAAW;MACXC,SAAS;MACTE;IACF,CAAC;EACH,CAAC,CAAC,OAAOkD,KAAK,EAAE;IACdC,OAAO,CAACD,KAAK,CAAC,8CAA8C,EAAEA,KAAK,CAAC;IACpE;IACA,OAAO;MACLrD,WAAW;MACXC,SAAS;MACTC,YAAY,EAAE;QACZO,IAAI,EAAE,6BAA6B;QACnCE,OAAO,EAAE,uBAAuB;QAChCE,KAAK,EAAE,kBAAkB;QACzBE,KAAK,EAAE;MACT;IACF,CAAC;EACH;AACF,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAMwC,wBAAwB,GAAG,MAAAA,CACtCvD,WAAwB,EACxBC,SAAe,KACG;EAClB,MAAMF,WAAW,GAAG,MAAMqD,iBAAiB,CAACpD,WAAW,EAAEC,SAAS,CAAC;EACnEsC,YAAY,CAACxC,WAAW,CAAC;AAC3B,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAMyD,8BAA8B,GAAG,MAAAA,CAC5CxD,WAAwB,EACxBC,SAAe,KACG;EAClB,MAAMF,WAAW,GAAG,MAAMqD,iBAAiB,CAACpD,WAAW,EAAEC,SAAS,CAAC;EACnE,MAAMkD,kBAAkB,CAACpD,WAAW,CAAC;AACvC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}