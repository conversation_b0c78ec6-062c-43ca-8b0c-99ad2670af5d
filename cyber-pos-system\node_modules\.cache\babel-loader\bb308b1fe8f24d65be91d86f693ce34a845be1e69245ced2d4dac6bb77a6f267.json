{"ast": null, "code": "import _objectSpread from\"E:/FX/Cyber POS/cyber-pos-system/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import React,{useState,useEffect,useRef}from'react';import{Settings as SettingsIcon,Save,Users,Database,Upload,X,Building2}from'lucide-react';import{useAuth}from'../../contexts/AuthContext';import{useBusinessSettings}from'../../contexts/BusinessSettingsContext';import UserManagement from'../users/UserManagement';import{initializeDemoData}from'../../utils/seedData';import{seedTestData}from'../../utils/testDataSeeder';import{getBusinessSettings,saveBusinessSettings,uploadLogo,deleteLogo,getDefaultBusinessSettings}from'../../services/businessSettingsService';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const Settings=()=>{const{hasPermission,currentUser}=useAuth();const{refreshBusinessSettings}=useBusinessSettings();const[activeTab,setActiveTab]=useState('general');const[loading,setLoading]=useState(false);const[message,setMessage]=useState('');// Business settings state\nconst[businessSettings,setBusinessSettings]=useState(null);const[businessFormData,setBusinessFormData]=useState(getDefaultBusinessSettings());const[logoFile,setLogoFile]=useState(null);const[logoPreview,setLogoPreview]=useState(null);const[savingSettings,setSavingSettings]=useState(false);const fileInputRef=useRef(null);const tabs=[{id:'general',name:'Business Settings',icon:Building2},...(hasPermission('admin')?[{id:'users',name:'User Management',icon:Users}]:[]),{id:'data',name:'Data Management',icon:Database}];// Load business settings on component mount\nuseEffect(()=>{loadBusinessSettings();},[]);const loadBusinessSettings=async()=>{try{const settings=await getBusinessSettings();if(settings){setBusinessSettings(settings);setBusinessFormData({businessName:settings.businessName,address:settings.address,phone:settings.phone,email:settings.email||'',taxNumber:settings.taxNumber||'',licenseNumber:settings.licenseNumber||'',currency:settings.currency,taxRate:settings.taxRate,logoUrl:settings.logoUrl,logoFileName:settings.logoFileName,receiptHeader:settings.receiptHeader||'',receiptFooter:settings.receiptFooter,operatingHours:settings.operatingHours||'',website:settings.website||''});if(settings.logoUrl){setLogoPreview(settings.logoUrl);}}}catch(error){console.error('Error loading business settings:',error);setMessage('Error loading business settings');}};const handleInitializeDemo=async()=>{setLoading(true);setMessage('');try{await initializeDemoData();setMessage('Demo data initialized successfully!');}catch(error){setMessage(\"Error: \".concat(error.message));}finally{setLoading(false);}};const handleSeedTestData=async()=>{if(!currentUser){setMessage('Error: User not authenticated');return;}setLoading(true);setMessage('');try{await seedTestData(currentUser.id);setMessage('Test data with transactions seeded successfully! You can now view reports with sample data.');}catch(error){setMessage(\"Error: \".concat(error.message));}finally{setLoading(false);}};// Business settings handlers\nconst handleBusinessFormChange=(field,value)=>{setBusinessFormData(prev=>_objectSpread(_objectSpread({},prev),{},{[field]:value}));};const handleLogoSelect=event=>{var _event$target$files;const file=(_event$target$files=event.target.files)===null||_event$target$files===void 0?void 0:_event$target$files[0];if(file){setLogoFile(file);// Create preview\nconst reader=new FileReader();reader.onload=e=>{var _e$target;setLogoPreview((_e$target=e.target)===null||_e$target===void 0?void 0:_e$target.result);};reader.readAsDataURL(file);}};const handleRemoveLogo=()=>{setLogoFile(null);setLogoPreview(null);setBusinessFormData(prev=>_objectSpread(_objectSpread({},prev),{},{logoUrl:undefined,logoFileName:undefined}));if(fileInputRef.current){fileInputRef.current.value='';}};const handleSaveBusinessSettings=async()=>{if(!hasPermission('admin')){setMessage('Error: Admin permission required');return;}setSavingSettings(true);setMessage('');try{let logoUrl=businessFormData.logoUrl;let logoFileName=businessFormData.logoFileName;// Upload new logo if selected\nif(logoFile){// Delete old logo if exists\nif(businessFormData.logoFileName){await deleteLogo(businessFormData.logoFileName);}const uploadResult=await uploadLogo(logoFile);logoUrl=uploadResult.url;logoFileName=uploadResult.fileName;}const settingsToSave=_objectSpread(_objectSpread({},businessFormData),{},{logoUrl,logoFileName});const savedSettings=await saveBusinessSettings(settingsToSave);setBusinessSettings(savedSettings);setLogoFile(null);// Refresh the business settings context to update the UI\nawait refreshBusinessSettings();setMessage('Business settings saved successfully!');}catch(error){setMessage(\"Error: \".concat(error.message));}finally{setSavingSettings(false);}};return/*#__PURE__*/_jsx(\"div\",{className:\"space-y-6\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"bg-white shadow rounded-lg\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"px-6 py-4 border-b border-gray-200\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsx(SettingsIcon,{className:\"h-6 w-6 text-primary-600 mr-2\"}),/*#__PURE__*/_jsx(\"h1\",{className:\"text-2xl font-bold text-gray-900\",children:\"System Settings\"})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"border-b border-gray-200\",children:/*#__PURE__*/_jsx(\"nav\",{className:\"-mb-px flex space-x-8 px-6\",children:tabs.map(tab=>/*#__PURE__*/_jsxs(\"button\",{onClick:()=>setActiveTab(tab.id),className:\"\".concat(activeTab===tab.id?'border-primary-500 text-primary-600':'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300',\" whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm flex items-center\"),children:[/*#__PURE__*/_jsx(tab.icon,{className:\"h-4 w-4 mr-2\"}),tab.name]},tab.id))})}),/*#__PURE__*/_jsxs(\"div\",{className:\"p-6\",children:[activeTab==='general'&&/*#__PURE__*/_jsx(\"div\",{className:\"space-y-6\",children:/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg font-medium text-gray-900 mb-4\",children:\"Business Settings\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"mb-8 p-6 bg-gray-50 rounded-lg\",children:[/*#__PURE__*/_jsx(\"h4\",{className:\"text-md font-medium text-gray-900 mb-4\",children:\"Company Logo\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-start space-x-6\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"flex-shrink-0\",children:logoPreview?/*#__PURE__*/_jsxs(\"div\",{className:\"relative\",children:[/*#__PURE__*/_jsx(\"img\",{src:logoPreview,alt:\"Logo preview\",className:\"w-32 h-32 object-contain border border-gray-300 rounded-lg bg-white\"}),/*#__PURE__*/_jsx(\"button\",{onClick:handleRemoveLogo,className:\"absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600\",children:/*#__PURE__*/_jsx(X,{className:\"h-4 w-4\"})})]}):/*#__PURE__*/_jsx(\"div\",{className:\"w-32 h-32 border-2 border-dashed border-gray-300 rounded-lg flex items-center justify-center bg-white\",children:/*#__PURE__*/_jsx(Upload,{className:\"h-8 w-8 text-gray-400\"})})}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1\",children:[/*#__PURE__*/_jsx(\"input\",{ref:fileInputRef,type:\"file\",accept:\"image/*\",onChange:handleLogoSelect,className:\"hidden\"}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>{var _fileInputRef$current;return(_fileInputRef$current=fileInputRef.current)===null||_fileInputRef$current===void 0?void 0:_fileInputRef$current.click();},className:\"bg-white border border-gray-300 rounded-md px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50\",children:logoPreview?'Change Logo':'Upload Logo'}),/*#__PURE__*/_jsx(\"p\",{className:\"mt-2 text-sm text-gray-500\",children:\"Upload your company logo. Recommended size: 200x200px. Max file size: 5MB. Supported formats: JPG, PNG, GIF.\"})]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-1 gap-6 sm:grid-cols-2\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700\",children:\"Business Name *\"}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",value:businessFormData.businessName,onChange:e=>handleBusinessFormChange('businessName',e.target.value),className:\"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-primary-500 focus:border-primary-500\",required:true})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700\",children:\"Phone Number *\"}),/*#__PURE__*/_jsx(\"input\",{type:\"tel\",value:businessFormData.phone,onChange:e=>handleBusinessFormChange('phone',e.target.value),className:\"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-primary-500 focus:border-primary-500\",required:true})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"sm:col-span-2\",children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700\",children:\"Business Address *\"}),/*#__PURE__*/_jsx(\"textarea\",{value:businessFormData.address,onChange:e=>handleBusinessFormChange('address',e.target.value),rows:3,className:\"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-primary-500 focus:border-primary-500\",required:true})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700\",children:\"Email Address\"}),/*#__PURE__*/_jsx(\"input\",{type:\"email\",value:businessFormData.email,onChange:e=>handleBusinessFormChange('email',e.target.value),className:\"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-primary-500 focus:border-primary-500\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700\",children:\"Website\"}),/*#__PURE__*/_jsx(\"input\",{type:\"url\",value:businessFormData.website,onChange:e=>handleBusinessFormChange('website',e.target.value),className:\"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-primary-500 focus:border-primary-500\",placeholder:\"https://www.yourbusiness.com\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"mt-8\",children:[/*#__PURE__*/_jsx(\"h4\",{className:\"text-md font-medium text-gray-900 mb-4\",children:\"Tax & Legal Information\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-1 gap-6 sm:grid-cols-2\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700\",children:\"Tax Registration Number (PIN)\"}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",value:businessFormData.taxNumber,onChange:e=>handleBusinessFormChange('taxNumber',e.target.value),className:\"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-primary-500 focus:border-primary-500\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700\",children:\"Business License Number\"}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",value:businessFormData.licenseNumber,onChange:e=>handleBusinessFormChange('licenseNumber',e.target.value),className:\"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-primary-500 focus:border-primary-500\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700\",children:\"Currency\"}),/*#__PURE__*/_jsxs(\"select\",{value:businessFormData.currency,onChange:e=>handleBusinessFormChange('currency',e.target.value),className:\"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-primary-500 focus:border-primary-500\",children:[/*#__PURE__*/_jsx(\"option\",{value:\"KSh\",children:\"KSh (Kenyan Shilling)\"}),/*#__PURE__*/_jsx(\"option\",{value:\"USD\",children:\"USD (US Dollar)\"}),/*#__PURE__*/_jsx(\"option\",{value:\"EUR\",children:\"EUR (Euro)\"}),/*#__PURE__*/_jsx(\"option\",{value:\"GBP\",children:\"GBP (British Pound)\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700\",children:\"Tax Rate (%)\"}),/*#__PURE__*/_jsx(\"input\",{type:\"number\",value:businessFormData.taxRate,onChange:e=>handleBusinessFormChange('taxRate',parseFloat(e.target.value)||0),step:\"0.01\",min:\"0\",max:\"100\",className:\"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-primary-500 focus:border-primary-500\"})]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"mt-8\",children:[/*#__PURE__*/_jsx(\"h4\",{className:\"text-md font-medium text-gray-900 mb-4\",children:\"Receipt Configuration\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-1 gap-6\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700\",children:\"Receipt Header (Optional)\"}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",value:businessFormData.receiptHeader,onChange:e=>handleBusinessFormChange('receiptHeader',e.target.value),className:\"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-primary-500 focus:border-primary-500\",placeholder:\"Special message for receipt header\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700\",children:\"Receipt Footer\"}),/*#__PURE__*/_jsx(\"textarea\",{value:businessFormData.receiptFooter,onChange:e=>handleBusinessFormChange('receiptFooter',e.target.value),rows:3,className:\"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-primary-500 focus:border-primary-500\",placeholder:\"Thank you message and additional information\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700\",children:\"Operating Hours\"}),/*#__PURE__*/_jsx(\"textarea\",{value:businessFormData.operatingHours,onChange:e=>handleBusinessFormChange('operatingHours',e.target.value),rows:2,className:\"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-primary-500 focus:border-primary-500\",placeholder:\"Mon-Fri: 8:00 AM - 6:00 PM\"})]})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"mt-8 pt-6 border-t border-gray-200\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-sm text-gray-500\",children:\"* Required fields\"}),/*#__PURE__*/_jsxs(\"button\",{onClick:handleSaveBusinessSettings,disabled:savingSettings||!hasPermission('admin'),className:\"bg-primary-600 text-white px-6 py-2 rounded-lg hover:bg-primary-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center\",children:[/*#__PURE__*/_jsx(Save,{className:\"h-4 w-4 mr-2\"}),savingSettings?'Saving...':'Save Business Settings']})]})})]})}),activeTab==='users'&&hasPermission('admin')&&/*#__PURE__*/_jsx(UserManagement,{}),activeTab==='data'&&/*#__PURE__*/_jsx(\"div\",{className:\"space-y-6\",children:/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg font-medium text-gray-900 mb-4\",children:\"Data Management\"}),/*#__PURE__*/_jsx(\"div\",{className:\"bg-yellow-50 border border-yellow-200 rounded-md p-4 mb-6\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"flex-shrink-0\",children:/*#__PURE__*/_jsx(Database,{className:\"h-5 w-5 text-yellow-400\"})}),/*#__PURE__*/_jsxs(\"div\",{className:\"ml-3\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"text-sm font-medium text-yellow-800\",children:\"Demo Data Initialization\"}),/*#__PURE__*/_jsx(\"div\",{className:\"mt-2 text-sm text-yellow-700\",children:/*#__PURE__*/_jsx(\"p\",{children:\"Initialize the system with demo services and products for testing purposes. This will add sample cyber services and stationery items to your database.\"})})]})]})}),message&&/*#__PURE__*/_jsx(\"div\",{className:\"p-4 rounded-md mb-4 \".concat(message.includes('Error')?'bg-red-50 text-red-700 border border-red-200':'bg-green-50 text-green-700 border border-green-200'),children:message}),/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-4\",children:[/*#__PURE__*/_jsxs(\"button\",{onClick:handleInitializeDemo,disabled:loading,className:\"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 disabled:opacity-50 mr-4\",children:[/*#__PURE__*/_jsx(Database,{className:\"h-4 w-4 mr-2\"}),loading?'Initializing...':'Initialize Demo Data']}),/*#__PURE__*/_jsxs(\"button\",{onClick:handleSeedTestData,disabled:loading,className:\"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 disabled:opacity-50\",children:[/*#__PURE__*/_jsx(Database,{className:\"h-4 w-4 mr-2\"}),loading?'Seeding...':'Seed Test Data with Transactions']}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-sm text-gray-600 mt-2\",children:[/*#__PURE__*/_jsxs(\"p\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Initialize Demo Data:\"}),\" Adds basic services and products\"]}),/*#__PURE__*/_jsxs(\"p\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Seed Test Data:\"}),\" Adds comprehensive test data including 30 days of sample transactions for testing reports\"]})]})]})]})})]})]})});};export default Settings;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "Settings", "SettingsIcon", "Save", "Users", "Database", "Upload", "X", "Building2", "useAuth", "useBusinessSettings", "UserManagement", "initializeDemoData", "seedTestData", "getBusinessSettings", "saveBusinessSettings", "uploadLogo", "deleteLogo", "getDefaultBusinessSettings", "jsx", "_jsx", "jsxs", "_jsxs", "hasPermission", "currentUser", "refreshBusinessSettings", "activeTab", "setActiveTab", "loading", "setLoading", "message", "setMessage", "businessSettings", "setBusinessSettings", "businessFormData", "setBusinessFormData", "logoFile", "setLogoFile", "logoPreview", "setLogoPreview", "savingSettings", "setSavingSettings", "fileInputRef", "tabs", "id", "name", "icon", "loadBusinessSettings", "settings", "businessName", "address", "phone", "email", "taxNumber", "licenseNumber", "currency", "taxRate", "logoUrl", "logoFileName", "receiptHeader", "receiptFooter", "operatingHours", "website", "error", "console", "handleInitializeDemo", "concat", "handleSeedTestData", "handleBusinessFormChange", "field", "value", "prev", "_objectSpread", "handleLogoSelect", "event", "_event$target$files", "file", "target", "files", "reader", "FileReader", "onload", "e", "_e$target", "result", "readAsDataURL", "handleRemoveLogo", "undefined", "current", "handleSaveBusinessSettings", "uploadResult", "url", "fileName", "settingsToSave", "savedSettings", "className", "children", "map", "tab", "onClick", "src", "alt", "ref", "type", "accept", "onChange", "_fileInputRef$current", "click", "required", "rows", "placeholder", "parseFloat", "step", "min", "max", "disabled", "includes"], "sources": ["E:/FX/Cyber POS/cyber-pos-system/src/components/settings/Settings.tsx"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\nimport { Settings as SettingsIcon, Save, Users, Database, Upload, X, Building2 } from 'lucide-react';\nimport { useAuth } from '../../contexts/AuthContext';\nimport { useBusinessSettings } from '../../contexts/BusinessSettingsContext';\nimport UserManagement from '../users/UserManagement';\nimport { initializeDemoData } from '../../utils/seedData';\nimport { seedTestData } from '../../utils/testDataSeeder';\nimport { BusinessSettings } from '../../types';\nimport {\n  getBusinessSettings,\n  saveBusinessSettings,\n  uploadLogo,\n  deleteLogo,\n  getDefaultBusinessSettings\n} from '../../services/businessSettingsService';\n\nconst Settings: React.FC = () => {\n  const { hasPermission, currentUser } = useAuth();\n  const { refreshBusinessSettings } = useBusinessSettings();\n  const [activeTab, setActiveTab] = useState('general');\n  const [loading, setLoading] = useState(false);\n  const [message, setMessage] = useState('');\n\n  // Business settings state\n  const [businessSettings, setBusinessSettings] = useState<BusinessSettings | null>(null);\n  const [businessFormData, setBusinessFormData] = useState(getDefaultBusinessSettings());\n  const [logoFile, setLogoFile] = useState<File | null>(null);\n  const [logoPreview, setLogoPreview] = useState<string | null>(null);\n  const [savingSettings, setSavingSettings] = useState(false);\n  const fileInputRef = useRef<HTMLInputElement>(null);\n\n  const tabs = [\n    { id: 'general', name: 'Business Settings', icon: Building2 },\n    ...(hasPermission('admin') ? [{ id: 'users', name: 'User Management', icon: Users }] : []),\n    { id: 'data', name: 'Data Management', icon: Database },\n  ];\n\n  // Load business settings on component mount\n  useEffect(() => {\n    loadBusinessSettings();\n  }, []);\n\n  const loadBusinessSettings = async () => {\n    try {\n      const settings = await getBusinessSettings();\n      if (settings) {\n        setBusinessSettings(settings);\n        setBusinessFormData({\n          businessName: settings.businessName,\n          address: settings.address,\n          phone: settings.phone,\n          email: settings.email || '',\n          taxNumber: settings.taxNumber || '',\n          licenseNumber: settings.licenseNumber || '',\n          currency: settings.currency,\n          taxRate: settings.taxRate,\n          logoUrl: settings.logoUrl,\n          logoFileName: settings.logoFileName,\n          receiptHeader: settings.receiptHeader || '',\n          receiptFooter: settings.receiptFooter,\n          operatingHours: settings.operatingHours || '',\n          website: settings.website || '',\n        });\n        if (settings.logoUrl) {\n          setLogoPreview(settings.logoUrl);\n        }\n      }\n    } catch (error) {\n      console.error('Error loading business settings:', error);\n      setMessage('Error loading business settings');\n    }\n  };\n\n  const handleInitializeDemo = async () => {\n    setLoading(true);\n    setMessage('');\n\n    try {\n      await initializeDemoData();\n      setMessage('Demo data initialized successfully!');\n    } catch (error: any) {\n      setMessage(`Error: ${error.message}`);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleSeedTestData = async () => {\n    if (!currentUser) {\n      setMessage('Error: User not authenticated');\n      return;\n    }\n\n    setLoading(true);\n    setMessage('');\n\n    try {\n      await seedTestData(currentUser.id);\n      setMessage('Test data with transactions seeded successfully! You can now view reports with sample data.');\n    } catch (error: any) {\n      setMessage(`Error: ${error.message}`);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Business settings handlers\n  const handleBusinessFormChange = (field: string, value: string | number) => {\n    setBusinessFormData(prev => ({\n      ...prev,\n      [field]: value\n    }));\n  };\n\n  const handleLogoSelect = (event: React.ChangeEvent<HTMLInputElement>) => {\n    const file = event.target.files?.[0];\n    if (file) {\n      setLogoFile(file);\n\n      // Create preview\n      const reader = new FileReader();\n      reader.onload = (e) => {\n        setLogoPreview(e.target?.result as string);\n      };\n      reader.readAsDataURL(file);\n    }\n  };\n\n  const handleRemoveLogo = () => {\n    setLogoFile(null);\n    setLogoPreview(null);\n    setBusinessFormData(prev => ({\n      ...prev,\n      logoUrl: undefined,\n      logoFileName: undefined\n    }));\n    if (fileInputRef.current) {\n      fileInputRef.current.value = '';\n    }\n  };\n\n  const handleSaveBusinessSettings = async () => {\n    if (!hasPermission('admin')) {\n      setMessage('Error: Admin permission required');\n      return;\n    }\n\n    setSavingSettings(true);\n    setMessage('');\n\n    try {\n      let logoUrl = businessFormData.logoUrl;\n      let logoFileName = businessFormData.logoFileName;\n\n      // Upload new logo if selected\n      if (logoFile) {\n        // Delete old logo if exists\n        if (businessFormData.logoFileName) {\n          await deleteLogo(businessFormData.logoFileName);\n        }\n\n        const uploadResult = await uploadLogo(logoFile);\n        logoUrl = uploadResult.url;\n        logoFileName = uploadResult.fileName;\n      }\n\n      const settingsToSave = {\n        ...businessFormData,\n        logoUrl,\n        logoFileName,\n      };\n\n      const savedSettings = await saveBusinessSettings(settingsToSave);\n      setBusinessSettings(savedSettings);\n      setLogoFile(null);\n\n      // Refresh the business settings context to update the UI\n      await refreshBusinessSettings();\n\n      setMessage('Business settings saved successfully!');\n    } catch (error: any) {\n      setMessage(`Error: ${error.message}`);\n    } finally {\n      setSavingSettings(false);\n    }\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"bg-white shadow rounded-lg\">\n        <div className=\"px-6 py-4 border-b border-gray-200\">\n          <div className=\"flex items-center\">\n            <SettingsIcon className=\"h-6 w-6 text-primary-600 mr-2\" />\n            <h1 className=\"text-2xl font-bold text-gray-900\">System Settings</h1>\n          </div>\n        </div>\n\n        {/* Tabs */}\n        <div className=\"border-b border-gray-200\">\n          <nav className=\"-mb-px flex space-x-8 px-6\">\n            {tabs.map((tab) => (\n              <button\n                key={tab.id}\n                onClick={() => setActiveTab(tab.id)}\n                className={`${\n                  activeTab === tab.id\n                    ? 'border-primary-500 text-primary-600'\n                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n                } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm flex items-center`}\n              >\n                <tab.icon className=\"h-4 w-4 mr-2\" />\n                {tab.name}\n              </button>\n            ))}\n          </nav>\n        </div>\n\n        {/* Tab Content */}\n        <div className=\"p-6\">\n          {activeTab === 'general' && (\n            <div className=\"space-y-6\">\n              <div>\n                <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Business Settings</h3>\n\n                {/* Logo Upload Section */}\n                <div className=\"mb-8 p-6 bg-gray-50 rounded-lg\">\n                  <h4 className=\"text-md font-medium text-gray-900 mb-4\">Company Logo</h4>\n                  <div className=\"flex items-start space-x-6\">\n                    <div className=\"flex-shrink-0\">\n                      {logoPreview ? (\n                        <div className=\"relative\">\n                          <img\n                            src={logoPreview}\n                            alt=\"Logo preview\"\n                            className=\"w-32 h-32 object-contain border border-gray-300 rounded-lg bg-white\"\n                          />\n                          <button\n                            onClick={handleRemoveLogo}\n                            className=\"absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600\"\n                          >\n                            <X className=\"h-4 w-4\" />\n                          </button>\n                        </div>\n                      ) : (\n                        <div className=\"w-32 h-32 border-2 border-dashed border-gray-300 rounded-lg flex items-center justify-center bg-white\">\n                          <Upload className=\"h-8 w-8 text-gray-400\" />\n                        </div>\n                      )}\n                    </div>\n                    <div className=\"flex-1\">\n                      <input\n                        ref={fileInputRef}\n                        type=\"file\"\n                        accept=\"image/*\"\n                        onChange={handleLogoSelect}\n                        className=\"hidden\"\n                      />\n                      <button\n                        onClick={() => fileInputRef.current?.click()}\n                        className=\"bg-white border border-gray-300 rounded-md px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50\"\n                      >\n                        {logoPreview ? 'Change Logo' : 'Upload Logo'}\n                      </button>\n                      <p className=\"mt-2 text-sm text-gray-500\">\n                        Upload your company logo. Recommended size: 200x200px. Max file size: 5MB.\n                        Supported formats: JPG, PNG, GIF.\n                      </p>\n                    </div>\n                  </div>\n                </div>\n\n                {/* Business Information Form */}\n                <div className=\"grid grid-cols-1 gap-6 sm:grid-cols-2\">\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700\">Business Name *</label>\n                    <input\n                      type=\"text\"\n                      value={businessFormData.businessName}\n                      onChange={(e) => handleBusinessFormChange('businessName', e.target.value)}\n                      className=\"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-primary-500 focus:border-primary-500\"\n                      required\n                    />\n                  </div>\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700\">Phone Number *</label>\n                    <input\n                      type=\"tel\"\n                      value={businessFormData.phone}\n                      onChange={(e) => handleBusinessFormChange('phone', e.target.value)}\n                      className=\"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-primary-500 focus:border-primary-500\"\n                      required\n                    />\n                  </div>\n                  <div className=\"sm:col-span-2\">\n                    <label className=\"block text-sm font-medium text-gray-700\">Business Address *</label>\n                    <textarea\n                      value={businessFormData.address}\n                      onChange={(e) => handleBusinessFormChange('address', e.target.value)}\n                      rows={3}\n                      className=\"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-primary-500 focus:border-primary-500\"\n                      required\n                    />\n                  </div>\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700\">Email Address</label>\n                    <input\n                      type=\"email\"\n                      value={businessFormData.email}\n                      onChange={(e) => handleBusinessFormChange('email', e.target.value)}\n                      className=\"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-primary-500 focus:border-primary-500\"\n                    />\n                  </div>\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700\">Website</label>\n                    <input\n                      type=\"url\"\n                      value={businessFormData.website}\n                      onChange={(e) => handleBusinessFormChange('website', e.target.value)}\n                      className=\"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-primary-500 focus:border-primary-500\"\n                      placeholder=\"https://www.yourbusiness.com\"\n                    />\n                  </div>\n                </div>\n\n                {/* Tax and Legal Information */}\n                <div className=\"mt-8\">\n                  <h4 className=\"text-md font-medium text-gray-900 mb-4\">Tax & Legal Information</h4>\n                  <div className=\"grid grid-cols-1 gap-6 sm:grid-cols-2\">\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700\">Tax Registration Number (PIN)</label>\n                      <input\n                        type=\"text\"\n                        value={businessFormData.taxNumber}\n                        onChange={(e) => handleBusinessFormChange('taxNumber', e.target.value)}\n                        className=\"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-primary-500 focus:border-primary-500\"\n                      />\n                    </div>\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700\">Business License Number</label>\n                      <input\n                        type=\"text\"\n                        value={businessFormData.licenseNumber}\n                        onChange={(e) => handleBusinessFormChange('licenseNumber', e.target.value)}\n                        className=\"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-primary-500 focus:border-primary-500\"\n                      />\n                    </div>\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700\">Currency</label>\n                      <select\n                        value={businessFormData.currency}\n                        onChange={(e) => handleBusinessFormChange('currency', e.target.value)}\n                        className=\"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-primary-500 focus:border-primary-500\"\n                      >\n                        <option value=\"KSh\">KSh (Kenyan Shilling)</option>\n                        <option value=\"USD\">USD (US Dollar)</option>\n                        <option value=\"EUR\">EUR (Euro)</option>\n                        <option value=\"GBP\">GBP (British Pound)</option>\n                      </select>\n                    </div>\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700\">Tax Rate (%)</label>\n                      <input\n                        type=\"number\"\n                        value={businessFormData.taxRate}\n                        onChange={(e) => handleBusinessFormChange('taxRate', parseFloat(e.target.value) || 0)}\n                        step=\"0.01\"\n                        min=\"0\"\n                        max=\"100\"\n                        className=\"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-primary-500 focus:border-primary-500\"\n                      />\n                    </div>\n                  </div>\n                </div>\n\n                {/* Receipt Configuration */}\n                <div className=\"mt-8\">\n                  <h4 className=\"text-md font-medium text-gray-900 mb-4\">Receipt Configuration</h4>\n                  <div className=\"grid grid-cols-1 gap-6\">\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700\">Receipt Header (Optional)</label>\n                      <input\n                        type=\"text\"\n                        value={businessFormData.receiptHeader}\n                        onChange={(e) => handleBusinessFormChange('receiptHeader', e.target.value)}\n                        className=\"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-primary-500 focus:border-primary-500\"\n                        placeholder=\"Special message for receipt header\"\n                      />\n                    </div>\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700\">Receipt Footer</label>\n                      <textarea\n                        value={businessFormData.receiptFooter}\n                        onChange={(e) => handleBusinessFormChange('receiptFooter', e.target.value)}\n                        rows={3}\n                        className=\"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-primary-500 focus:border-primary-500\"\n                        placeholder=\"Thank you message and additional information\"\n                      />\n                    </div>\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700\">Operating Hours</label>\n                      <textarea\n                        value={businessFormData.operatingHours}\n                        onChange={(e) => handleBusinessFormChange('operatingHours', e.target.value)}\n                        rows={2}\n                        className=\"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-primary-500 focus:border-primary-500\"\n                        placeholder=\"Mon-Fri: 8:00 AM - 6:00 PM\"\n                      />\n                    </div>\n                  </div>\n                </div>\n\n                {/* Save Button */}\n                <div className=\"mt-8 pt-6 border-t border-gray-200\">\n                  <div className=\"flex items-center justify-between\">\n                    <div className=\"text-sm text-gray-500\">\n                      * Required fields\n                    </div>\n                    <button\n                      onClick={handleSaveBusinessSettings}\n                      disabled={savingSettings || !hasPermission('admin')}\n                      className=\"bg-primary-600 text-white px-6 py-2 rounded-lg hover:bg-primary-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center\"\n                    >\n                      <Save className=\"h-4 w-4 mr-2\" />\n                      {savingSettings ? 'Saving...' : 'Save Business Settings'}\n                    </button>\n                  </div>\n                </div>\n              </div>\n            </div>\n          )}\n\n          {activeTab === 'users' && hasPermission('admin') && (\n            <UserManagement />\n          )}\n\n          {activeTab === 'data' && (\n            <div className=\"space-y-6\">\n              <div>\n                <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Data Management</h3>\n                <div className=\"bg-yellow-50 border border-yellow-200 rounded-md p-4 mb-6\">\n                  <div className=\"flex\">\n                    <div className=\"flex-shrink-0\">\n                      <Database className=\"h-5 w-5 text-yellow-400\" />\n                    </div>\n                    <div className=\"ml-3\">\n                      <h3 className=\"text-sm font-medium text-yellow-800\">\n                        Demo Data Initialization\n                      </h3>\n                      <div className=\"mt-2 text-sm text-yellow-700\">\n                        <p>\n                          Initialize the system with demo services and products for testing purposes.\n                          This will add sample cyber services and stationery items to your database.\n                        </p>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n\n                {message && (\n                  <div className={`p-4 rounded-md mb-4 ${\n                    message.includes('Error')\n                      ? 'bg-red-50 text-red-700 border border-red-200'\n                      : 'bg-green-50 text-green-700 border border-green-200'\n                  }`}>\n                    {message}\n                  </div>\n                )}\n\n                <div className=\"space-y-4\">\n                  <button\n                    onClick={handleInitializeDemo}\n                    disabled={loading}\n                    className=\"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 disabled:opacity-50 mr-4\"\n                  >\n                    <Database className=\"h-4 w-4 mr-2\" />\n                    {loading ? 'Initializing...' : 'Initialize Demo Data'}\n                  </button>\n\n                  <button\n                    onClick={handleSeedTestData}\n                    disabled={loading}\n                    className=\"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 disabled:opacity-50\"\n                  >\n                    <Database className=\"h-4 w-4 mr-2\" />\n                    {loading ? 'Seeding...' : 'Seed Test Data with Transactions'}\n                  </button>\n\n                  <div className=\"text-sm text-gray-600 mt-2\">\n                    <p><strong>Initialize Demo Data:</strong> Adds basic services and products</p>\n                    <p><strong>Seed Test Data:</strong> Adds comprehensive test data including 30 days of sample transactions for testing reports</p>\n                  </div>\n                </div>\n              </div>\n            </div>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Settings;\n"], "mappings": "qHAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,CAAEC,MAAM,KAAQ,OAAO,CAC1D,OAASC,QAAQ,GAAI,CAAAC,YAAY,CAAEC,IAAI,CAAEC,KAAK,CAAEC,QAAQ,CAAEC,MAAM,CAAEC,CAAC,CAAEC,SAAS,KAAQ,cAAc,CACpG,OAASC,OAAO,KAAQ,4BAA4B,CACpD,OAASC,mBAAmB,KAAQ,wCAAwC,CAC5E,MAAO,CAAAC,cAAc,KAAM,yBAAyB,CACpD,OAASC,kBAAkB,KAAQ,sBAAsB,CACzD,OAASC,YAAY,KAAQ,4BAA4B,CAEzD,OACEC,mBAAmB,CACnBC,oBAAoB,CACpBC,UAAU,CACVC,UAAU,CACVC,0BAA0B,KACrB,wCAAwC,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEhD,KAAM,CAAArB,QAAkB,CAAGA,CAAA,GAAM,CAC/B,KAAM,CAAEsB,aAAa,CAAEC,WAAY,CAAC,CAAGf,OAAO,CAAC,CAAC,CAChD,KAAM,CAAEgB,uBAAwB,CAAC,CAAGf,mBAAmB,CAAC,CAAC,CACzD,KAAM,CAACgB,SAAS,CAAEC,YAAY,CAAC,CAAG7B,QAAQ,CAAC,SAAS,CAAC,CACrD,KAAM,CAAC8B,OAAO,CAAEC,UAAU,CAAC,CAAG/B,QAAQ,CAAC,KAAK,CAAC,CAC7C,KAAM,CAACgC,OAAO,CAAEC,UAAU,CAAC,CAAGjC,QAAQ,CAAC,EAAE,CAAC,CAE1C;AACA,KAAM,CAACkC,gBAAgB,CAAEC,mBAAmB,CAAC,CAAGnC,QAAQ,CAA0B,IAAI,CAAC,CACvF,KAAM,CAACoC,gBAAgB,CAAEC,mBAAmB,CAAC,CAAGrC,QAAQ,CAACoB,0BAA0B,CAAC,CAAC,CAAC,CACtF,KAAM,CAACkB,QAAQ,CAAEC,WAAW,CAAC,CAAGvC,QAAQ,CAAc,IAAI,CAAC,CAC3D,KAAM,CAACwC,WAAW,CAAEC,cAAc,CAAC,CAAGzC,QAAQ,CAAgB,IAAI,CAAC,CACnE,KAAM,CAAC0C,cAAc,CAAEC,iBAAiB,CAAC,CAAG3C,QAAQ,CAAC,KAAK,CAAC,CAC3D,KAAM,CAAA4C,YAAY,CAAG1C,MAAM,CAAmB,IAAI,CAAC,CAEnD,KAAM,CAAA2C,IAAI,CAAG,CACX,CAAEC,EAAE,CAAE,SAAS,CAAEC,IAAI,CAAE,mBAAmB,CAAEC,IAAI,CAAEtC,SAAU,CAAC,CAC7D,IAAIe,aAAa,CAAC,OAAO,CAAC,CAAG,CAAC,CAAEqB,EAAE,CAAE,OAAO,CAAEC,IAAI,CAAE,iBAAiB,CAAEC,IAAI,CAAE1C,KAAM,CAAC,CAAC,CAAG,EAAE,CAAC,CAC1F,CAAEwC,EAAE,CAAE,MAAM,CAAEC,IAAI,CAAE,iBAAiB,CAAEC,IAAI,CAAEzC,QAAS,CAAC,CACxD,CAED;AACAN,SAAS,CAAC,IAAM,CACdgD,oBAAoB,CAAC,CAAC,CACxB,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAA,oBAAoB,CAAG,KAAAA,CAAA,GAAY,CACvC,GAAI,CACF,KAAM,CAAAC,QAAQ,CAAG,KAAM,CAAAlC,mBAAmB,CAAC,CAAC,CAC5C,GAAIkC,QAAQ,CAAE,CACZf,mBAAmB,CAACe,QAAQ,CAAC,CAC7Bb,mBAAmB,CAAC,CAClBc,YAAY,CAAED,QAAQ,CAACC,YAAY,CACnCC,OAAO,CAAEF,QAAQ,CAACE,OAAO,CACzBC,KAAK,CAAEH,QAAQ,CAACG,KAAK,CACrBC,KAAK,CAAEJ,QAAQ,CAACI,KAAK,EAAI,EAAE,CAC3BC,SAAS,CAAEL,QAAQ,CAACK,SAAS,EAAI,EAAE,CACnCC,aAAa,CAAEN,QAAQ,CAACM,aAAa,EAAI,EAAE,CAC3CC,QAAQ,CAAEP,QAAQ,CAACO,QAAQ,CAC3BC,OAAO,CAAER,QAAQ,CAACQ,OAAO,CACzBC,OAAO,CAAET,QAAQ,CAACS,OAAO,CACzBC,YAAY,CAAEV,QAAQ,CAACU,YAAY,CACnCC,aAAa,CAAEX,QAAQ,CAACW,aAAa,EAAI,EAAE,CAC3CC,aAAa,CAAEZ,QAAQ,CAACY,aAAa,CACrCC,cAAc,CAAEb,QAAQ,CAACa,cAAc,EAAI,EAAE,CAC7CC,OAAO,CAAEd,QAAQ,CAACc,OAAO,EAAI,EAC/B,CAAC,CAAC,CACF,GAAId,QAAQ,CAACS,OAAO,CAAE,CACpBlB,cAAc,CAACS,QAAQ,CAACS,OAAO,CAAC,CAClC,CACF,CACF,CAAE,MAAOM,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,kCAAkC,CAAEA,KAAK,CAAC,CACxDhC,UAAU,CAAC,iCAAiC,CAAC,CAC/C,CACF,CAAC,CAED,KAAM,CAAAkC,oBAAoB,CAAG,KAAAA,CAAA,GAAY,CACvCpC,UAAU,CAAC,IAAI,CAAC,CAChBE,UAAU,CAAC,EAAE,CAAC,CAEd,GAAI,CACF,KAAM,CAAAnB,kBAAkB,CAAC,CAAC,CAC1BmB,UAAU,CAAC,qCAAqC,CAAC,CACnD,CAAE,MAAOgC,KAAU,CAAE,CACnBhC,UAAU,WAAAmC,MAAA,CAAWH,KAAK,CAACjC,OAAO,CAAE,CAAC,CACvC,CAAC,OAAS,CACRD,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED,KAAM,CAAAsC,kBAAkB,CAAG,KAAAA,CAAA,GAAY,CACrC,GAAI,CAAC3C,WAAW,CAAE,CAChBO,UAAU,CAAC,+BAA+B,CAAC,CAC3C,OACF,CAEAF,UAAU,CAAC,IAAI,CAAC,CAChBE,UAAU,CAAC,EAAE,CAAC,CAEd,GAAI,CACF,KAAM,CAAAlB,YAAY,CAACW,WAAW,CAACoB,EAAE,CAAC,CAClCb,UAAU,CAAC,6FAA6F,CAAC,CAC3G,CAAE,MAAOgC,KAAU,CAAE,CACnBhC,UAAU,WAAAmC,MAAA,CAAWH,KAAK,CAACjC,OAAO,CAAE,CAAC,CACvC,CAAC,OAAS,CACRD,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED;AACA,KAAM,CAAAuC,wBAAwB,CAAGA,CAACC,KAAa,CAAEC,KAAsB,GAAK,CAC1EnC,mBAAmB,CAACoC,IAAI,EAAAC,aAAA,CAAAA,aAAA,IACnBD,IAAI,MACP,CAACF,KAAK,EAAGC,KAAK,EACd,CAAC,CACL,CAAC,CAED,KAAM,CAAAG,gBAAgB,CAAIC,KAA0C,EAAK,KAAAC,mBAAA,CACvE,KAAM,CAAAC,IAAI,EAAAD,mBAAA,CAAGD,KAAK,CAACG,MAAM,CAACC,KAAK,UAAAH,mBAAA,iBAAlBA,mBAAA,CAAqB,CAAC,CAAC,CACpC,GAAIC,IAAI,CAAE,CACRvC,WAAW,CAACuC,IAAI,CAAC,CAEjB;AACA,KAAM,CAAAG,MAAM,CAAG,GAAI,CAAAC,UAAU,CAAC,CAAC,CAC/BD,MAAM,CAACE,MAAM,CAAIC,CAAC,EAAK,KAAAC,SAAA,CACrB5C,cAAc,EAAA4C,SAAA,CAACD,CAAC,CAACL,MAAM,UAAAM,SAAA,iBAARA,SAAA,CAAUC,MAAgB,CAAC,CAC5C,CAAC,CACDL,MAAM,CAACM,aAAa,CAACT,IAAI,CAAC,CAC5B,CACF,CAAC,CAED,KAAM,CAAAU,gBAAgB,CAAGA,CAAA,GAAM,CAC7BjD,WAAW,CAAC,IAAI,CAAC,CACjBE,cAAc,CAAC,IAAI,CAAC,CACpBJ,mBAAmB,CAACoC,IAAI,EAAAC,aAAA,CAAAA,aAAA,IACnBD,IAAI,MACPd,OAAO,CAAE8B,SAAS,CAClB7B,YAAY,CAAE6B,SAAS,EACvB,CAAC,CACH,GAAI7C,YAAY,CAAC8C,OAAO,CAAE,CACxB9C,YAAY,CAAC8C,OAAO,CAAClB,KAAK,CAAG,EAAE,CACjC,CACF,CAAC,CAED,KAAM,CAAAmB,0BAA0B,CAAG,KAAAA,CAAA,GAAY,CAC7C,GAAI,CAAClE,aAAa,CAAC,OAAO,CAAC,CAAE,CAC3BQ,UAAU,CAAC,kCAAkC,CAAC,CAC9C,OACF,CAEAU,iBAAiB,CAAC,IAAI,CAAC,CACvBV,UAAU,CAAC,EAAE,CAAC,CAEd,GAAI,CACF,GAAI,CAAA0B,OAAO,CAAGvB,gBAAgB,CAACuB,OAAO,CACtC,GAAI,CAAAC,YAAY,CAAGxB,gBAAgB,CAACwB,YAAY,CAEhD;AACA,GAAItB,QAAQ,CAAE,CACZ;AACA,GAAIF,gBAAgB,CAACwB,YAAY,CAAE,CACjC,KAAM,CAAAzC,UAAU,CAACiB,gBAAgB,CAACwB,YAAY,CAAC,CACjD,CAEA,KAAM,CAAAgC,YAAY,CAAG,KAAM,CAAA1E,UAAU,CAACoB,QAAQ,CAAC,CAC/CqB,OAAO,CAAGiC,YAAY,CAACC,GAAG,CAC1BjC,YAAY,CAAGgC,YAAY,CAACE,QAAQ,CACtC,CAEA,KAAM,CAAAC,cAAc,CAAArB,aAAA,CAAAA,aAAA,IACftC,gBAAgB,MACnBuB,OAAO,CACPC,YAAY,EACb,CAED,KAAM,CAAAoC,aAAa,CAAG,KAAM,CAAA/E,oBAAoB,CAAC8E,cAAc,CAAC,CAChE5D,mBAAmB,CAAC6D,aAAa,CAAC,CAClCzD,WAAW,CAAC,IAAI,CAAC,CAEjB;AACA,KAAM,CAAAZ,uBAAuB,CAAC,CAAC,CAE/BM,UAAU,CAAC,uCAAuC,CAAC,CACrD,CAAE,MAAOgC,KAAU,CAAE,CACnBhC,UAAU,WAAAmC,MAAA,CAAWH,KAAK,CAACjC,OAAO,CAAE,CAAC,CACvC,CAAC,OAAS,CACRW,iBAAiB,CAAC,KAAK,CAAC,CAC1B,CACF,CAAC,CAED,mBACErB,IAAA,QAAK2E,SAAS,CAAC,WAAW,CAAAC,QAAA,cAExB1E,KAAA,QAAKyE,SAAS,CAAC,4BAA4B,CAAAC,QAAA,eACzC5E,IAAA,QAAK2E,SAAS,CAAC,oCAAoC,CAAAC,QAAA,cACjD1E,KAAA,QAAKyE,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChC5E,IAAA,CAAClB,YAAY,EAAC6F,SAAS,CAAC,+BAA+B,CAAE,CAAC,cAC1D3E,IAAA,OAAI2E,SAAS,CAAC,kCAAkC,CAAAC,QAAA,CAAC,iBAAe,CAAI,CAAC,EAClE,CAAC,CACH,CAAC,cAGN5E,IAAA,QAAK2E,SAAS,CAAC,0BAA0B,CAAAC,QAAA,cACvC5E,IAAA,QAAK2E,SAAS,CAAC,4BAA4B,CAAAC,QAAA,CACxCrD,IAAI,CAACsD,GAAG,CAAEC,GAAG,eACZ5E,KAAA,WAEE6E,OAAO,CAAEA,CAAA,GAAMxE,YAAY,CAACuE,GAAG,CAACtD,EAAE,CAAE,CACpCmD,SAAS,IAAA7B,MAAA,CACPxC,SAAS,GAAKwE,GAAG,CAACtD,EAAE,CAChB,qCAAqC,CACrC,4EAA4E,iFACF,CAAAoD,QAAA,eAEhF5E,IAAA,CAAC8E,GAAG,CAACpD,IAAI,EAACiD,SAAS,CAAC,cAAc,CAAE,CAAC,CACpCG,GAAG,CAACrD,IAAI,GATJqD,GAAG,CAACtD,EAUH,CACT,CAAC,CACC,CAAC,CACH,CAAC,cAGNtB,KAAA,QAAKyE,SAAS,CAAC,KAAK,CAAAC,QAAA,EACjBtE,SAAS,GAAK,SAAS,eACtBN,IAAA,QAAK2E,SAAS,CAAC,WAAW,CAAAC,QAAA,cACxB1E,KAAA,QAAA0E,QAAA,eACE5E,IAAA,OAAI2E,SAAS,CAAC,wCAAwC,CAAAC,QAAA,CAAC,mBAAiB,CAAI,CAAC,cAG7E1E,KAAA,QAAKyE,SAAS,CAAC,gCAAgC,CAAAC,QAAA,eAC7C5E,IAAA,OAAI2E,SAAS,CAAC,wCAAwC,CAAAC,QAAA,CAAC,cAAY,CAAI,CAAC,cACxE1E,KAAA,QAAKyE,SAAS,CAAC,4BAA4B,CAAAC,QAAA,eACzC5E,IAAA,QAAK2E,SAAS,CAAC,eAAe,CAAAC,QAAA,CAC3B1D,WAAW,cACVhB,KAAA,QAAKyE,SAAS,CAAC,UAAU,CAAAC,QAAA,eACvB5E,IAAA,QACEgF,GAAG,CAAE9D,WAAY,CACjB+D,GAAG,CAAC,cAAc,CAClBN,SAAS,CAAC,qEAAqE,CAChF,CAAC,cACF3E,IAAA,WACE+E,OAAO,CAAEb,gBAAiB,CAC1BS,SAAS,CAAC,kFAAkF,CAAAC,QAAA,cAE5F5E,IAAA,CAACb,CAAC,EAACwF,SAAS,CAAC,SAAS,CAAE,CAAC,CACnB,CAAC,EACN,CAAC,cAEN3E,IAAA,QAAK2E,SAAS,CAAC,uGAAuG,CAAAC,QAAA,cACpH5E,IAAA,CAACd,MAAM,EAACyF,SAAS,CAAC,uBAAuB,CAAE,CAAC,CACzC,CACN,CACE,CAAC,cACNzE,KAAA,QAAKyE,SAAS,CAAC,QAAQ,CAAAC,QAAA,eACrB5E,IAAA,UACEkF,GAAG,CAAE5D,YAAa,CAClB6D,IAAI,CAAC,MAAM,CACXC,MAAM,CAAC,SAAS,CAChBC,QAAQ,CAAEhC,gBAAiB,CAC3BsB,SAAS,CAAC,QAAQ,CACnB,CAAC,cACF3E,IAAA,WACE+E,OAAO,CAAEA,CAAA,QAAAO,qBAAA,QAAAA,qBAAA,CAAMhE,YAAY,CAAC8C,OAAO,UAAAkB,qBAAA,iBAApBA,qBAAA,CAAsBC,KAAK,CAAC,CAAC,EAAC,CAC7CZ,SAAS,CAAC,yGAAyG,CAAAC,QAAA,CAElH1D,WAAW,CAAG,aAAa,CAAG,aAAa,CACtC,CAAC,cACTlB,IAAA,MAAG2E,SAAS,CAAC,4BAA4B,CAAAC,QAAA,CAAC,8GAG1C,CAAG,CAAC,EACD,CAAC,EACH,CAAC,EACH,CAAC,cAGN1E,KAAA,QAAKyE,SAAS,CAAC,uCAAuC,CAAAC,QAAA,eACpD1E,KAAA,QAAA0E,QAAA,eACE5E,IAAA,UAAO2E,SAAS,CAAC,yCAAyC,CAAAC,QAAA,CAAC,iBAAe,CAAO,CAAC,cAClF5E,IAAA,UACEmF,IAAI,CAAC,MAAM,CACXjC,KAAK,CAAEpC,gBAAgB,CAACe,YAAa,CACrCwD,QAAQ,CAAGvB,CAAC,EAAKd,wBAAwB,CAAC,cAAc,CAAEc,CAAC,CAACL,MAAM,CAACP,KAAK,CAAE,CAC1EyB,SAAS,CAAC,kIAAkI,CAC5Ia,QAAQ,MACT,CAAC,EACC,CAAC,cACNtF,KAAA,QAAA0E,QAAA,eACE5E,IAAA,UAAO2E,SAAS,CAAC,yCAAyC,CAAAC,QAAA,CAAC,gBAAc,CAAO,CAAC,cACjF5E,IAAA,UACEmF,IAAI,CAAC,KAAK,CACVjC,KAAK,CAAEpC,gBAAgB,CAACiB,KAAM,CAC9BsD,QAAQ,CAAGvB,CAAC,EAAKd,wBAAwB,CAAC,OAAO,CAAEc,CAAC,CAACL,MAAM,CAACP,KAAK,CAAE,CACnEyB,SAAS,CAAC,kIAAkI,CAC5Ia,QAAQ,MACT,CAAC,EACC,CAAC,cACNtF,KAAA,QAAKyE,SAAS,CAAC,eAAe,CAAAC,QAAA,eAC5B5E,IAAA,UAAO2E,SAAS,CAAC,yCAAyC,CAAAC,QAAA,CAAC,oBAAkB,CAAO,CAAC,cACrF5E,IAAA,aACEkD,KAAK,CAAEpC,gBAAgB,CAACgB,OAAQ,CAChCuD,QAAQ,CAAGvB,CAAC,EAAKd,wBAAwB,CAAC,SAAS,CAAEc,CAAC,CAACL,MAAM,CAACP,KAAK,CAAE,CACrEuC,IAAI,CAAE,CAAE,CACRd,SAAS,CAAC,kIAAkI,CAC5Ia,QAAQ,MACT,CAAC,EACC,CAAC,cACNtF,KAAA,QAAA0E,QAAA,eACE5E,IAAA,UAAO2E,SAAS,CAAC,yCAAyC,CAAAC,QAAA,CAAC,eAAa,CAAO,CAAC,cAChF5E,IAAA,UACEmF,IAAI,CAAC,OAAO,CACZjC,KAAK,CAAEpC,gBAAgB,CAACkB,KAAM,CAC9BqD,QAAQ,CAAGvB,CAAC,EAAKd,wBAAwB,CAAC,OAAO,CAAEc,CAAC,CAACL,MAAM,CAACP,KAAK,CAAE,CACnEyB,SAAS,CAAC,kIAAkI,CAC7I,CAAC,EACC,CAAC,cACNzE,KAAA,QAAA0E,QAAA,eACE5E,IAAA,UAAO2E,SAAS,CAAC,yCAAyC,CAAAC,QAAA,CAAC,SAAO,CAAO,CAAC,cAC1E5E,IAAA,UACEmF,IAAI,CAAC,KAAK,CACVjC,KAAK,CAAEpC,gBAAgB,CAAC4B,OAAQ,CAChC2C,QAAQ,CAAGvB,CAAC,EAAKd,wBAAwB,CAAC,SAAS,CAAEc,CAAC,CAACL,MAAM,CAACP,KAAK,CAAE,CACrEyB,SAAS,CAAC,kIAAkI,CAC5Ie,WAAW,CAAC,8BAA8B,CAC3C,CAAC,EACC,CAAC,EACH,CAAC,cAGNxF,KAAA,QAAKyE,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnB5E,IAAA,OAAI2E,SAAS,CAAC,wCAAwC,CAAAC,QAAA,CAAC,yBAAuB,CAAI,CAAC,cACnF1E,KAAA,QAAKyE,SAAS,CAAC,uCAAuC,CAAAC,QAAA,eACpD1E,KAAA,QAAA0E,QAAA,eACE5E,IAAA,UAAO2E,SAAS,CAAC,yCAAyC,CAAAC,QAAA,CAAC,+BAA6B,CAAO,CAAC,cAChG5E,IAAA,UACEmF,IAAI,CAAC,MAAM,CACXjC,KAAK,CAAEpC,gBAAgB,CAACmB,SAAU,CAClCoD,QAAQ,CAAGvB,CAAC,EAAKd,wBAAwB,CAAC,WAAW,CAAEc,CAAC,CAACL,MAAM,CAACP,KAAK,CAAE,CACvEyB,SAAS,CAAC,kIAAkI,CAC7I,CAAC,EACC,CAAC,cACNzE,KAAA,QAAA0E,QAAA,eACE5E,IAAA,UAAO2E,SAAS,CAAC,yCAAyC,CAAAC,QAAA,CAAC,yBAAuB,CAAO,CAAC,cAC1F5E,IAAA,UACEmF,IAAI,CAAC,MAAM,CACXjC,KAAK,CAAEpC,gBAAgB,CAACoB,aAAc,CACtCmD,QAAQ,CAAGvB,CAAC,EAAKd,wBAAwB,CAAC,eAAe,CAAEc,CAAC,CAACL,MAAM,CAACP,KAAK,CAAE,CAC3EyB,SAAS,CAAC,kIAAkI,CAC7I,CAAC,EACC,CAAC,cACNzE,KAAA,QAAA0E,QAAA,eACE5E,IAAA,UAAO2E,SAAS,CAAC,yCAAyC,CAAAC,QAAA,CAAC,UAAQ,CAAO,CAAC,cAC3E1E,KAAA,WACEgD,KAAK,CAAEpC,gBAAgB,CAACqB,QAAS,CACjCkD,QAAQ,CAAGvB,CAAC,EAAKd,wBAAwB,CAAC,UAAU,CAAEc,CAAC,CAACL,MAAM,CAACP,KAAK,CAAE,CACtEyB,SAAS,CAAC,kIAAkI,CAAAC,QAAA,eAE5I5E,IAAA,WAAQkD,KAAK,CAAC,KAAK,CAAA0B,QAAA,CAAC,uBAAqB,CAAQ,CAAC,cAClD5E,IAAA,WAAQkD,KAAK,CAAC,KAAK,CAAA0B,QAAA,CAAC,iBAAe,CAAQ,CAAC,cAC5C5E,IAAA,WAAQkD,KAAK,CAAC,KAAK,CAAA0B,QAAA,CAAC,YAAU,CAAQ,CAAC,cACvC5E,IAAA,WAAQkD,KAAK,CAAC,KAAK,CAAA0B,QAAA,CAAC,qBAAmB,CAAQ,CAAC,EAC1C,CAAC,EACN,CAAC,cACN1E,KAAA,QAAA0E,QAAA,eACE5E,IAAA,UAAO2E,SAAS,CAAC,yCAAyC,CAAAC,QAAA,CAAC,cAAY,CAAO,CAAC,cAC/E5E,IAAA,UACEmF,IAAI,CAAC,QAAQ,CACbjC,KAAK,CAAEpC,gBAAgB,CAACsB,OAAQ,CAChCiD,QAAQ,CAAGvB,CAAC,EAAKd,wBAAwB,CAAC,SAAS,CAAE2C,UAAU,CAAC7B,CAAC,CAACL,MAAM,CAACP,KAAK,CAAC,EAAI,CAAC,CAAE,CACtF0C,IAAI,CAAC,MAAM,CACXC,GAAG,CAAC,GAAG,CACPC,GAAG,CAAC,KAAK,CACTnB,SAAS,CAAC,kIAAkI,CAC7I,CAAC,EACC,CAAC,EACH,CAAC,EACH,CAAC,cAGNzE,KAAA,QAAKyE,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnB5E,IAAA,OAAI2E,SAAS,CAAC,wCAAwC,CAAAC,QAAA,CAAC,uBAAqB,CAAI,CAAC,cACjF1E,KAAA,QAAKyE,SAAS,CAAC,wBAAwB,CAAAC,QAAA,eACrC1E,KAAA,QAAA0E,QAAA,eACE5E,IAAA,UAAO2E,SAAS,CAAC,yCAAyC,CAAAC,QAAA,CAAC,2BAAyB,CAAO,CAAC,cAC5F5E,IAAA,UACEmF,IAAI,CAAC,MAAM,CACXjC,KAAK,CAAEpC,gBAAgB,CAACyB,aAAc,CACtC8C,QAAQ,CAAGvB,CAAC,EAAKd,wBAAwB,CAAC,eAAe,CAAEc,CAAC,CAACL,MAAM,CAACP,KAAK,CAAE,CAC3EyB,SAAS,CAAC,kIAAkI,CAC5Ie,WAAW,CAAC,oCAAoC,CACjD,CAAC,EACC,CAAC,cACNxF,KAAA,QAAA0E,QAAA,eACE5E,IAAA,UAAO2E,SAAS,CAAC,yCAAyC,CAAAC,QAAA,CAAC,gBAAc,CAAO,CAAC,cACjF5E,IAAA,aACEkD,KAAK,CAAEpC,gBAAgB,CAAC0B,aAAc,CACtC6C,QAAQ,CAAGvB,CAAC,EAAKd,wBAAwB,CAAC,eAAe,CAAEc,CAAC,CAACL,MAAM,CAACP,KAAK,CAAE,CAC3EuC,IAAI,CAAE,CAAE,CACRd,SAAS,CAAC,kIAAkI,CAC5Ie,WAAW,CAAC,8CAA8C,CAC3D,CAAC,EACC,CAAC,cACNxF,KAAA,QAAA0E,QAAA,eACE5E,IAAA,UAAO2E,SAAS,CAAC,yCAAyC,CAAAC,QAAA,CAAC,iBAAe,CAAO,CAAC,cAClF5E,IAAA,aACEkD,KAAK,CAAEpC,gBAAgB,CAAC2B,cAAe,CACvC4C,QAAQ,CAAGvB,CAAC,EAAKd,wBAAwB,CAAC,gBAAgB,CAAEc,CAAC,CAACL,MAAM,CAACP,KAAK,CAAE,CAC5EuC,IAAI,CAAE,CAAE,CACRd,SAAS,CAAC,kIAAkI,CAC5Ie,WAAW,CAAC,4BAA4B,CACzC,CAAC,EACC,CAAC,EACH,CAAC,EACH,CAAC,cAGN1F,IAAA,QAAK2E,SAAS,CAAC,oCAAoC,CAAAC,QAAA,cACjD1E,KAAA,QAAKyE,SAAS,CAAC,mCAAmC,CAAAC,QAAA,eAChD5E,IAAA,QAAK2E,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,mBAEvC,CAAK,CAAC,cACN1E,KAAA,WACE6E,OAAO,CAAEV,0BAA2B,CACpC0B,QAAQ,CAAE3E,cAAc,EAAI,CAACjB,aAAa,CAAC,OAAO,CAAE,CACpDwE,SAAS,CAAC,uIAAuI,CAAAC,QAAA,eAEjJ5E,IAAA,CAACjB,IAAI,EAAC4F,SAAS,CAAC,cAAc,CAAE,CAAC,CAChCvD,cAAc,CAAG,WAAW,CAAG,wBAAwB,EAClD,CAAC,EACN,CAAC,CACH,CAAC,EACH,CAAC,CACH,CACN,CAEAd,SAAS,GAAK,OAAO,EAAIH,aAAa,CAAC,OAAO,CAAC,eAC9CH,IAAA,CAACT,cAAc,GAAE,CAClB,CAEAe,SAAS,GAAK,MAAM,eACnBN,IAAA,QAAK2E,SAAS,CAAC,WAAW,CAAAC,QAAA,cACxB1E,KAAA,QAAA0E,QAAA,eACE5E,IAAA,OAAI2E,SAAS,CAAC,wCAAwC,CAAAC,QAAA,CAAC,iBAAe,CAAI,CAAC,cAC3E5E,IAAA,QAAK2E,SAAS,CAAC,2DAA2D,CAAAC,QAAA,cACxE1E,KAAA,QAAKyE,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnB5E,IAAA,QAAK2E,SAAS,CAAC,eAAe,CAAAC,QAAA,cAC5B5E,IAAA,CAACf,QAAQ,EAAC0F,SAAS,CAAC,yBAAyB,CAAE,CAAC,CAC7C,CAAC,cACNzE,KAAA,QAAKyE,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnB5E,IAAA,OAAI2E,SAAS,CAAC,qCAAqC,CAAAC,QAAA,CAAC,0BAEpD,CAAI,CAAC,cACL5E,IAAA,QAAK2E,SAAS,CAAC,8BAA8B,CAAAC,QAAA,cAC3C5E,IAAA,MAAA4E,QAAA,CAAG,wJAGH,CAAG,CAAC,CACD,CAAC,EACH,CAAC,EACH,CAAC,CACH,CAAC,CAELlE,OAAO,eACNV,IAAA,QAAK2E,SAAS,wBAAA7B,MAAA,CACZpC,OAAO,CAACsF,QAAQ,CAAC,OAAO,CAAC,CACrB,8CAA8C,CAC9C,oDAAoD,CACvD,CAAApB,QAAA,CACAlE,OAAO,CACL,CACN,cAEDR,KAAA,QAAKyE,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxB1E,KAAA,WACE6E,OAAO,CAAElC,oBAAqB,CAC9BkD,QAAQ,CAAEvF,OAAQ,CAClBmE,SAAS,CAAC,+KAA+K,CAAAC,QAAA,eAEzL5E,IAAA,CAACf,QAAQ,EAAC0F,SAAS,CAAC,cAAc,CAAE,CAAC,CACpCnE,OAAO,CAAG,iBAAiB,CAAG,sBAAsB,EAC/C,CAAC,cAETN,KAAA,WACE6E,OAAO,CAAEhC,kBAAmB,CAC5BgD,QAAQ,CAAEvF,OAAQ,CAClBmE,SAAS,CAAC,sKAAsK,CAAAC,QAAA,eAEhL5E,IAAA,CAACf,QAAQ,EAAC0F,SAAS,CAAC,cAAc,CAAE,CAAC,CACpCnE,OAAO,CAAG,YAAY,CAAG,kCAAkC,EACtD,CAAC,cAETN,KAAA,QAAKyE,SAAS,CAAC,4BAA4B,CAAAC,QAAA,eACzC1E,KAAA,MAAA0E,QAAA,eAAG5E,IAAA,WAAA4E,QAAA,CAAQ,uBAAqB,CAAQ,CAAC,oCAAiC,EAAG,CAAC,cAC9E1E,KAAA,MAAA0E,QAAA,eAAG5E,IAAA,WAAA4E,QAAA,CAAQ,iBAAe,CAAQ,CAAC,6FAA0F,EAAG,CAAC,EAC9H,CAAC,EACH,CAAC,EACH,CAAC,CACH,CACN,EACE,CAAC,EACH,CAAC,CACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAA/F,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}