# Firebase Configuration for Development
# Updated with actual Firebase service account configuration

# Firebase Project Configuration (from Firebase Console > Project Settings > Your apps)
REACT_APP_FIREBASE_API_KEY=AIzaSyC8iaY7fTWuP_FIg_aUQSDMdYTCooSkxX8
REACT_APP_FIREBASE_AUTH_DOMAIN=cyber-pos-system.firebaseapp.com
REACT_APP_FIREBASE_PROJECT_ID=cyber-pos-system
REACT_APP_FIREBASE_STORAGE_BUCKET=cyber-pos-system.firebasestorage.app
REACT_APP_FIREBASE_MESSAGING_SENDER_ID=************
REACT_APP_FIREBASE_APP_ID=1:************:web:91cca0802b7f2df084e0b8
REACT_APP_FIREBASE_MEASUREMENT_ID=G-FK2H5TP1V0

# Note: Service account configuration removed - not needed for web app authentication

# Environment
REACT_APP_ENVIRONMENT=development

# Firebase Emulator Configuration
REACT_APP_USE_FIREBASE_EMULATOR=false
REACT_APP_FIREBASE_AUTH_EMULATOR_HOST=localhost:9099
REACT_APP_FIREBASE_FIRESTORE_EMULATOR_HOST=localhost:8080
REACT_APP_FIREBASE_STORAGE_EMULATOR_HOST=localhost:9199

# Note: This configuration now includes actual Firebase web app credentials
# For production builds, set REACT_APP_USE_FIREBASE_EMULATOR=false
# Keep the Firebase configuration secure and never commit sensitive keys to public repositories
