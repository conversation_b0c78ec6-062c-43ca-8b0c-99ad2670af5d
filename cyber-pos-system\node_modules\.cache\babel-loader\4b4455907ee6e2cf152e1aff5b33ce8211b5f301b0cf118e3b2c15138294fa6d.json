{"ast": null, "code": "import _objectSpread from\"E:/FX/Cyber POS/cyber-pos-system/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import React,{useState}from'react';import{Upload,Download,Edit,Trash2,Package,AlertTriangle,CheckCircle,X,FileSpreadsheet}from'lucide-react';import{useProducts}from'../../hooks/useProducts';import{parseImportFile,generateImportTemplate}from'../../utils/excelImport';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const BulkOperations=_ref=>{var _importResult$data,_importResult$data2,_importResult$data3,_importResult$data4,_importResult$data5;let{selectedProducts,onClose,onClearSelection}=_ref;const{updateProduct,deleteProduct,createProduct}=useProducts();const[operation,setOperation]=useState(null);const[bulkUpdateData,setBulkUpdateData]=useState({category:'',priceAdjustment:'',adjustmentType:'percentage',stockAdjustment:'',reorderLevel:'',isActive:''});const[processing,setProcessing]=useState(false);const[importFile,setImportFile]=useState(null);const[importResult,setImportResult]=useState(null);const[showImportPreview,setShowImportPreview]=useState(false);// Handle bulk price update\nconst handleBulkUpdate=async()=>{if(selectedProducts.length===0)return;setProcessing(true);try{for(const product of selectedProducts){const updates={};// Category update\nif(bulkUpdateData.category){updates.category=bulkUpdateData.category;}// Price adjustment\nif(bulkUpdateData.priceAdjustment){const adjustment=parseFloat(bulkUpdateData.priceAdjustment);if(bulkUpdateData.adjustmentType==='percentage'){updates.price=product.price*(1+adjustment/100);}else{updates.price=product.price+adjustment;}}// Stock adjustment\nif(bulkUpdateData.stockAdjustment){const adjustment=parseInt(bulkUpdateData.stockAdjustment);updates.stockQuantity=Math.max(0,product.stockQuantity+adjustment);}// Reorder level\nif(bulkUpdateData.reorderLevel){updates.reorderLevel=parseInt(bulkUpdateData.reorderLevel);}// Active status\nif(bulkUpdateData.isActive!==''){updates.isActive=bulkUpdateData.isActive==='true';}if(Object.keys(updates).length>0){await updateProduct(product.id,updates);}}alert(\"Successfully updated \".concat(selectedProducts.length,\" products\"));onClearSelection();onClose();}catch(error){console.error('Bulk update error:',error);alert('Error updating products. Please try again.');}finally{setProcessing(false);}};// Handle bulk delete\nconst handleBulkDelete=async()=>{if(selectedProducts.length===0)return;const confirmed=window.confirm(\"Are you sure you want to delete \".concat(selectedProducts.length,\" products? This action cannot be undone.\"));if(!confirmed)return;setProcessing(true);try{for(const product of selectedProducts){await deleteProduct(product.id);}alert(\"Successfully deleted \".concat(selectedProducts.length,\" products\"));onClearSelection();onClose();}catch(error){console.error('Bulk delete error:',error);alert('Error deleting products. Please try again.');}finally{setProcessing(false);}};// Export products to CSV\nconst handleExport=()=>{const csvHeaders=['ID','Name','Description','Category','Price','Stock Quantity','Reorder Level','Has Expiry','Expiry Date','Is Active','Created At'];const csvData=selectedProducts.map(product=>{var _product$expiryDate;return[product.id,product.name,product.description,product.category,product.price,product.stockQuantity,product.reorderLevel,product.hasExpiry,((_product$expiryDate=product.expiryDate)===null||_product$expiryDate===void 0?void 0:_product$expiryDate.toISOString())||'',product.isActive,product.createdAt.toISOString()];});const csvContent=[csvHeaders,...csvData].map(row=>row.map(field=>\"\\\"\".concat(field,\"\\\"\")).join(',')).join('\\n');const blob=new Blob([csvContent],{type:'text/csv'});const url=window.URL.createObjectURL(blob);const link=document.createElement('a');link.href=url;link.download=\"products-export-\".concat(new Date().toISOString().split('T')[0],\".csv\");link.click();window.URL.revokeObjectURL(url);alert(\"Exported \".concat(selectedProducts.length,\" products to CSV\"));};// Handle file parsing and preview\nconst handleFileSelect=async file=>{setImportFile(file);setImportResult(null);setShowImportPreview(false);if(!file)return;setProcessing(true);try{const result=await parseImportFile(file);setImportResult(result);if(result.success&&result.data&&result.data.length>0){setShowImportPreview(true);}else if(result.errors){alert(\"Import validation failed:\\n\".concat(result.errors.join('\\n')));}}catch(error){console.error('File parsing error:',error);alert('Error parsing file. Please check the file format.');}finally{setProcessing(false);}};// Handle actual import after preview confirmation\nconst handleConfirmImport=async()=>{if(!importResult||!importResult.data)return;// Show confirmation dialog\nconst totalProducts=importResult.data.length;const confirmMessage=\"Are you sure you want to import \".concat(totalProducts,\" product\").concat(totalProducts>1?'s':'',\" into your inventory?\\n\\nThis action cannot be undone.\");if(!window.confirm(confirmMessage)){return;}setProcessing(true);try{let importedCount=0;let failedCount=0;const failedProducts=[];for(const productData of importResult.data){try{await createProduct(productData);importedCount++;}catch(error){console.error('Error creating product:',error);failedCount++;failedProducts.push(productData.name||'Unknown product');}}// Show detailed results\nif(importedCount>0){let message=\"\\u2705 Successfully imported \".concat(importedCount,\" product\").concat(importedCount>1?'s':'',\" into your inventory!\");if(failedCount>0){message+=\"\\n\\n\\u26A0\\uFE0F \".concat(failedCount,\" product\").concat(failedCount>1?'s':'',\" failed to import:\");message+=\"\\n\".concat(failedProducts.slice(0,5).join('\\n'));if(failedProducts.length>5){message+=\"\\n... and \".concat(failedProducts.length-5,\" more\");}}alert(message);}else{alert('❌ No products were imported. Please check the data and try again.');}// Reset the form\nsetImportFile(null);setImportResult(null);setShowImportPreview(false);onClearSelection();onClose();}catch(error){console.error('Import error:',error);alert('❌ Error importing products. Please try again.');}finally{setProcessing(false);}};// Handle template download\nconst handleDownloadTemplate=()=>{generateImportTemplate();};return/*#__PURE__*/_jsx(\"div\",{className:\"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"relative top-20 mx-auto p-5 border w-full max-w-2xl shadow-lg rounded-md bg-white\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between mb-6\",children:[/*#__PURE__*/_jsxs(\"h3\",{className:\"text-lg font-medium text-gray-900 flex items-center\",children:[/*#__PURE__*/_jsx(Package,{className:\"h-5 w-5 mr-2\"}),\"Bulk Operations (\",selectedProducts.length,\" products selected)\"]}),/*#__PURE__*/_jsx(\"button\",{onClick:onClose,className:\"text-gray-400 hover:text-gray-600\",children:/*#__PURE__*/_jsx(X,{className:\"h-5 w-5\"})})]}),!operation?/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-2 gap-4\",children:[/*#__PURE__*/_jsxs(\"button\",{onClick:()=>setOperation('update'),className:\"p-4 border border-gray-300 rounded-lg hover:bg-gray-50 text-left\",children:[/*#__PURE__*/_jsx(Edit,{className:\"h-6 w-6 text-blue-600 mb-2\"}),/*#__PURE__*/_jsx(\"h4\",{className:\"font-medium text-gray-900\",children:\"Bulk Update\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-sm text-gray-500\",children:\"Update multiple products at once\"})]}),/*#__PURE__*/_jsxs(\"button\",{onClick:()=>setOperation('delete'),className:\"p-4 border border-gray-300 rounded-lg hover:bg-gray-50 text-left\",children:[/*#__PURE__*/_jsx(Trash2,{className:\"h-6 w-6 text-red-600 mb-2\"}),/*#__PURE__*/_jsx(\"h4\",{className:\"font-medium text-gray-900\",children:\"Bulk Delete\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-sm text-gray-500\",children:\"Delete selected products\"})]}),/*#__PURE__*/_jsxs(\"button\",{onClick:()=>setOperation('export'),className:\"p-4 border border-gray-300 rounded-lg hover:bg-gray-50 text-left\",children:[/*#__PURE__*/_jsx(Download,{className:\"h-6 w-6 text-green-600 mb-2\"}),/*#__PURE__*/_jsx(\"h4\",{className:\"font-medium text-gray-900\",children:\"Export\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-sm text-gray-500\",children:\"Export to CSV file\"})]}),/*#__PURE__*/_jsxs(\"button\",{onClick:()=>setOperation('import'),className:\"p-4 border border-gray-300 rounded-lg hover:bg-gray-50 text-left\",children:[/*#__PURE__*/_jsx(Upload,{className:\"h-6 w-6 text-purple-600 mb-2\"}),/*#__PURE__*/_jsx(\"h4\",{className:\"font-medium text-gray-900\",children:\"Import\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-sm text-gray-500\",children:\"Import from Excel or CSV file\"})]})]}):operation==='update'?/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-4\",children:[/*#__PURE__*/_jsx(\"h4\",{className:\"font-medium text-gray-900\",children:\"Bulk Update Products\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-2 gap-4\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700 mb-1\",children:\"Category (leave empty to keep current)\"}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",value:bulkUpdateData.category,onChange:e=>setBulkUpdateData(prev=>_objectSpread(_objectSpread({},prev),{},{category:e.target.value})),className:\"w-full border border-gray-300 rounded-md px-3 py-2\",placeholder:\"New category\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700 mb-1\",children:\"Price Adjustment\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex\",children:[/*#__PURE__*/_jsxs(\"select\",{value:bulkUpdateData.adjustmentType,onChange:e=>setBulkUpdateData(prev=>_objectSpread(_objectSpread({},prev),{},{adjustmentType:e.target.value})),className:\"border border-gray-300 rounded-l-md px-3 py-2\",children:[/*#__PURE__*/_jsx(\"option\",{value:\"percentage\",children:\"%\"}),/*#__PURE__*/_jsx(\"option\",{value:\"fixed\",children:\"KSh\"})]}),/*#__PURE__*/_jsx(\"input\",{type:\"number\",value:bulkUpdateData.priceAdjustment,onChange:e=>setBulkUpdateData(prev=>_objectSpread(_objectSpread({},prev),{},{priceAdjustment:e.target.value})),className:\"flex-1 border border-gray-300 rounded-r-md px-3 py-2\",placeholder:\"0\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700 mb-1\",children:\"Stock Adjustment\"}),/*#__PURE__*/_jsx(\"input\",{type:\"number\",value:bulkUpdateData.stockAdjustment,onChange:e=>setBulkUpdateData(prev=>_objectSpread(_objectSpread({},prev),{},{stockAdjustment:e.target.value})),className:\"w-full border border-gray-300 rounded-md px-3 py-2\",placeholder:\"+/- quantity\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700 mb-1\",children:\"Reorder Level\"}),/*#__PURE__*/_jsx(\"input\",{type:\"number\",value:bulkUpdateData.reorderLevel,onChange:e=>setBulkUpdateData(prev=>_objectSpread(_objectSpread({},prev),{},{reorderLevel:e.target.value})),className:\"w-full border border-gray-300 rounded-md px-3 py-2\",placeholder:\"New reorder level\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700 mb-1\",children:\"Active Status\"}),/*#__PURE__*/_jsxs(\"select\",{value:bulkUpdateData.isActive,onChange:e=>setBulkUpdateData(prev=>_objectSpread(_objectSpread({},prev),{},{isActive:e.target.value})),className:\"w-full border border-gray-300 rounded-md px-3 py-2\",children:[/*#__PURE__*/_jsx(\"option\",{value:\"\",children:\"Keep current\"}),/*#__PURE__*/_jsx(\"option\",{value:\"true\",children:\"Active\"}),/*#__PURE__*/_jsx(\"option\",{value:\"false\",children:\"Inactive\"})]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex justify-end space-x-3 pt-4\",children:[/*#__PURE__*/_jsx(\"button\",{onClick:()=>setOperation(null),className:\"px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50\",children:\"Back\"}),/*#__PURE__*/_jsx(\"button\",{onClick:handleBulkUpdate,disabled:processing,className:\"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50\",children:processing?'Updating...':'Update Products'})]})]}):operation==='delete'?/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-4\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center text-red-600\",children:[/*#__PURE__*/_jsx(AlertTriangle,{className:\"h-5 w-5 mr-2\"}),/*#__PURE__*/_jsx(\"h4\",{className:\"font-medium\",children:\"Delete Selected Products\"})]}),/*#__PURE__*/_jsxs(\"p\",{className:\"text-gray-600\",children:[\"You are about to delete \",selectedProducts.length,\" products. This action cannot be undone.\"]}),/*#__PURE__*/_jsxs(\"div\",{className:\"bg-red-50 border border-red-200 rounded-md p-3\",children:[/*#__PURE__*/_jsx(\"p\",{className:\"text-sm text-red-800\",children:\"Products to be deleted:\"}),/*#__PURE__*/_jsxs(\"ul\",{className:\"mt-2 text-sm text-red-700 max-h-32 overflow-y-auto\",children:[selectedProducts.slice(0,10).map(product=>/*#__PURE__*/_jsxs(\"li\",{children:[\"\\u2022 \",product.name]},product.id)),selectedProducts.length>10&&/*#__PURE__*/_jsxs(\"li\",{children:[\"\\u2022 ... and \",selectedProducts.length-10,\" more\"]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex justify-end space-x-3 pt-4\",children:[/*#__PURE__*/_jsx(\"button\",{onClick:()=>setOperation(null),className:\"px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50\",children:\"Back\"}),/*#__PURE__*/_jsx(\"button\",{onClick:handleBulkDelete,disabled:processing,className:\"px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 disabled:opacity-50\",children:processing?'Deleting...':'Delete Products'})]})]}):operation==='export'?/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-4\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center text-green-600\",children:[/*#__PURE__*/_jsx(Download,{className:\"h-5 w-5 mr-2\"}),/*#__PURE__*/_jsx(\"h4\",{className:\"font-medium\",children:\"Export Products\"})]}),/*#__PURE__*/_jsxs(\"p\",{className:\"text-gray-600\",children:[\"Export \",selectedProducts.length,\" selected products to a CSV file.\"]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex justify-end space-x-3 pt-4\",children:[/*#__PURE__*/_jsx(\"button\",{onClick:()=>setOperation(null),className:\"px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50\",children:\"Back\"}),/*#__PURE__*/_jsx(\"button\",{onClick:handleExport,className:\"px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700\",children:\"Export to CSV\"})]})]}):operation==='import'?/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-4\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center text-purple-600\",children:[/*#__PURE__*/_jsx(Upload,{className:\"h-5 w-5 mr-2\"}),/*#__PURE__*/_jsx(\"h4\",{className:\"font-medium\",children:\"Import Products\"})]}),/*#__PURE__*/_jsxs(\"button\",{onClick:handleDownloadTemplate,className:\"flex items-center text-sm text-blue-600 hover:text-blue-800\",children:[/*#__PURE__*/_jsx(FileSpreadsheet,{className:\"h-4 w-4 mr-1\"}),\"Download Template\"]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"bg-blue-50 border border-blue-200 rounded-md p-3\",children:[/*#__PURE__*/_jsxs(\"p\",{className:\"text-sm text-blue-800\",children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Supported formats:\"}),\" Excel (.xlsx, .xls) and CSV (.csv) files\"]}),/*#__PURE__*/_jsxs(\"p\",{className:\"text-sm text-blue-700 mt-1\",children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Required columns:\"}),\" Name, Category, Price, Stock Quantity\"]}),/*#__PURE__*/_jsxs(\"p\",{className:\"text-sm text-blue-700\",children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Optional columns:\"}),\" Description, Reorder Level, Has Expiry, Expiry Date, Is Active\"]})]}),!showImportPreview?/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-4\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{htmlFor:\"file-input\",className:\"block text-sm font-medium text-gray-700 mb-2\",children:\"Select File\"}),/*#__PURE__*/_jsx(\"input\",{id:\"file-input\",type:\"file\",accept:\".csv,.xlsx,.xls\",onChange:e=>{var _e$target$files;return handleFileSelect(((_e$target$files=e.target.files)===null||_e$target$files===void 0?void 0:_e$target$files[0])||null);},className:\"w-full border border-gray-300 rounded-md px-3 py-2\"}),processing&&/*#__PURE__*/_jsx(\"p\",{className:\"text-sm text-gray-600 mt-2\",children:\"Parsing file...\"})]}),importFile&&!processing&&importResult&&!importResult.success&&/*#__PURE__*/_jsxs(\"div\",{className:\"bg-red-50 border border-red-200 rounded-md p-3\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center text-red-800\",children:[/*#__PURE__*/_jsx(AlertTriangle,{className:\"h-4 w-4 mr-2\"}),/*#__PURE__*/_jsx(\"span\",{className:\"font-medium\",children:\"File validation failed\"})]}),importResult.errors&&/*#__PURE__*/_jsx(\"ul\",{className:\"text-sm text-red-700 mt-1 list-disc list-inside\",children:importResult.errors.map((error,index)=>/*#__PURE__*/_jsx(\"li\",{children:error},index))})]})]}):/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-4\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"bg-green-50 border border-green-200 rounded-md p-4\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center text-green-800\",children:[/*#__PURE__*/_jsx(CheckCircle,{className:\"h-5 w-5 mr-2\"}),/*#__PURE__*/_jsx(\"span\",{className:\"font-medium\",children:\"Import Preview Ready\"})]}),/*#__PURE__*/_jsxs(\"span\",{className:\"bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded-full\",children:[(importResult===null||importResult===void 0?void 0:(_importResult$data=importResult.data)===null||_importResult$data===void 0?void 0:_importResult$data.length)||0,\" products\"]})]}),/*#__PURE__*/_jsx(\"p\",{className:\"text-sm text-green-700 mt-2\",children:\"Review the products below and click \\\"Execute Import\\\" to add them to your inventory.\"}),(importResult===null||importResult===void 0?void 0:importResult.warnings)&&importResult.warnings.length>0&&/*#__PURE__*/_jsxs(\"div\",{className:\"mt-3 bg-yellow-50 border border-yellow-200 rounded-md p-2\",children:[/*#__PURE__*/_jsxs(\"p\",{className:\"text-sm text-yellow-800 font-medium flex items-center\",children:[/*#__PURE__*/_jsx(AlertTriangle,{className:\"h-4 w-4 mr-1\"}),\"Warnings:\"]}),/*#__PURE__*/_jsx(\"ul\",{className:\"text-sm text-yellow-700 mt-1 list-disc list-inside ml-5\",children:importResult.warnings.map((warning,index)=>/*#__PURE__*/_jsx(\"li\",{children:warning},index))})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"bg-white border border-gray-200 rounded-lg overflow-hidden\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"bg-gray-50 px-4 py-2 border-b border-gray-200\",children:/*#__PURE__*/_jsx(\"h5\",{className:\"text-sm font-medium text-gray-900\",children:\"Products to Import (Preview)\"})}),/*#__PURE__*/_jsx(\"div\",{className:\"max-h-64 overflow-y-auto\",children:/*#__PURE__*/_jsxs(\"table\",{className:\"min-w-full text-sm\",children:[/*#__PURE__*/_jsx(\"thead\",{className:\"bg-gray-50 sticky top-0\",children:/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"th\",{className:\"px-4 py-2 text-left font-medium text-gray-700\",children:\"#\"}),/*#__PURE__*/_jsx(\"th\",{className:\"px-4 py-2 text-left font-medium text-gray-700\",children:\"Name\"}),/*#__PURE__*/_jsx(\"th\",{className:\"px-4 py-2 text-left font-medium text-gray-700\",children:\"Category\"}),/*#__PURE__*/_jsx(\"th\",{className:\"px-4 py-2 text-left font-medium text-gray-700\",children:\"Price\"}),/*#__PURE__*/_jsx(\"th\",{className:\"px-4 py-2 text-left font-medium text-gray-700\",children:\"Stock\"})]})}),/*#__PURE__*/_jsxs(\"tbody\",{children:[importResult===null||importResult===void 0?void 0:(_importResult$data2=importResult.data)===null||_importResult$data2===void 0?void 0:_importResult$data2.slice(0,15).map((product,index)=>{var _product$price;return/*#__PURE__*/_jsxs(\"tr\",{className:\"border-t border-gray-200 hover:bg-gray-50\",children:[/*#__PURE__*/_jsx(\"td\",{className:\"px-4 py-2 text-gray-500\",children:index+1}),/*#__PURE__*/_jsx(\"td\",{className:\"px-4 py-2 font-medium\",children:product.name}),/*#__PURE__*/_jsx(\"td\",{className:\"px-4 py-2\",children:product.category}),/*#__PURE__*/_jsxs(\"td\",{className:\"px-4 py-2 text-green-600 font-medium\",children:[\"KSh \",(_product$price=product.price)===null||_product$price===void 0?void 0:_product$price.toLocaleString()]}),/*#__PURE__*/_jsx(\"td\",{className:\"px-4 py-2\",children:product.stockQuantity})]},index);}),((importResult===null||importResult===void 0?void 0:(_importResult$data3=importResult.data)===null||_importResult$data3===void 0?void 0:_importResult$data3.length)||0)>15&&/*#__PURE__*/_jsx(\"tr\",{className:\"border-t border-gray-200 bg-gray-50\",children:/*#__PURE__*/_jsxs(\"td\",{colSpan:5,className:\"px-4 py-3 text-center text-gray-500 font-medium\",children:[\"... and \",((importResult===null||importResult===void 0?void 0:(_importResult$data4=importResult.data)===null||_importResult$data4===void 0?void 0:_importResult$data4.length)||0)-15,\" more products\"]})})]})]})})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex justify-between items-center pt-4\",children:[/*#__PURE__*/_jsx(\"button\",{onClick:()=>{setOperation(null);setImportFile(null);setImportResult(null);setShowImportPreview(false);},className:\"px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50\",children:\"Back\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex space-x-3\",children:[!showImportPreview&&/*#__PURE__*/_jsxs(\"button\",{onClick:handleDownloadTemplate,className:\"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center\",children:[/*#__PURE__*/_jsx(FileSpreadsheet,{className:\"h-4 w-4 mr-2\"}),\"Download Template\"]}),showImportPreview&&/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(\"button\",{onClick:()=>{setImportFile(null);setImportResult(null);setShowImportPreview(false);},className:\"px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50\",children:\"Choose Different File\"}),/*#__PURE__*/_jsxs(\"button\",{onClick:handleConfirmImport,disabled:processing||!(importResult!==null&&importResult!==void 0&&importResult.data)||importResult.data.length===0,className:\"px-6 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center font-medium\",children:[/*#__PURE__*/_jsx(Upload,{className:\"h-4 w-4 mr-2\"}),processing?'Executing Import...':\"Execute Import (\".concat((importResult===null||importResult===void 0?void 0:(_importResult$data5=importResult.data)===null||_importResult$data5===void 0?void 0:_importResult$data5.length)||0,\" products)\")]})]})]})]})]}):null]})});};export default BulkOperations;", "map": {"version": 3, "names": ["React", "useState", "Upload", "Download", "Edit", "Trash2", "Package", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "CheckCircle", "X", "FileSpreadsheet", "useProducts", "parseImportFile", "generateImportTemplate", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "BulkOperations", "_ref", "_importResult$data", "_importResult$data2", "_importResult$data3", "_importResult$data4", "_importResult$data5", "selectedProducts", "onClose", "onClearSelection", "updateProduct", "deleteProduct", "createProduct", "operation", "setOperation", "bulkUpdateData", "setBulkUpdateData", "category", "priceAdjustment", "adjustmentType", "stockAdjustment", "reorderLevel", "isActive", "processing", "setProcessing", "importFile", "setImportFile", "importResult", "setImportResult", "showImportPreview", "setShowImportPreview", "handleBulkUpdate", "length", "product", "updates", "adjustment", "parseFloat", "price", "parseInt", "stockQuantity", "Math", "max", "Object", "keys", "id", "alert", "concat", "error", "console", "handleBulkDelete", "confirmed", "window", "confirm", "handleExport", "csvHeaders", "csvData", "map", "_product$expiryDate", "name", "description", "hasEx<PERSON>ry", "expiryDate", "toISOString", "createdAt", "csv<PERSON><PERSON>nt", "row", "field", "join", "blob", "Blob", "type", "url", "URL", "createObjectURL", "link", "document", "createElement", "href", "download", "Date", "split", "click", "revokeObjectURL", "handleFileSelect", "file", "result", "success", "data", "errors", "handleConfirmImport", "totalProducts", "confirmMessage", "importedCount", "failedCount", "failedProducts", "productData", "push", "message", "slice", "handleDownloadTemplate", "className", "children", "onClick", "value", "onChange", "e", "prev", "_objectSpread", "target", "placeholder", "disabled", "htmlFor", "accept", "_e$target$files", "files", "index", "warnings", "warning", "_product$price", "toLocaleString", "colSpan"], "sources": ["E:/FX/Cyber POS/cyber-pos-system/src/components/inventory/BulkOperations.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport {\n  Upload,\n  Download,\n  Edit,\n  Trash2,\n  Package,\n  AlertTriangle,\n  CheckCircle,\n  X,\n  FileSpreadsheet\n} from 'lucide-react';\nimport { Product } from '../../types';\nimport { useProducts } from '../../hooks/useProducts';\nimport { parseImportFile, generateImportTemplate, ImportResult } from '../../utils/excelImport';\n\ninterface BulkOperationsProps {\n  selectedProducts: Product[];\n  onClose: () => void;\n  onClearSelection: () => void;\n}\n\nconst BulkOperations: React.FC<BulkOperationsProps> = ({\n  selectedProducts,\n  onClose,\n  onClearSelection\n}) => {\n  const { updateProduct, deleteProduct, createProduct } = useProducts();\n  const [operation, setOperation] = useState<'update' | 'delete' | 'export' | 'import' | null>(null);\n  const [bulkUpdateData, setBulkUpdateData] = useState({\n    category: '',\n    priceAdjustment: '',\n    adjustmentType: 'percentage' as 'percentage' | 'fixed',\n    stockAdjustment: '',\n    reorderLevel: '',\n    isActive: ''\n  });\n  const [processing, setProcessing] = useState(false);\n  const [importFile, setImportFile] = useState<File | null>(null);\n  const [importResult, setImportResult] = useState<ImportResult | null>(null);\n  const [showImportPreview, setShowImportPreview] = useState(false);\n\n  // Handle bulk price update\n  const handleBulkUpdate = async () => {\n    if (selectedProducts.length === 0) return;\n\n    setProcessing(true);\n    try {\n      for (const product of selectedProducts) {\n        const updates: Partial<Product> = {};\n\n        // Category update\n        if (bulkUpdateData.category) {\n          updates.category = bulkUpdateData.category;\n        }\n\n        // Price adjustment\n        if (bulkUpdateData.priceAdjustment) {\n          const adjustment = parseFloat(bulkUpdateData.priceAdjustment);\n          if (bulkUpdateData.adjustmentType === 'percentage') {\n            updates.price = product.price * (1 + adjustment / 100);\n          } else {\n            updates.price = product.price + adjustment;\n          }\n        }\n\n        // Stock adjustment\n        if (bulkUpdateData.stockAdjustment) {\n          const adjustment = parseInt(bulkUpdateData.stockAdjustment);\n          updates.stockQuantity = Math.max(0, product.stockQuantity + adjustment);\n        }\n\n        // Reorder level\n        if (bulkUpdateData.reorderLevel) {\n          updates.reorderLevel = parseInt(bulkUpdateData.reorderLevel);\n        }\n\n        // Active status\n        if (bulkUpdateData.isActive !== '') {\n          updates.isActive = bulkUpdateData.isActive === 'true';\n        }\n\n        if (Object.keys(updates).length > 0) {\n          await updateProduct(product.id, updates);\n        }\n      }\n\n      alert(`Successfully updated ${selectedProducts.length} products`);\n      onClearSelection();\n      onClose();\n    } catch (error) {\n      console.error('Bulk update error:', error);\n      alert('Error updating products. Please try again.');\n    } finally {\n      setProcessing(false);\n    }\n  };\n\n  // Handle bulk delete\n  const handleBulkDelete = async () => {\n    if (selectedProducts.length === 0) return;\n\n    const confirmed = window.confirm(\n      `Are you sure you want to delete ${selectedProducts.length} products? This action cannot be undone.`\n    );\n\n    if (!confirmed) return;\n\n    setProcessing(true);\n    try {\n      for (const product of selectedProducts) {\n        await deleteProduct(product.id);\n      }\n\n      alert(`Successfully deleted ${selectedProducts.length} products`);\n      onClearSelection();\n      onClose();\n    } catch (error) {\n      console.error('Bulk delete error:', error);\n      alert('Error deleting products. Please try again.');\n    } finally {\n      setProcessing(false);\n    }\n  };\n\n  // Export products to CSV\n  const handleExport = () => {\n    const csvHeaders = [\n      'ID',\n      'Name',\n      'Description',\n      'Category',\n      'Price',\n      'Stock Quantity',\n      'Reorder Level',\n      'Has Expiry',\n      'Expiry Date',\n      'Is Active',\n      'Created At'\n    ];\n\n    const csvData = selectedProducts.map(product => [\n      product.id,\n      product.name,\n      product.description,\n      product.category,\n      product.price,\n      product.stockQuantity,\n      product.reorderLevel,\n      product.hasExpiry,\n      product.expiryDate?.toISOString() || '',\n      product.isActive,\n      product.createdAt.toISOString()\n    ]);\n\n    const csvContent = [csvHeaders, ...csvData]\n      .map(row => row.map(field => `\"${field}\"`).join(','))\n      .join('\\n');\n\n    const blob = new Blob([csvContent], { type: 'text/csv' });\n    const url = window.URL.createObjectURL(blob);\n    const link = document.createElement('a');\n    link.href = url;\n    link.download = `products-export-${new Date().toISOString().split('T')[0]}.csv`;\n    link.click();\n    window.URL.revokeObjectURL(url);\n\n    alert(`Exported ${selectedProducts.length} products to CSV`);\n  };\n\n  // Handle file parsing and preview\n  const handleFileSelect = async (file: File | null) => {\n    setImportFile(file);\n    setImportResult(null);\n    setShowImportPreview(false);\n\n    if (!file) return;\n\n    setProcessing(true);\n    try {\n      const result = await parseImportFile(file);\n      setImportResult(result);\n\n      if (result.success && result.data && result.data.length > 0) {\n        setShowImportPreview(true);\n      } else if (result.errors) {\n        alert(`Import validation failed:\\n${result.errors.join('\\n')}`);\n      }\n    } catch (error) {\n      console.error('File parsing error:', error);\n      alert('Error parsing file. Please check the file format.');\n    } finally {\n      setProcessing(false);\n    }\n  };\n\n  // Handle actual import after preview confirmation\n  const handleConfirmImport = async () => {\n    if (!importResult || !importResult.data) return;\n\n    // Show confirmation dialog\n    const totalProducts = importResult.data.length;\n    const confirmMessage = `Are you sure you want to import ${totalProducts} product${totalProducts > 1 ? 's' : ''} into your inventory?\\n\\nThis action cannot be undone.`;\n\n    if (!window.confirm(confirmMessage)) {\n      return;\n    }\n\n    setProcessing(true);\n    try {\n      let importedCount = 0;\n      let failedCount = 0;\n      const failedProducts: string[] = [];\n\n      for (const productData of importResult.data) {\n        try {\n          await createProduct(productData as Omit<Product, 'id' | 'createdAt' | 'updatedAt'>);\n          importedCount++;\n        } catch (error) {\n          console.error('Error creating product:', error);\n          failedCount++;\n          failedProducts.push(productData.name || 'Unknown product');\n        }\n      }\n\n      // Show detailed results\n      if (importedCount > 0) {\n        let message = `✅ Successfully imported ${importedCount} product${importedCount > 1 ? 's' : ''} into your inventory!`;\n\n        if (failedCount > 0) {\n          message += `\\n\\n⚠️ ${failedCount} product${failedCount > 1 ? 's' : ''} failed to import:`;\n          message += `\\n${failedProducts.slice(0, 5).join('\\n')}`;\n          if (failedProducts.length > 5) {\n            message += `\\n... and ${failedProducts.length - 5} more`;\n          }\n        }\n\n        alert(message);\n      } else {\n        alert('❌ No products were imported. Please check the data and try again.');\n      }\n\n      // Reset the form\n      setImportFile(null);\n      setImportResult(null);\n      setShowImportPreview(false);\n      onClearSelection();\n      onClose();\n    } catch (error) {\n      console.error('Import error:', error);\n      alert('❌ Error importing products. Please try again.');\n    } finally {\n      setProcessing(false);\n    }\n  };\n\n  // Handle template download\n  const handleDownloadTemplate = () => {\n    generateImportTemplate();\n  };\n\n  return (\n    <div className=\"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50\">\n      <div className=\"relative top-20 mx-auto p-5 border w-full max-w-2xl shadow-lg rounded-md bg-white\">\n        <div className=\"flex items-center justify-between mb-6\">\n          <h3 className=\"text-lg font-medium text-gray-900 flex items-center\">\n            <Package className=\"h-5 w-5 mr-2\" />\n            Bulk Operations ({selectedProducts.length} products selected)\n          </h3>\n          <button\n            onClick={onClose}\n            className=\"text-gray-400 hover:text-gray-600\"\n          >\n            <X className=\"h-5 w-5\" />\n          </button>\n        </div>\n\n        {!operation ? (\n          <div className=\"grid grid-cols-2 gap-4\">\n            <button\n              onClick={() => setOperation('update')}\n              className=\"p-4 border border-gray-300 rounded-lg hover:bg-gray-50 text-left\"\n            >\n              <Edit className=\"h-6 w-6 text-blue-600 mb-2\" />\n              <h4 className=\"font-medium text-gray-900\">Bulk Update</h4>\n              <p className=\"text-sm text-gray-500\">Update multiple products at once</p>\n            </button>\n\n            <button\n              onClick={() => setOperation('delete')}\n              className=\"p-4 border border-gray-300 rounded-lg hover:bg-gray-50 text-left\"\n            >\n              <Trash2 className=\"h-6 w-6 text-red-600 mb-2\" />\n              <h4 className=\"font-medium text-gray-900\">Bulk Delete</h4>\n              <p className=\"text-sm text-gray-500\">Delete selected products</p>\n            </button>\n\n            <button\n              onClick={() => setOperation('export')}\n              className=\"p-4 border border-gray-300 rounded-lg hover:bg-gray-50 text-left\"\n            >\n              <Download className=\"h-6 w-6 text-green-600 mb-2\" />\n              <h4 className=\"font-medium text-gray-900\">Export</h4>\n              <p className=\"text-sm text-gray-500\">Export to CSV file</p>\n            </button>\n\n            <button\n              onClick={() => setOperation('import')}\n              className=\"p-4 border border-gray-300 rounded-lg hover:bg-gray-50 text-left\"\n            >\n              <Upload className=\"h-6 w-6 text-purple-600 mb-2\" />\n              <h4 className=\"font-medium text-gray-900\">Import</h4>\n              <p className=\"text-sm text-gray-500\">Import from Excel or CSV file</p>\n            </button>\n          </div>\n        ) : operation === 'update' ? (\n          <div className=\"space-y-4\">\n            <h4 className=\"font-medium text-gray-900\">Bulk Update Products</h4>\n            \n            <div className=\"grid grid-cols-2 gap-4\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                  Category (leave empty to keep current)\n                </label>\n                <input\n                  type=\"text\"\n                  value={bulkUpdateData.category}\n                  onChange={(e) => setBulkUpdateData(prev => ({ ...prev, category: e.target.value }))}\n                  className=\"w-full border border-gray-300 rounded-md px-3 py-2\"\n                  placeholder=\"New category\"\n                />\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                  Price Adjustment\n                </label>\n                <div className=\"flex\">\n                  <select\n                    value={bulkUpdateData.adjustmentType}\n                    onChange={(e) => setBulkUpdateData(prev => ({ \n                      ...prev, \n                      adjustmentType: e.target.value as 'percentage' | 'fixed' \n                    }))}\n                    className=\"border border-gray-300 rounded-l-md px-3 py-2\"\n                  >\n                    <option value=\"percentage\">%</option>\n                    <option value=\"fixed\">KSh</option>\n                  </select>\n                  <input\n                    type=\"number\"\n                    value={bulkUpdateData.priceAdjustment}\n                    onChange={(e) => setBulkUpdateData(prev => ({ ...prev, priceAdjustment: e.target.value }))}\n                    className=\"flex-1 border border-gray-300 rounded-r-md px-3 py-2\"\n                    placeholder=\"0\"\n                  />\n                </div>\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                  Stock Adjustment\n                </label>\n                <input\n                  type=\"number\"\n                  value={bulkUpdateData.stockAdjustment}\n                  onChange={(e) => setBulkUpdateData(prev => ({ ...prev, stockAdjustment: e.target.value }))}\n                  className=\"w-full border border-gray-300 rounded-md px-3 py-2\"\n                  placeholder=\"+/- quantity\"\n                />\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                  Reorder Level\n                </label>\n                <input\n                  type=\"number\"\n                  value={bulkUpdateData.reorderLevel}\n                  onChange={(e) => setBulkUpdateData(prev => ({ ...prev, reorderLevel: e.target.value }))}\n                  className=\"w-full border border-gray-300 rounded-md px-3 py-2\"\n                  placeholder=\"New reorder level\"\n                />\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                  Active Status\n                </label>\n                <select\n                  value={bulkUpdateData.isActive}\n                  onChange={(e) => setBulkUpdateData(prev => ({ ...prev, isActive: e.target.value }))}\n                  className=\"w-full border border-gray-300 rounded-md px-3 py-2\"\n                >\n                  <option value=\"\">Keep current</option>\n                  <option value=\"true\">Active</option>\n                  <option value=\"false\">Inactive</option>\n                </select>\n              </div>\n            </div>\n\n            <div className=\"flex justify-end space-x-3 pt-4\">\n              <button\n                onClick={() => setOperation(null)}\n                className=\"px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50\"\n              >\n                Back\n              </button>\n              <button\n                onClick={handleBulkUpdate}\n                disabled={processing}\n                className=\"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50\"\n              >\n                {processing ? 'Updating...' : 'Update Products'}\n              </button>\n            </div>\n          </div>\n        ) : operation === 'delete' ? (\n          <div className=\"space-y-4\">\n            <div className=\"flex items-center text-red-600\">\n              <AlertTriangle className=\"h-5 w-5 mr-2\" />\n              <h4 className=\"font-medium\">Delete Selected Products</h4>\n            </div>\n            \n            <p className=\"text-gray-600\">\n              You are about to delete {selectedProducts.length} products. This action cannot be undone.\n            </p>\n\n            <div className=\"bg-red-50 border border-red-200 rounded-md p-3\">\n              <p className=\"text-sm text-red-800\">\n                Products to be deleted:\n              </p>\n              <ul className=\"mt-2 text-sm text-red-700 max-h-32 overflow-y-auto\">\n                {selectedProducts.slice(0, 10).map(product => (\n                  <li key={product.id}>• {product.name}</li>\n                ))}\n                {selectedProducts.length > 10 && (\n                  <li>• ... and {selectedProducts.length - 10} more</li>\n                )}\n              </ul>\n            </div>\n\n            <div className=\"flex justify-end space-x-3 pt-4\">\n              <button\n                onClick={() => setOperation(null)}\n                className=\"px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50\"\n              >\n                Back\n              </button>\n              <button\n                onClick={handleBulkDelete}\n                disabled={processing}\n                className=\"px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 disabled:opacity-50\"\n              >\n                {processing ? 'Deleting...' : 'Delete Products'}\n              </button>\n            </div>\n          </div>\n        ) : operation === 'export' ? (\n          <div className=\"space-y-4\">\n            <div className=\"flex items-center text-green-600\">\n              <Download className=\"h-5 w-5 mr-2\" />\n              <h4 className=\"font-medium\">Export Products</h4>\n            </div>\n            \n            <p className=\"text-gray-600\">\n              Export {selectedProducts.length} selected products to a CSV file.\n            </p>\n\n            <div className=\"flex justify-end space-x-3 pt-4\">\n              <button\n                onClick={() => setOperation(null)}\n                className=\"px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50\"\n              >\n                Back\n              </button>\n              <button\n                onClick={handleExport}\n                className=\"px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700\"\n              >\n                Export to CSV\n              </button>\n            </div>\n          </div>\n        ) : operation === 'import' ? (\n          <div className=\"space-y-4\">\n            <div className=\"flex items-center justify-between\">\n              <div className=\"flex items-center text-purple-600\">\n                <Upload className=\"h-5 w-5 mr-2\" />\n                <h4 className=\"font-medium\">Import Products</h4>\n              </div>\n              <button\n                onClick={handleDownloadTemplate}\n                className=\"flex items-center text-sm text-blue-600 hover:text-blue-800\"\n              >\n                <FileSpreadsheet className=\"h-4 w-4 mr-1\" />\n                Download Template\n              </button>\n            </div>\n\n            <div className=\"bg-blue-50 border border-blue-200 rounded-md p-3\">\n              <p className=\"text-sm text-blue-800\">\n                <strong>Supported formats:</strong> Excel (.xlsx, .xls) and CSV (.csv) files\n              </p>\n              <p className=\"text-sm text-blue-700 mt-1\">\n                <strong>Required columns:</strong> Name, Category, Price, Stock Quantity\n              </p>\n              <p className=\"text-sm text-blue-700\">\n                <strong>Optional columns:</strong> Description, Reorder Level, Has Expiry, Expiry Date, Is Active\n              </p>\n            </div>\n\n            {!showImportPreview ? (\n              <div className=\"space-y-4\">\n                <div>\n                  <label htmlFor=\"file-input\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    Select File\n                  </label>\n                  <input\n                    id=\"file-input\"\n                    type=\"file\"\n                    accept=\".csv,.xlsx,.xls\"\n                    onChange={(e) => handleFileSelect(e.target.files?.[0] || null)}\n                    className=\"w-full border border-gray-300 rounded-md px-3 py-2\"\n                  />\n                  {processing && (\n                    <p className=\"text-sm text-gray-600 mt-2\">Parsing file...</p>\n                  )}\n                </div>\n\n                {importFile && !processing && importResult && !importResult.success && (\n                  <div className=\"bg-red-50 border border-red-200 rounded-md p-3\">\n                    <div className=\"flex items-center text-red-800\">\n                      <AlertTriangle className=\"h-4 w-4 mr-2\" />\n                      <span className=\"font-medium\">File validation failed</span>\n                    </div>\n                    {importResult.errors && (\n                      <ul className=\"text-sm text-red-700 mt-1 list-disc list-inside\">\n                        {importResult.errors.map((error, index) => (\n                          <li key={index}>{error}</li>\n                        ))}\n                      </ul>\n                    )}\n                  </div>\n                )}\n              </div>\n            ) : (\n              <div className=\"space-y-4\">\n                <div className=\"bg-green-50 border border-green-200 rounded-md p-4\">\n                  <div className=\"flex items-center justify-between\">\n                    <div className=\"flex items-center text-green-800\">\n                      <CheckCircle className=\"h-5 w-5 mr-2\" />\n                      <span className=\"font-medium\">Import Preview Ready</span>\n                    </div>\n                    <span className=\"bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded-full\">\n                      {importResult?.data?.length || 0} products\n                    </span>\n                  </div>\n                  <p className=\"text-sm text-green-700 mt-2\">\n                    Review the products below and click \"Execute Import\" to add them to your inventory.\n                  </p>\n                  {importResult?.warnings && importResult.warnings.length > 0 && (\n                    <div className=\"mt-3 bg-yellow-50 border border-yellow-200 rounded-md p-2\">\n                      <p className=\"text-sm text-yellow-800 font-medium flex items-center\">\n                        <AlertTriangle className=\"h-4 w-4 mr-1\" />\n                        Warnings:\n                      </p>\n                      <ul className=\"text-sm text-yellow-700 mt-1 list-disc list-inside ml-5\">\n                        {importResult.warnings.map((warning, index) => (\n                          <li key={index}>{warning}</li>\n                        ))}\n                      </ul>\n                    </div>\n                  )}\n                </div>\n\n                <div className=\"bg-white border border-gray-200 rounded-lg overflow-hidden\">\n                  <div className=\"bg-gray-50 px-4 py-2 border-b border-gray-200\">\n                    <h5 className=\"text-sm font-medium text-gray-900\">Products to Import (Preview)</h5>\n                  </div>\n                  <div className=\"max-h-64 overflow-y-auto\">\n                    <table className=\"min-w-full text-sm\">\n                      <thead className=\"bg-gray-50 sticky top-0\">\n                        <tr>\n                          <th className=\"px-4 py-2 text-left font-medium text-gray-700\">#</th>\n                          <th className=\"px-4 py-2 text-left font-medium text-gray-700\">Name</th>\n                          <th className=\"px-4 py-2 text-left font-medium text-gray-700\">Category</th>\n                          <th className=\"px-4 py-2 text-left font-medium text-gray-700\">Price</th>\n                          <th className=\"px-4 py-2 text-left font-medium text-gray-700\">Stock</th>\n                        </tr>\n                      </thead>\n                      <tbody>\n                        {importResult?.data?.slice(0, 15).map((product, index) => (\n                          <tr key={index} className=\"border-t border-gray-200 hover:bg-gray-50\">\n                            <td className=\"px-4 py-2 text-gray-500\">{index + 1}</td>\n                            <td className=\"px-4 py-2 font-medium\">{product.name}</td>\n                            <td className=\"px-4 py-2\">{product.category}</td>\n                            <td className=\"px-4 py-2 text-green-600 font-medium\">KSh {product.price?.toLocaleString()}</td>\n                            <td className=\"px-4 py-2\">{product.stockQuantity}</td>\n                          </tr>\n                        ))}\n                        {(importResult?.data?.length || 0) > 15 && (\n                          <tr className=\"border-t border-gray-200 bg-gray-50\">\n                            <td colSpan={5} className=\"px-4 py-3 text-center text-gray-500 font-medium\">\n                              ... and {(importResult?.data?.length || 0) - 15} more products\n                            </td>\n                          </tr>\n                        )}\n                      </tbody>\n                    </table>\n                  </div>\n                </div>\n              </div>\n            )}\n\n            <div className=\"flex justify-between items-center pt-4\">\n              <button\n                onClick={() => {\n                  setOperation(null);\n                  setImportFile(null);\n                  setImportResult(null);\n                  setShowImportPreview(false);\n                }}\n                className=\"px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50\"\n              >\n                Back\n              </button>\n\n              <div className=\"flex space-x-3\">\n                {!showImportPreview && (\n                  <button\n                    onClick={handleDownloadTemplate}\n                    className=\"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center\"\n                  >\n                    <FileSpreadsheet className=\"h-4 w-4 mr-2\" />\n                    Download Template\n                  </button>\n                )}\n\n                {showImportPreview && (\n                  <>\n                    <button\n                      onClick={() => {\n                        setImportFile(null);\n                        setImportResult(null);\n                        setShowImportPreview(false);\n                      }}\n                      className=\"px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50\"\n                    >\n                      Choose Different File\n                    </button>\n                    <button\n                      onClick={handleConfirmImport}\n                      disabled={processing || !importResult?.data || importResult.data.length === 0}\n                      className=\"px-6 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center font-medium\"\n                    >\n                      <Upload className=\"h-4 w-4 mr-2\" />\n                      {processing ? 'Executing Import...' : `Execute Import (${importResult?.data?.length || 0} products)`}\n                    </button>\n                  </>\n                )}\n              </div>\n            </div>\n          </div>\n        ) : null}\n      </div>\n    </div>\n  );\n};\n\nexport default BulkOperations;\n"], "mappings": "qHAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,KAAQ,OAAO,CACvC,OACEC,MAAM,CACNC,QAAQ,CACRC,IAAI,CACJC,MAAM,CACNC,OAAO,CACPC,aAAa,CACbC,WAAW,CACXC,CAAC,CACDC,eAAe,KACV,cAAc,CAErB,OAASC,WAAW,KAAQ,yBAAyB,CACrD,OAASC,eAAe,CAAEC,sBAAsB,KAAsB,yBAAyB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAQhG,KAAM,CAAAC,cAA6C,CAAGC,IAAA,EAIhD,KAAAC,kBAAA,CAAAC,mBAAA,CAAAC,mBAAA,CAAAC,mBAAA,CAAAC,mBAAA,IAJiD,CACrDC,gBAAgB,CAChBC,OAAO,CACPC,gBACF,CAAC,CAAAR,IAAA,CACC,KAAM,CAAES,aAAa,CAAEC,aAAa,CAAEC,aAAc,CAAC,CAAGrB,WAAW,CAAC,CAAC,CACrE,KAAM,CAACsB,SAAS,CAAEC,YAAY,CAAC,CAAGjC,QAAQ,CAAmD,IAAI,CAAC,CAClG,KAAM,CAACkC,cAAc,CAAEC,iBAAiB,CAAC,CAAGnC,QAAQ,CAAC,CACnDoC,QAAQ,CAAE,EAAE,CACZC,eAAe,CAAE,EAAE,CACnBC,cAAc,CAAE,YAAsC,CACtDC,eAAe,CAAE,EAAE,CACnBC,YAAY,CAAE,EAAE,CAChBC,QAAQ,CAAE,EACZ,CAAC,CAAC,CACF,KAAM,CAACC,UAAU,CAAEC,aAAa,CAAC,CAAG3C,QAAQ,CAAC,KAAK,CAAC,CACnD,KAAM,CAAC4C,UAAU,CAAEC,aAAa,CAAC,CAAG7C,QAAQ,CAAc,IAAI,CAAC,CAC/D,KAAM,CAAC8C,YAAY,CAAEC,eAAe,CAAC,CAAG/C,QAAQ,CAAsB,IAAI,CAAC,CAC3E,KAAM,CAACgD,iBAAiB,CAAEC,oBAAoB,CAAC,CAAGjD,QAAQ,CAAC,KAAK,CAAC,CAEjE;AACA,KAAM,CAAAkD,gBAAgB,CAAG,KAAAA,CAAA,GAAY,CACnC,GAAIxB,gBAAgB,CAACyB,MAAM,GAAK,CAAC,CAAE,OAEnCR,aAAa,CAAC,IAAI,CAAC,CACnB,GAAI,CACF,IAAK,KAAM,CAAAS,OAAO,GAAI,CAAA1B,gBAAgB,CAAE,CACtC,KAAM,CAAA2B,OAAyB,CAAG,CAAC,CAAC,CAEpC;AACA,GAAInB,cAAc,CAACE,QAAQ,CAAE,CAC3BiB,OAAO,CAACjB,QAAQ,CAAGF,cAAc,CAACE,QAAQ,CAC5C,CAEA;AACA,GAAIF,cAAc,CAACG,eAAe,CAAE,CAClC,KAAM,CAAAiB,UAAU,CAAGC,UAAU,CAACrB,cAAc,CAACG,eAAe,CAAC,CAC7D,GAAIH,cAAc,CAACI,cAAc,GAAK,YAAY,CAAE,CAClDe,OAAO,CAACG,KAAK,CAAGJ,OAAO,CAACI,KAAK,EAAI,CAAC,CAAGF,UAAU,CAAG,GAAG,CAAC,CACxD,CAAC,IAAM,CACLD,OAAO,CAACG,KAAK,CAAGJ,OAAO,CAACI,KAAK,CAAGF,UAAU,CAC5C,CACF,CAEA;AACA,GAAIpB,cAAc,CAACK,eAAe,CAAE,CAClC,KAAM,CAAAe,UAAU,CAAGG,QAAQ,CAACvB,cAAc,CAACK,eAAe,CAAC,CAC3Dc,OAAO,CAACK,aAAa,CAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,CAAER,OAAO,CAACM,aAAa,CAAGJ,UAAU,CAAC,CACzE,CAEA;AACA,GAAIpB,cAAc,CAACM,YAAY,CAAE,CAC/Ba,OAAO,CAACb,YAAY,CAAGiB,QAAQ,CAACvB,cAAc,CAACM,YAAY,CAAC,CAC9D,CAEA;AACA,GAAIN,cAAc,CAACO,QAAQ,GAAK,EAAE,CAAE,CAClCY,OAAO,CAACZ,QAAQ,CAAGP,cAAc,CAACO,QAAQ,GAAK,MAAM,CACvD,CAEA,GAAIoB,MAAM,CAACC,IAAI,CAACT,OAAO,CAAC,CAACF,MAAM,CAAG,CAAC,CAAE,CACnC,KAAM,CAAAtB,aAAa,CAACuB,OAAO,CAACW,EAAE,CAAEV,OAAO,CAAC,CAC1C,CACF,CAEAW,KAAK,yBAAAC,MAAA,CAAyBvC,gBAAgB,CAACyB,MAAM,aAAW,CAAC,CACjEvB,gBAAgB,CAAC,CAAC,CAClBD,OAAO,CAAC,CAAC,CACX,CAAE,MAAOuC,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,oBAAoB,CAAEA,KAAK,CAAC,CAC1CF,KAAK,CAAC,4CAA4C,CAAC,CACrD,CAAC,OAAS,CACRrB,aAAa,CAAC,KAAK,CAAC,CACtB,CACF,CAAC,CAED;AACA,KAAM,CAAAyB,gBAAgB,CAAG,KAAAA,CAAA,GAAY,CACnC,GAAI1C,gBAAgB,CAACyB,MAAM,GAAK,CAAC,CAAE,OAEnC,KAAM,CAAAkB,SAAS,CAAGC,MAAM,CAACC,OAAO,oCAAAN,MAAA,CACKvC,gBAAgB,CAACyB,MAAM,4CAC5D,CAAC,CAED,GAAI,CAACkB,SAAS,CAAE,OAEhB1B,aAAa,CAAC,IAAI,CAAC,CACnB,GAAI,CACF,IAAK,KAAM,CAAAS,OAAO,GAAI,CAAA1B,gBAAgB,CAAE,CACtC,KAAM,CAAAI,aAAa,CAACsB,OAAO,CAACW,EAAE,CAAC,CACjC,CAEAC,KAAK,yBAAAC,MAAA,CAAyBvC,gBAAgB,CAACyB,MAAM,aAAW,CAAC,CACjEvB,gBAAgB,CAAC,CAAC,CAClBD,OAAO,CAAC,CAAC,CACX,CAAE,MAAOuC,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,oBAAoB,CAAEA,KAAK,CAAC,CAC1CF,KAAK,CAAC,4CAA4C,CAAC,CACrD,CAAC,OAAS,CACRrB,aAAa,CAAC,KAAK,CAAC,CACtB,CACF,CAAC,CAED;AACA,KAAM,CAAA6B,YAAY,CAAGA,CAAA,GAAM,CACzB,KAAM,CAAAC,UAAU,CAAG,CACjB,IAAI,CACJ,MAAM,CACN,aAAa,CACb,UAAU,CACV,OAAO,CACP,gBAAgB,CAChB,eAAe,CACf,YAAY,CACZ,aAAa,CACb,WAAW,CACX,YAAY,CACb,CAED,KAAM,CAAAC,OAAO,CAAGhD,gBAAgB,CAACiD,GAAG,CAACvB,OAAO,OAAAwB,mBAAA,OAAI,CAC9CxB,OAAO,CAACW,EAAE,CACVX,OAAO,CAACyB,IAAI,CACZzB,OAAO,CAAC0B,WAAW,CACnB1B,OAAO,CAAChB,QAAQ,CAChBgB,OAAO,CAACI,KAAK,CACbJ,OAAO,CAACM,aAAa,CACrBN,OAAO,CAACZ,YAAY,CACpBY,OAAO,CAAC2B,SAAS,CACjB,EAAAH,mBAAA,CAAAxB,OAAO,CAAC4B,UAAU,UAAAJ,mBAAA,iBAAlBA,mBAAA,CAAoBK,WAAW,CAAC,CAAC,GAAI,EAAE,CACvC7B,OAAO,CAACX,QAAQ,CAChBW,OAAO,CAAC8B,SAAS,CAACD,WAAW,CAAC,CAAC,CAChC,GAAC,CAEF,KAAM,CAAAE,UAAU,CAAG,CAACV,UAAU,CAAE,GAAGC,OAAO,CAAC,CACxCC,GAAG,CAACS,GAAG,EAAIA,GAAG,CAACT,GAAG,CAACU,KAAK,OAAApB,MAAA,CAAQoB,KAAK,MAAG,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC,CAAC,CACpDA,IAAI,CAAC,IAAI,CAAC,CAEb,KAAM,CAAAC,IAAI,CAAG,GAAI,CAAAC,IAAI,CAAC,CAACL,UAAU,CAAC,CAAE,CAAEM,IAAI,CAAE,UAAW,CAAC,CAAC,CACzD,KAAM,CAAAC,GAAG,CAAGpB,MAAM,CAACqB,GAAG,CAACC,eAAe,CAACL,IAAI,CAAC,CAC5C,KAAM,CAAAM,IAAI,CAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC,CACxCF,IAAI,CAACG,IAAI,CAAGN,GAAG,CACfG,IAAI,CAACI,QAAQ,oBAAAhC,MAAA,CAAsB,GAAI,CAAAiC,IAAI,CAAC,CAAC,CAACjB,WAAW,CAAC,CAAC,CAACkB,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,QAAM,CAC/EN,IAAI,CAACO,KAAK,CAAC,CAAC,CACZ9B,MAAM,CAACqB,GAAG,CAACU,eAAe,CAACX,GAAG,CAAC,CAE/B1B,KAAK,aAAAC,MAAA,CAAavC,gBAAgB,CAACyB,MAAM,oBAAkB,CAAC,CAC9D,CAAC,CAED;AACA,KAAM,CAAAmD,gBAAgB,CAAG,KAAO,CAAAC,IAAiB,EAAK,CACpD1D,aAAa,CAAC0D,IAAI,CAAC,CACnBxD,eAAe,CAAC,IAAI,CAAC,CACrBE,oBAAoB,CAAC,KAAK,CAAC,CAE3B,GAAI,CAACsD,IAAI,CAAE,OAEX5D,aAAa,CAAC,IAAI,CAAC,CACnB,GAAI,CACF,KAAM,CAAA6D,MAAM,CAAG,KAAM,CAAA7F,eAAe,CAAC4F,IAAI,CAAC,CAC1CxD,eAAe,CAACyD,MAAM,CAAC,CAEvB,GAAIA,MAAM,CAACC,OAAO,EAAID,MAAM,CAACE,IAAI,EAAIF,MAAM,CAACE,IAAI,CAACvD,MAAM,CAAG,CAAC,CAAE,CAC3DF,oBAAoB,CAAC,IAAI,CAAC,CAC5B,CAAC,IAAM,IAAIuD,MAAM,CAACG,MAAM,CAAE,CACxB3C,KAAK,+BAAAC,MAAA,CAA+BuC,MAAM,CAACG,MAAM,CAACrB,IAAI,CAAC,IAAI,CAAC,CAAE,CAAC,CACjE,CACF,CAAE,MAAOpB,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,qBAAqB,CAAEA,KAAK,CAAC,CAC3CF,KAAK,CAAC,mDAAmD,CAAC,CAC5D,CAAC,OAAS,CACRrB,aAAa,CAAC,KAAK,CAAC,CACtB,CACF,CAAC,CAED;AACA,KAAM,CAAAiE,mBAAmB,CAAG,KAAAA,CAAA,GAAY,CACtC,GAAI,CAAC9D,YAAY,EAAI,CAACA,YAAY,CAAC4D,IAAI,CAAE,OAEzC;AACA,KAAM,CAAAG,aAAa,CAAG/D,YAAY,CAAC4D,IAAI,CAACvD,MAAM,CAC9C,KAAM,CAAA2D,cAAc,oCAAA7C,MAAA,CAAsC4C,aAAa,aAAA5C,MAAA,CAAW4C,aAAa,CAAG,CAAC,CAAG,GAAG,CAAG,EAAE,0DAAwD,CAEtK,GAAI,CAACvC,MAAM,CAACC,OAAO,CAACuC,cAAc,CAAC,CAAE,CACnC,OACF,CAEAnE,aAAa,CAAC,IAAI,CAAC,CACnB,GAAI,CACF,GAAI,CAAAoE,aAAa,CAAG,CAAC,CACrB,GAAI,CAAAC,WAAW,CAAG,CAAC,CACnB,KAAM,CAAAC,cAAwB,CAAG,EAAE,CAEnC,IAAK,KAAM,CAAAC,WAAW,GAAI,CAAApE,YAAY,CAAC4D,IAAI,CAAE,CAC3C,GAAI,CACF,KAAM,CAAA3E,aAAa,CAACmF,WAA8D,CAAC,CACnFH,aAAa,EAAE,CACjB,CAAE,MAAO7C,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,CAAEA,KAAK,CAAC,CAC/C8C,WAAW,EAAE,CACbC,cAAc,CAACE,IAAI,CAACD,WAAW,CAACrC,IAAI,EAAI,iBAAiB,CAAC,CAC5D,CACF,CAEA;AACA,GAAIkC,aAAa,CAAG,CAAC,CAAE,CACrB,GAAI,CAAAK,OAAO,iCAAAnD,MAAA,CAA8B8C,aAAa,aAAA9C,MAAA,CAAW8C,aAAa,CAAG,CAAC,CAAG,GAAG,CAAG,EAAE,yBAAuB,CAEpH,GAAIC,WAAW,CAAG,CAAC,CAAE,CACnBI,OAAO,sBAAAnD,MAAA,CAAc+C,WAAW,aAAA/C,MAAA,CAAW+C,WAAW,CAAG,CAAC,CAAG,GAAG,CAAG,EAAE,sBAAoB,CACzFI,OAAO,OAAAnD,MAAA,CAASgD,cAAc,CAACI,KAAK,CAAC,CAAC,CAAE,CAAC,CAAC,CAAC/B,IAAI,CAAC,IAAI,CAAC,CAAE,CACvD,GAAI2B,cAAc,CAAC9D,MAAM,CAAG,CAAC,CAAE,CAC7BiE,OAAO,eAAAnD,MAAA,CAAiBgD,cAAc,CAAC9D,MAAM,CAAG,CAAC,SAAO,CAC1D,CACF,CAEAa,KAAK,CAACoD,OAAO,CAAC,CAChB,CAAC,IAAM,CACLpD,KAAK,CAAC,mEAAmE,CAAC,CAC5E,CAEA;AACAnB,aAAa,CAAC,IAAI,CAAC,CACnBE,eAAe,CAAC,IAAI,CAAC,CACrBE,oBAAoB,CAAC,KAAK,CAAC,CAC3BrB,gBAAgB,CAAC,CAAC,CAClBD,OAAO,CAAC,CAAC,CACX,CAAE,MAAOuC,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,eAAe,CAAEA,KAAK,CAAC,CACrCF,KAAK,CAAC,+CAA+C,CAAC,CACxD,CAAC,OAAS,CACRrB,aAAa,CAAC,KAAK,CAAC,CACtB,CACF,CAAC,CAED;AACA,KAAM,CAAA2E,sBAAsB,CAAGA,CAAA,GAAM,CACnC1G,sBAAsB,CAAC,CAAC,CAC1B,CAAC,CAED,mBACEE,IAAA,QAAKyG,SAAS,CAAC,4EAA4E,CAAAC,QAAA,cACzFxG,KAAA,QAAKuG,SAAS,CAAC,mFAAmF,CAAAC,QAAA,eAChGxG,KAAA,QAAKuG,SAAS,CAAC,wCAAwC,CAAAC,QAAA,eACrDxG,KAAA,OAAIuG,SAAS,CAAC,qDAAqD,CAAAC,QAAA,eACjE1G,IAAA,CAACT,OAAO,EAACkH,SAAS,CAAC,cAAc,CAAE,CAAC,oBACnB,CAAC7F,gBAAgB,CAACyB,MAAM,CAAC,qBAC5C,EAAI,CAAC,cACLrC,IAAA,WACE2G,OAAO,CAAE9F,OAAQ,CACjB4F,SAAS,CAAC,mCAAmC,CAAAC,QAAA,cAE7C1G,IAAA,CAACN,CAAC,EAAC+G,SAAS,CAAC,SAAS,CAAE,CAAC,CACnB,CAAC,EACN,CAAC,CAEL,CAACvF,SAAS,cACThB,KAAA,QAAKuG,SAAS,CAAC,wBAAwB,CAAAC,QAAA,eACrCxG,KAAA,WACEyG,OAAO,CAAEA,CAAA,GAAMxF,YAAY,CAAC,QAAQ,CAAE,CACtCsF,SAAS,CAAC,kEAAkE,CAAAC,QAAA,eAE5E1G,IAAA,CAACX,IAAI,EAACoH,SAAS,CAAC,4BAA4B,CAAE,CAAC,cAC/CzG,IAAA,OAAIyG,SAAS,CAAC,2BAA2B,CAAAC,QAAA,CAAC,aAAW,CAAI,CAAC,cAC1D1G,IAAA,MAAGyG,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,kCAAgC,CAAG,CAAC,EACnE,CAAC,cAETxG,KAAA,WACEyG,OAAO,CAAEA,CAAA,GAAMxF,YAAY,CAAC,QAAQ,CAAE,CACtCsF,SAAS,CAAC,kEAAkE,CAAAC,QAAA,eAE5E1G,IAAA,CAACV,MAAM,EAACmH,SAAS,CAAC,2BAA2B,CAAE,CAAC,cAChDzG,IAAA,OAAIyG,SAAS,CAAC,2BAA2B,CAAAC,QAAA,CAAC,aAAW,CAAI,CAAC,cAC1D1G,IAAA,MAAGyG,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,0BAAwB,CAAG,CAAC,EAC3D,CAAC,cAETxG,KAAA,WACEyG,OAAO,CAAEA,CAAA,GAAMxF,YAAY,CAAC,QAAQ,CAAE,CACtCsF,SAAS,CAAC,kEAAkE,CAAAC,QAAA,eAE5E1G,IAAA,CAACZ,QAAQ,EAACqH,SAAS,CAAC,6BAA6B,CAAE,CAAC,cACpDzG,IAAA,OAAIyG,SAAS,CAAC,2BAA2B,CAAAC,QAAA,CAAC,QAAM,CAAI,CAAC,cACrD1G,IAAA,MAAGyG,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,oBAAkB,CAAG,CAAC,EACrD,CAAC,cAETxG,KAAA,WACEyG,OAAO,CAAEA,CAAA,GAAMxF,YAAY,CAAC,QAAQ,CAAE,CACtCsF,SAAS,CAAC,kEAAkE,CAAAC,QAAA,eAE5E1G,IAAA,CAACb,MAAM,EAACsH,SAAS,CAAC,8BAA8B,CAAE,CAAC,cACnDzG,IAAA,OAAIyG,SAAS,CAAC,2BAA2B,CAAAC,QAAA,CAAC,QAAM,CAAI,CAAC,cACrD1G,IAAA,MAAGyG,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,+BAA6B,CAAG,CAAC,EAChE,CAAC,EACN,CAAC,CACJxF,SAAS,GAAK,QAAQ,cACxBhB,KAAA,QAAKuG,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxB1G,IAAA,OAAIyG,SAAS,CAAC,2BAA2B,CAAAC,QAAA,CAAC,sBAAoB,CAAI,CAAC,cAEnExG,KAAA,QAAKuG,SAAS,CAAC,wBAAwB,CAAAC,QAAA,eACrCxG,KAAA,QAAAwG,QAAA,eACE1G,IAAA,UAAOyG,SAAS,CAAC,8CAA8C,CAAAC,QAAA,CAAC,wCAEhE,CAAO,CAAC,cACR1G,IAAA,UACE2E,IAAI,CAAC,MAAM,CACXiC,KAAK,CAAExF,cAAc,CAACE,QAAS,CAC/BuF,QAAQ,CAAGC,CAAC,EAAKzF,iBAAiB,CAAC0F,IAAI,EAAAC,aAAA,CAAAA,aAAA,IAAUD,IAAI,MAAEzF,QAAQ,CAAEwF,CAAC,CAACG,MAAM,CAACL,KAAK,EAAG,CAAE,CACpFH,SAAS,CAAC,oDAAoD,CAC9DS,WAAW,CAAC,cAAc,CAC3B,CAAC,EACC,CAAC,cAENhH,KAAA,QAAAwG,QAAA,eACE1G,IAAA,UAAOyG,SAAS,CAAC,8CAA8C,CAAAC,QAAA,CAAC,kBAEhE,CAAO,CAAC,cACRxG,KAAA,QAAKuG,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnBxG,KAAA,WACE0G,KAAK,CAAExF,cAAc,CAACI,cAAe,CACrCqF,QAAQ,CAAGC,CAAC,EAAKzF,iBAAiB,CAAC0F,IAAI,EAAAC,aAAA,CAAAA,aAAA,IAClCD,IAAI,MACPvF,cAAc,CAAEsF,CAAC,CAACG,MAAM,CAACL,KAA+B,EACxD,CAAE,CACJH,SAAS,CAAC,+CAA+C,CAAAC,QAAA,eAEzD1G,IAAA,WAAQ4G,KAAK,CAAC,YAAY,CAAAF,QAAA,CAAC,GAAC,CAAQ,CAAC,cACrC1G,IAAA,WAAQ4G,KAAK,CAAC,OAAO,CAAAF,QAAA,CAAC,KAAG,CAAQ,CAAC,EAC5B,CAAC,cACT1G,IAAA,UACE2E,IAAI,CAAC,QAAQ,CACbiC,KAAK,CAAExF,cAAc,CAACG,eAAgB,CACtCsF,QAAQ,CAAGC,CAAC,EAAKzF,iBAAiB,CAAC0F,IAAI,EAAAC,aAAA,CAAAA,aAAA,IAAUD,IAAI,MAAExF,eAAe,CAAEuF,CAAC,CAACG,MAAM,CAACL,KAAK,EAAG,CAAE,CAC3FH,SAAS,CAAC,sDAAsD,CAChES,WAAW,CAAC,GAAG,CAChB,CAAC,EACC,CAAC,EACH,CAAC,cAENhH,KAAA,QAAAwG,QAAA,eACE1G,IAAA,UAAOyG,SAAS,CAAC,8CAA8C,CAAAC,QAAA,CAAC,kBAEhE,CAAO,CAAC,cACR1G,IAAA,UACE2E,IAAI,CAAC,QAAQ,CACbiC,KAAK,CAAExF,cAAc,CAACK,eAAgB,CACtCoF,QAAQ,CAAGC,CAAC,EAAKzF,iBAAiB,CAAC0F,IAAI,EAAAC,aAAA,CAAAA,aAAA,IAAUD,IAAI,MAAEtF,eAAe,CAAEqF,CAAC,CAACG,MAAM,CAACL,KAAK,EAAG,CAAE,CAC3FH,SAAS,CAAC,oDAAoD,CAC9DS,WAAW,CAAC,cAAc,CAC3B,CAAC,EACC,CAAC,cAENhH,KAAA,QAAAwG,QAAA,eACE1G,IAAA,UAAOyG,SAAS,CAAC,8CAA8C,CAAAC,QAAA,CAAC,eAEhE,CAAO,CAAC,cACR1G,IAAA,UACE2E,IAAI,CAAC,QAAQ,CACbiC,KAAK,CAAExF,cAAc,CAACM,YAAa,CACnCmF,QAAQ,CAAGC,CAAC,EAAKzF,iBAAiB,CAAC0F,IAAI,EAAAC,aAAA,CAAAA,aAAA,IAAUD,IAAI,MAAErF,YAAY,CAAEoF,CAAC,CAACG,MAAM,CAACL,KAAK,EAAG,CAAE,CACxFH,SAAS,CAAC,oDAAoD,CAC9DS,WAAW,CAAC,mBAAmB,CAChC,CAAC,EACC,CAAC,cAENhH,KAAA,QAAAwG,QAAA,eACE1G,IAAA,UAAOyG,SAAS,CAAC,8CAA8C,CAAAC,QAAA,CAAC,eAEhE,CAAO,CAAC,cACRxG,KAAA,WACE0G,KAAK,CAAExF,cAAc,CAACO,QAAS,CAC/BkF,QAAQ,CAAGC,CAAC,EAAKzF,iBAAiB,CAAC0F,IAAI,EAAAC,aAAA,CAAAA,aAAA,IAAUD,IAAI,MAAEpF,QAAQ,CAAEmF,CAAC,CAACG,MAAM,CAACL,KAAK,EAAG,CAAE,CACpFH,SAAS,CAAC,oDAAoD,CAAAC,QAAA,eAE9D1G,IAAA,WAAQ4G,KAAK,CAAC,EAAE,CAAAF,QAAA,CAAC,cAAY,CAAQ,CAAC,cACtC1G,IAAA,WAAQ4G,KAAK,CAAC,MAAM,CAAAF,QAAA,CAAC,QAAM,CAAQ,CAAC,cACpC1G,IAAA,WAAQ4G,KAAK,CAAC,OAAO,CAAAF,QAAA,CAAC,UAAQ,CAAQ,CAAC,EACjC,CAAC,EACN,CAAC,EACH,CAAC,cAENxG,KAAA,QAAKuG,SAAS,CAAC,iCAAiC,CAAAC,QAAA,eAC9C1G,IAAA,WACE2G,OAAO,CAAEA,CAAA,GAAMxF,YAAY,CAAC,IAAI,CAAE,CAClCsF,SAAS,CAAC,4EAA4E,CAAAC,QAAA,CACvF,MAED,CAAQ,CAAC,cACT1G,IAAA,WACE2G,OAAO,CAAEvE,gBAAiB,CAC1B+E,QAAQ,CAAEvF,UAAW,CACrB6E,SAAS,CAAC,mFAAmF,CAAAC,QAAA,CAE5F9E,UAAU,CAAG,aAAa,CAAG,iBAAiB,CACzC,CAAC,EACN,CAAC,EACH,CAAC,CACJV,SAAS,GAAK,QAAQ,cACxBhB,KAAA,QAAKuG,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxBxG,KAAA,QAAKuG,SAAS,CAAC,gCAAgC,CAAAC,QAAA,eAC7C1G,IAAA,CAACR,aAAa,EAACiH,SAAS,CAAC,cAAc,CAAE,CAAC,cAC1CzG,IAAA,OAAIyG,SAAS,CAAC,aAAa,CAAAC,QAAA,CAAC,0BAAwB,CAAI,CAAC,EACtD,CAAC,cAENxG,KAAA,MAAGuG,SAAS,CAAC,eAAe,CAAAC,QAAA,EAAC,0BACH,CAAC9F,gBAAgB,CAACyB,MAAM,CAAC,0CACnD,EAAG,CAAC,cAEJnC,KAAA,QAAKuG,SAAS,CAAC,gDAAgD,CAAAC,QAAA,eAC7D1G,IAAA,MAAGyG,SAAS,CAAC,sBAAsB,CAAAC,QAAA,CAAC,yBAEpC,CAAG,CAAC,cACJxG,KAAA,OAAIuG,SAAS,CAAC,oDAAoD,CAAAC,QAAA,EAC/D9F,gBAAgB,CAAC2F,KAAK,CAAC,CAAC,CAAE,EAAE,CAAC,CAAC1C,GAAG,CAACvB,OAAO,eACxCpC,KAAA,OAAAwG,QAAA,EAAqB,SAAE,CAACpE,OAAO,CAACyB,IAAI,GAA3BzB,OAAO,CAACW,EAAwB,CAC1C,CAAC,CACDrC,gBAAgB,CAACyB,MAAM,CAAG,EAAE,eAC3BnC,KAAA,OAAAwG,QAAA,EAAI,iBAAU,CAAC9F,gBAAgB,CAACyB,MAAM,CAAG,EAAE,CAAC,OAAK,EAAI,CACtD,EACC,CAAC,EACF,CAAC,cAENnC,KAAA,QAAKuG,SAAS,CAAC,iCAAiC,CAAAC,QAAA,eAC9C1G,IAAA,WACE2G,OAAO,CAAEA,CAAA,GAAMxF,YAAY,CAAC,IAAI,CAAE,CAClCsF,SAAS,CAAC,4EAA4E,CAAAC,QAAA,CACvF,MAED,CAAQ,CAAC,cACT1G,IAAA,WACE2G,OAAO,CAAErD,gBAAiB,CAC1B6D,QAAQ,CAAEvF,UAAW,CACrB6E,SAAS,CAAC,iFAAiF,CAAAC,QAAA,CAE1F9E,UAAU,CAAG,aAAa,CAAG,iBAAiB,CACzC,CAAC,EACN,CAAC,EACH,CAAC,CACJV,SAAS,GAAK,QAAQ,cACxBhB,KAAA,QAAKuG,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxBxG,KAAA,QAAKuG,SAAS,CAAC,kCAAkC,CAAAC,QAAA,eAC/C1G,IAAA,CAACZ,QAAQ,EAACqH,SAAS,CAAC,cAAc,CAAE,CAAC,cACrCzG,IAAA,OAAIyG,SAAS,CAAC,aAAa,CAAAC,QAAA,CAAC,iBAAe,CAAI,CAAC,EAC7C,CAAC,cAENxG,KAAA,MAAGuG,SAAS,CAAC,eAAe,CAAAC,QAAA,EAAC,SACpB,CAAC9F,gBAAgB,CAACyB,MAAM,CAAC,mCAClC,EAAG,CAAC,cAEJnC,KAAA,QAAKuG,SAAS,CAAC,iCAAiC,CAAAC,QAAA,eAC9C1G,IAAA,WACE2G,OAAO,CAAEA,CAAA,GAAMxF,YAAY,CAAC,IAAI,CAAE,CAClCsF,SAAS,CAAC,4EAA4E,CAAAC,QAAA,CACvF,MAED,CAAQ,CAAC,cACT1G,IAAA,WACE2G,OAAO,CAAEjD,YAAa,CACtB+C,SAAS,CAAC,iEAAiE,CAAAC,QAAA,CAC5E,eAED,CAAQ,CAAC,EACN,CAAC,EACH,CAAC,CACJxF,SAAS,GAAK,QAAQ,cACxBhB,KAAA,QAAKuG,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxBxG,KAAA,QAAKuG,SAAS,CAAC,mCAAmC,CAAAC,QAAA,eAChDxG,KAAA,QAAKuG,SAAS,CAAC,mCAAmC,CAAAC,QAAA,eAChD1G,IAAA,CAACb,MAAM,EAACsH,SAAS,CAAC,cAAc,CAAE,CAAC,cACnCzG,IAAA,OAAIyG,SAAS,CAAC,aAAa,CAAAC,QAAA,CAAC,iBAAe,CAAI,CAAC,EAC7C,CAAC,cACNxG,KAAA,WACEyG,OAAO,CAAEH,sBAAuB,CAChCC,SAAS,CAAC,6DAA6D,CAAAC,QAAA,eAEvE1G,IAAA,CAACL,eAAe,EAAC8G,SAAS,CAAC,cAAc,CAAE,CAAC,oBAE9C,EAAQ,CAAC,EACN,CAAC,cAENvG,KAAA,QAAKuG,SAAS,CAAC,kDAAkD,CAAAC,QAAA,eAC/DxG,KAAA,MAAGuG,SAAS,CAAC,uBAAuB,CAAAC,QAAA,eAClC1G,IAAA,WAAA0G,QAAA,CAAQ,oBAAkB,CAAQ,CAAC,4CACrC,EAAG,CAAC,cACJxG,KAAA,MAAGuG,SAAS,CAAC,4BAA4B,CAAAC,QAAA,eACvC1G,IAAA,WAAA0G,QAAA,CAAQ,mBAAiB,CAAQ,CAAC,yCACpC,EAAG,CAAC,cACJxG,KAAA,MAAGuG,SAAS,CAAC,uBAAuB,CAAAC,QAAA,eAClC1G,IAAA,WAAA0G,QAAA,CAAQ,mBAAiB,CAAQ,CAAC,kEACpC,EAAG,CAAC,EACD,CAAC,CAEL,CAACxE,iBAAiB,cACjBhC,KAAA,QAAKuG,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxBxG,KAAA,QAAAwG,QAAA,eACE1G,IAAA,UAAOoH,OAAO,CAAC,YAAY,CAACX,SAAS,CAAC,8CAA8C,CAAAC,QAAA,CAAC,aAErF,CAAO,CAAC,cACR1G,IAAA,UACEiD,EAAE,CAAC,YAAY,CACf0B,IAAI,CAAC,MAAM,CACX0C,MAAM,CAAC,iBAAiB,CACxBR,QAAQ,CAAGC,CAAC,OAAAQ,eAAA,OAAK,CAAA9B,gBAAgB,CAAC,EAAA8B,eAAA,CAAAR,CAAC,CAACG,MAAM,CAACM,KAAK,UAAAD,eAAA,iBAAdA,eAAA,CAAiB,CAAC,CAAC,GAAI,IAAI,CAAC,EAAC,CAC/Db,SAAS,CAAC,oDAAoD,CAC/D,CAAC,CACD7E,UAAU,eACT5B,IAAA,MAAGyG,SAAS,CAAC,4BAA4B,CAAAC,QAAA,CAAC,iBAAe,CAAG,CAC7D,EACE,CAAC,CAEL5E,UAAU,EAAI,CAACF,UAAU,EAAII,YAAY,EAAI,CAACA,YAAY,CAAC2D,OAAO,eACjEzF,KAAA,QAAKuG,SAAS,CAAC,gDAAgD,CAAAC,QAAA,eAC7DxG,KAAA,QAAKuG,SAAS,CAAC,gCAAgC,CAAAC,QAAA,eAC7C1G,IAAA,CAACR,aAAa,EAACiH,SAAS,CAAC,cAAc,CAAE,CAAC,cAC1CzG,IAAA,SAAMyG,SAAS,CAAC,aAAa,CAAAC,QAAA,CAAC,wBAAsB,CAAM,CAAC,EACxD,CAAC,CACL1E,YAAY,CAAC6D,MAAM,eAClB7F,IAAA,OAAIyG,SAAS,CAAC,iDAAiD,CAAAC,QAAA,CAC5D1E,YAAY,CAAC6D,MAAM,CAAChC,GAAG,CAAC,CAACT,KAAK,CAAEoE,KAAK,gBACpCxH,IAAA,OAAA0G,QAAA,CAAiBtD,KAAK,EAAboE,KAAkB,CAC5B,CAAC,CACA,CACL,EACE,CACN,EACE,CAAC,cAENtH,KAAA,QAAKuG,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxBxG,KAAA,QAAKuG,SAAS,CAAC,oDAAoD,CAAAC,QAAA,eACjExG,KAAA,QAAKuG,SAAS,CAAC,mCAAmC,CAAAC,QAAA,eAChDxG,KAAA,QAAKuG,SAAS,CAAC,kCAAkC,CAAAC,QAAA,eAC/C1G,IAAA,CAACP,WAAW,EAACgH,SAAS,CAAC,cAAc,CAAE,CAAC,cACxCzG,IAAA,SAAMyG,SAAS,CAAC,aAAa,CAAAC,QAAA,CAAC,sBAAoB,CAAM,CAAC,EACtD,CAAC,cACNxG,KAAA,SAAMuG,SAAS,CAAC,4EAA4E,CAAAC,QAAA,EACzF,CAAA1E,YAAY,SAAZA,YAAY,kBAAAzB,kBAAA,CAAZyB,YAAY,CAAE4D,IAAI,UAAArF,kBAAA,iBAAlBA,kBAAA,CAAoB8B,MAAM,GAAI,CAAC,CAAC,WACnC,EAAM,CAAC,EACJ,CAAC,cACNrC,IAAA,MAAGyG,SAAS,CAAC,6BAA6B,CAAAC,QAAA,CAAC,uFAE3C,CAAG,CAAC,CACH,CAAA1E,YAAY,SAAZA,YAAY,iBAAZA,YAAY,CAAEyF,QAAQ,GAAIzF,YAAY,CAACyF,QAAQ,CAACpF,MAAM,CAAG,CAAC,eACzDnC,KAAA,QAAKuG,SAAS,CAAC,2DAA2D,CAAAC,QAAA,eACxExG,KAAA,MAAGuG,SAAS,CAAC,uDAAuD,CAAAC,QAAA,eAClE1G,IAAA,CAACR,aAAa,EAACiH,SAAS,CAAC,cAAc,CAAE,CAAC,YAE5C,EAAG,CAAC,cACJzG,IAAA,OAAIyG,SAAS,CAAC,yDAAyD,CAAAC,QAAA,CACpE1E,YAAY,CAACyF,QAAQ,CAAC5D,GAAG,CAAC,CAAC6D,OAAO,CAAEF,KAAK,gBACxCxH,IAAA,OAAA0G,QAAA,CAAiBgB,OAAO,EAAfF,KAAoB,CAC9B,CAAC,CACA,CAAC,EACF,CACN,EACE,CAAC,cAENtH,KAAA,QAAKuG,SAAS,CAAC,4DAA4D,CAAAC,QAAA,eACzE1G,IAAA,QAAKyG,SAAS,CAAC,+CAA+C,CAAAC,QAAA,cAC5D1G,IAAA,OAAIyG,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CAAC,8BAA4B,CAAI,CAAC,CAChF,CAAC,cACN1G,IAAA,QAAKyG,SAAS,CAAC,0BAA0B,CAAAC,QAAA,cACvCxG,KAAA,UAAOuG,SAAS,CAAC,oBAAoB,CAAAC,QAAA,eACnC1G,IAAA,UAAOyG,SAAS,CAAC,yBAAyB,CAAAC,QAAA,cACxCxG,KAAA,OAAAwG,QAAA,eACE1G,IAAA,OAAIyG,SAAS,CAAC,+CAA+C,CAAAC,QAAA,CAAC,GAAC,CAAI,CAAC,cACpE1G,IAAA,OAAIyG,SAAS,CAAC,+CAA+C,CAAAC,QAAA,CAAC,MAAI,CAAI,CAAC,cACvE1G,IAAA,OAAIyG,SAAS,CAAC,+CAA+C,CAAAC,QAAA,CAAC,UAAQ,CAAI,CAAC,cAC3E1G,IAAA,OAAIyG,SAAS,CAAC,+CAA+C,CAAAC,QAAA,CAAC,OAAK,CAAI,CAAC,cACxE1G,IAAA,OAAIyG,SAAS,CAAC,+CAA+C,CAAAC,QAAA,CAAC,OAAK,CAAI,CAAC,EACtE,CAAC,CACA,CAAC,cACRxG,KAAA,UAAAwG,QAAA,EACG1E,YAAY,SAAZA,YAAY,kBAAAxB,mBAAA,CAAZwB,YAAY,CAAE4D,IAAI,UAAApF,mBAAA,iBAAlBA,mBAAA,CAAoB+F,KAAK,CAAC,CAAC,CAAE,EAAE,CAAC,CAAC1C,GAAG,CAAC,CAACvB,OAAO,CAAEkF,KAAK,QAAAG,cAAA,oBACnDzH,KAAA,OAAgBuG,SAAS,CAAC,2CAA2C,CAAAC,QAAA,eACnE1G,IAAA,OAAIyG,SAAS,CAAC,yBAAyB,CAAAC,QAAA,CAAEc,KAAK,CAAG,CAAC,CAAK,CAAC,cACxDxH,IAAA,OAAIyG,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAEpE,OAAO,CAACyB,IAAI,CAAK,CAAC,cACzD/D,IAAA,OAAIyG,SAAS,CAAC,WAAW,CAAAC,QAAA,CAAEpE,OAAO,CAAChB,QAAQ,CAAK,CAAC,cACjDpB,KAAA,OAAIuG,SAAS,CAAC,sCAAsC,CAAAC,QAAA,EAAC,MAAI,EAAAiB,cAAA,CAACrF,OAAO,CAACI,KAAK,UAAAiF,cAAA,iBAAbA,cAAA,CAAeC,cAAc,CAAC,CAAC,EAAK,CAAC,cAC/F5H,IAAA,OAAIyG,SAAS,CAAC,WAAW,CAAAC,QAAA,CAAEpE,OAAO,CAACM,aAAa,CAAK,CAAC,GAL/C4E,KAML,CAAC,EACN,CAAC,CACD,CAAC,CAAAxF,YAAY,SAAZA,YAAY,kBAAAvB,mBAAA,CAAZuB,YAAY,CAAE4D,IAAI,UAAAnF,mBAAA,iBAAlBA,mBAAA,CAAoB4B,MAAM,GAAI,CAAC,EAAI,EAAE,eACrCrC,IAAA,OAAIyG,SAAS,CAAC,qCAAqC,CAAAC,QAAA,cACjDxG,KAAA,OAAI2H,OAAO,CAAE,CAAE,CAACpB,SAAS,CAAC,iDAAiD,CAAAC,QAAA,EAAC,UAClE,CAAC,CAAC,CAAA1E,YAAY,SAAZA,YAAY,kBAAAtB,mBAAA,CAAZsB,YAAY,CAAE4D,IAAI,UAAAlF,mBAAA,iBAAlBA,mBAAA,CAAoB2B,MAAM,GAAI,CAAC,EAAI,EAAE,CAAC,gBAClD,EAAI,CAAC,CACH,CACL,EACI,CAAC,EACH,CAAC,CACL,CAAC,EACH,CAAC,EACH,CACN,cAEDnC,KAAA,QAAKuG,SAAS,CAAC,wCAAwC,CAAAC,QAAA,eACrD1G,IAAA,WACE2G,OAAO,CAAEA,CAAA,GAAM,CACbxF,YAAY,CAAC,IAAI,CAAC,CAClBY,aAAa,CAAC,IAAI,CAAC,CACnBE,eAAe,CAAC,IAAI,CAAC,CACrBE,oBAAoB,CAAC,KAAK,CAAC,CAC7B,CAAE,CACFsE,SAAS,CAAC,4EAA4E,CAAAC,QAAA,CACvF,MAED,CAAQ,CAAC,cAETxG,KAAA,QAAKuG,SAAS,CAAC,gBAAgB,CAAAC,QAAA,EAC5B,CAACxE,iBAAiB,eACjBhC,KAAA,WACEyG,OAAO,CAAEH,sBAAuB,CAChCC,SAAS,CAAC,iFAAiF,CAAAC,QAAA,eAE3F1G,IAAA,CAACL,eAAe,EAAC8G,SAAS,CAAC,cAAc,CAAE,CAAC,oBAE9C,EAAQ,CACT,CAEAvE,iBAAiB,eAChBhC,KAAA,CAAAE,SAAA,EAAAsG,QAAA,eACE1G,IAAA,WACE2G,OAAO,CAAEA,CAAA,GAAM,CACb5E,aAAa,CAAC,IAAI,CAAC,CACnBE,eAAe,CAAC,IAAI,CAAC,CACrBE,oBAAoB,CAAC,KAAK,CAAC,CAC7B,CAAE,CACFsE,SAAS,CAAC,4EAA4E,CAAAC,QAAA,CACvF,uBAED,CAAQ,CAAC,cACTxG,KAAA,WACEyG,OAAO,CAAEb,mBAAoB,CAC7BqB,QAAQ,CAAEvF,UAAU,EAAI,EAACI,YAAY,SAAZA,YAAY,WAAZA,YAAY,CAAE4D,IAAI,GAAI5D,YAAY,CAAC4D,IAAI,CAACvD,MAAM,GAAK,CAAE,CAC9EoE,SAAS,CAAC,+IAA+I,CAAAC,QAAA,eAEzJ1G,IAAA,CAACb,MAAM,EAACsH,SAAS,CAAC,cAAc,CAAE,CAAC,CAClC7E,UAAU,CAAG,qBAAqB,oBAAAuB,MAAA,CAAsB,CAAAnB,YAAY,SAAZA,YAAY,kBAAArB,mBAAA,CAAZqB,YAAY,CAAE4D,IAAI,UAAAjF,mBAAA,iBAAlBA,mBAAA,CAAoB0B,MAAM,GAAI,CAAC,cAAY,EAC9F,CAAC,EACT,CACH,EACE,CAAC,EACH,CAAC,EACH,CAAC,CACJ,IAAI,EACL,CAAC,CACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAAhC,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}