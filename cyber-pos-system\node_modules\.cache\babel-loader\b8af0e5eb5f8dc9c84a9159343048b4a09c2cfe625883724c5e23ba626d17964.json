{"ast": null, "code": "import _objectSpread from\"E:/FX/Cyber POS/cyber-pos-system/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import{useState}from'react';import{collection,doc,addDoc,getDoc,serverTimestamp,runTransaction,writeBatch}from'firebase/firestore';import{db}from'../config/firebase';import{cleanObject}from'../utils/objectUtils';export const useTransactions=()=>{const[loading,setLoading]=useState(false);const[error,setError]=useState(null);// Create or update customer for credit sales\nconst createOrUpdateCustomer=async(customerName,customerPhone)=>{try{// Check if customer already exists by name\nconst customersRef=collection(db,'customers');const customerData={name:customerName.trim(),phone:customerPhone===null||customerPhone===void 0?void 0:customerPhone.trim(),totalDebt:0,isActive:true,createdAt:new Date(),updatedAt:new Date()};// For simplicity, create a new customer document\n// In production, you might want to search for existing customers first\nconst customerDoc=await addDoc(customersRef,_objectSpread(_objectSpread({},customerData),{},{createdAt:serverTimestamp(),updatedAt:serverTimestamp()}));return customerDoc.id;}catch(error){console.error('Error creating customer:',error);throw new Error('Failed to create customer record');}};// Update product stock quantities\nconst updateProductStock=async items=>{const batch=writeBatch(db);for(const item of items){if(item.type==='product'){const productRef=doc(db,'products',item.itemId);const productDoc=await getDoc(productRef);if(productDoc.exists()){const currentStock=productDoc.data().stockQuantity||0;const newStock=Math.max(0,currentStock-item.quantity);batch.update(productRef,{stockQuantity:newStock,updatedAt:serverTimestamp()});}}}await batch.commit();};// Save transaction to Firebase\nconst saveTransaction=async(cartState,paymentData,attendantId)=>{setLoading(true);setError(null);try{var _paymentData$notes;// Prepare transaction items - cleanObject will remove undefined values\nconst transactionItems=cartState.items.map(item=>{var _item$notes;return cleanObject({id:item.id,type:item.type,itemId:item.itemId,name:item.name,quantity:item.quantity,unitPrice:item.unitPrice,totalPrice:item.totalPrice,notes:(_item$notes=item.notes)===null||_item$notes===void 0?void 0:_item$notes.trim()});});// Handle customer creation for credit sales\nlet customerId;if(paymentData.paymentMethod==='debt'&&paymentData.customerName){customerId=await createOrUpdateCustomer(paymentData.customerName,paymentData.customerPhone);}// Prepare transaction data - ensure no undefined values\nconst rawTransactionData={items:transactionItems,subtotal:cartState.subtotal,discount:cartState.discount||0,total:cartState.total,paymentMethod:paymentData.paymentMethod,customerId,attendantId,status:'completed',notes:(_paymentData$notes=paymentData.notes)===null||_paymentData$notes===void 0?void 0:_paymentData$notes.trim(),createdAt:new Date(),updatedAt:new Date()};// Clean the transaction data to remove any undefined values\nconst transactionData=cleanObject(rawTransactionData);// Use Firestore transaction to ensure data consistency\nconst transactionId=await runTransaction(db,async transaction=>{// Create the transaction document\nconst transactionRef=doc(collection(db,'transactions'));transaction.set(transactionRef,_objectSpread(_objectSpread({},transactionData),{},{createdAt:serverTimestamp(),updatedAt:serverTimestamp()}));// Update customer debt if it's a credit sale\nif(customerId&&paymentData.paymentMethod==='debt'){const customerRef=doc(db,'customers',customerId);const customerDoc=await transaction.get(customerRef);if(customerDoc.exists()){const currentDebt=customerDoc.data().totalDebt||0;transaction.update(customerRef,{totalDebt:currentDebt+cartState.total,updatedAt:serverTimestamp()});}}return transactionRef.id;});// Update product stock (outside of the transaction for better performance)\nawait updateProductStock(transactionItems);return transactionId;}catch(error){console.error('Error saving transaction:',error);setError('Failed to save transaction');throw error;}finally{setLoading(false);}};return{saveTransaction,loading,error};};", "map": {"version": 3, "names": ["useState", "collection", "doc", "addDoc", "getDoc", "serverTimestamp", "runTransaction", "writeBatch", "db", "cleanObject", "useTransactions", "loading", "setLoading", "error", "setError", "createOrUpdateCustomer", "customerName", "customerPhone", "customersRef", "customerData", "name", "trim", "phone", "totalDebt", "isActive", "createdAt", "Date", "updatedAt", "customerDoc", "_objectSpread", "id", "console", "Error", "updateProductStock", "items", "batch", "item", "type", "productRef", "itemId", "productDoc", "exists", "currentStock", "data", "stockQuantity", "newStock", "Math", "max", "quantity", "update", "commit", "saveTransaction", "cartState", "paymentData", "attendantId", "_paymentData$notes", "transactionItems", "map", "_item$notes", "unitPrice", "totalPrice", "notes", "customerId", "paymentMethod", "rawTransactionData", "subtotal", "discount", "total", "status", "transactionData", "transactionId", "transaction", "transactionRef", "set", "customerRef", "get", "currentDebt"], "sources": ["E:/FX/Cyber POS/cyber-pos-system/src/hooks/useTransactions.ts"], "sourcesContent": ["import { useState } from 'react';\nimport {\n  collection,\n  doc,\n  addDoc,\n  getDoc,\n  serverTimestamp,\n  runTransaction,\n  writeBatch\n} from 'firebase/firestore';\nimport { db } from '../config/firebase';\nimport { TransactionItem, Customer, PaymentMethod } from '../types';\nimport { CartState } from './useCart';\nimport { cleanObject } from '../utils/objectUtils';\n\nexport const useTransactions = () => {\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n\n  // Create or update customer for credit sales\n  const createOrUpdateCustomer = async (customerName: string, customerPhone?: string): Promise<string> => {\n    try {\n      // Check if customer already exists by name\n      const customersRef = collection(db, 'customers');\n      const customerData: Omit<Customer, 'id'> = {\n        name: customerName.trim(),\n        phone: customerPhone?.trim(),\n        totalDebt: 0,\n        isActive: true,\n        createdAt: new Date(),\n        updatedAt: new Date(),\n      };\n\n      // For simplicity, create a new customer document\n      // In production, you might want to search for existing customers first\n      const customerDoc = await addDoc(customersRef, {\n        ...customerData,\n        createdAt: serverTimestamp(),\n        updatedAt: serverTimestamp(),\n      });\n\n      return customerDoc.id;\n    } catch (error) {\n      console.error('Error creating customer:', error);\n      throw new Error('Failed to create customer record');\n    }\n  };\n\n  // Update product stock quantities\n  const updateProductStock = async (items: TransactionItem[]) => {\n    const batch = writeBatch(db);\n    \n    for (const item of items) {\n      if (item.type === 'product') {\n        const productRef = doc(db, 'products', item.itemId);\n        const productDoc = await getDoc(productRef);\n        \n        if (productDoc.exists()) {\n          const currentStock = productDoc.data().stockQuantity || 0;\n          const newStock = Math.max(0, currentStock - item.quantity);\n          \n          batch.update(productRef, {\n            stockQuantity: newStock,\n            updatedAt: serverTimestamp(),\n          });\n        }\n      }\n    }\n    \n    await batch.commit();\n  };\n\n  // Save transaction to Firebase\n  const saveTransaction = async (\n    cartState: CartState,\n    paymentData: {\n      paymentMethod: PaymentMethod;\n      customerName?: string;\n      customerPhone?: string;\n      notes?: string;\n    },\n    attendantId: string\n  ): Promise<string> => {\n    setLoading(true);\n    setError(null);\n\n    try {\n      // Prepare transaction items - cleanObject will remove undefined values\n      const transactionItems: TransactionItem[] = cartState.items.map(item =>\n        cleanObject({\n          id: item.id,\n          type: item.type,\n          itemId: item.itemId,\n          name: item.name,\n          quantity: item.quantity,\n          unitPrice: item.unitPrice,\n          totalPrice: item.totalPrice,\n          notes: item.notes?.trim(),\n        })\n      );\n\n      // Handle customer creation for credit sales\n      let customerId: string | undefined;\n      if (paymentData.paymentMethod === 'debt' && paymentData.customerName) {\n        customerId = await createOrUpdateCustomer(paymentData.customerName, paymentData.customerPhone);\n      }\n\n      // Prepare transaction data - ensure no undefined values\n      const rawTransactionData = {\n        items: transactionItems,\n        subtotal: cartState.subtotal,\n        discount: cartState.discount || 0,\n        total: cartState.total,\n        paymentMethod: paymentData.paymentMethod,\n        customerId,\n        attendantId,\n        status: 'completed',\n        notes: paymentData.notes?.trim(),\n        createdAt: new Date(),\n        updatedAt: new Date(),\n      };\n\n      // Clean the transaction data to remove any undefined values\n      const transactionData = cleanObject(rawTransactionData);\n\n      // Use Firestore transaction to ensure data consistency\n      const transactionId = await runTransaction(db, async (transaction) => {\n        // Create the transaction document\n        const transactionRef = doc(collection(db, 'transactions'));\n        transaction.set(transactionRef, {\n          ...transactionData,\n          createdAt: serverTimestamp(),\n          updatedAt: serverTimestamp(),\n        });\n\n        // Update customer debt if it's a credit sale\n        if (customerId && paymentData.paymentMethod === 'debt') {\n          const customerRef = doc(db, 'customers', customerId);\n          const customerDoc = await transaction.get(customerRef);\n          \n          if (customerDoc.exists()) {\n            const currentDebt = customerDoc.data().totalDebt || 0;\n            transaction.update(customerRef, {\n              totalDebt: currentDebt + cartState.total,\n              updatedAt: serverTimestamp(),\n            });\n          }\n        }\n\n        return transactionRef.id;\n      });\n\n      // Update product stock (outside of the transaction for better performance)\n      await updateProductStock(transactionItems);\n\n      return transactionId;\n    } catch (error) {\n      console.error('Error saving transaction:', error);\n      setError('Failed to save transaction');\n      throw error;\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return {\n    saveTransaction,\n    loading,\n    error,\n  };\n};\n"], "mappings": "qHAAA,OAASA,QAAQ,KAAQ,OAAO,CAChC,OACEC,UAAU,CACVC,GAAG,CACHC,MAAM,CACNC,MAAM,CACNC,eAAe,CACfC,cAAc,CACdC,UAAU,KACL,oBAAoB,CAC3B,OAASC,EAAE,KAAQ,oBAAoB,CAGvC,OAASC,WAAW,KAAQ,sBAAsB,CAElD,MAAO,MAAM,CAAAC,eAAe,CAAGA,CAAA,GAAM,CACnC,KAAM,CAACC,OAAO,CAAEC,UAAU,CAAC,CAAGZ,QAAQ,CAAC,KAAK,CAAC,CAC7C,KAAM,CAACa,KAAK,CAAEC,QAAQ,CAAC,CAAGd,QAAQ,CAAgB,IAAI,CAAC,CAEvD;AACA,KAAM,CAAAe,sBAAsB,CAAG,KAAAA,CAAOC,YAAoB,CAAEC,aAAsB,GAAsB,CACtG,GAAI,CACF;AACA,KAAM,CAAAC,YAAY,CAAGjB,UAAU,CAACO,EAAE,CAAE,WAAW,CAAC,CAChD,KAAM,CAAAW,YAAkC,CAAG,CACzCC,IAAI,CAAEJ,YAAY,CAACK,IAAI,CAAC,CAAC,CACzBC,KAAK,CAAEL,aAAa,SAAbA,aAAa,iBAAbA,aAAa,CAAEI,IAAI,CAAC,CAAC,CAC5BE,SAAS,CAAE,CAAC,CACZC,QAAQ,CAAE,IAAI,CACdC,SAAS,CAAE,GAAI,CAAAC,IAAI,CAAC,CAAC,CACrBC,SAAS,CAAE,GAAI,CAAAD,IAAI,CAAC,CACtB,CAAC,CAED;AACA;AACA,KAAM,CAAAE,WAAW,CAAG,KAAM,CAAAzB,MAAM,CAACe,YAAY,CAAAW,aAAA,CAAAA,aAAA,IACxCV,YAAY,MACfM,SAAS,CAAEpB,eAAe,CAAC,CAAC,CAC5BsB,SAAS,CAAEtB,eAAe,CAAC,CAAC,EAC7B,CAAC,CAEF,MAAO,CAAAuB,WAAW,CAACE,EAAE,CACvB,CAAE,MAAOjB,KAAK,CAAE,CACdkB,OAAO,CAAClB,KAAK,CAAC,0BAA0B,CAAEA,KAAK,CAAC,CAChD,KAAM,IAAI,CAAAmB,KAAK,CAAC,kCAAkC,CAAC,CACrD,CACF,CAAC,CAED;AACA,KAAM,CAAAC,kBAAkB,CAAG,KAAO,CAAAC,KAAwB,EAAK,CAC7D,KAAM,CAAAC,KAAK,CAAG5B,UAAU,CAACC,EAAE,CAAC,CAE5B,IAAK,KAAM,CAAA4B,IAAI,GAAI,CAAAF,KAAK,CAAE,CACxB,GAAIE,IAAI,CAACC,IAAI,GAAK,SAAS,CAAE,CAC3B,KAAM,CAAAC,UAAU,CAAGpC,GAAG,CAACM,EAAE,CAAE,UAAU,CAAE4B,IAAI,CAACG,MAAM,CAAC,CACnD,KAAM,CAAAC,UAAU,CAAG,KAAM,CAAApC,MAAM,CAACkC,UAAU,CAAC,CAE3C,GAAIE,UAAU,CAACC,MAAM,CAAC,CAAC,CAAE,CACvB,KAAM,CAAAC,YAAY,CAAGF,UAAU,CAACG,IAAI,CAAC,CAAC,CAACC,aAAa,EAAI,CAAC,CACzD,KAAM,CAAAC,QAAQ,CAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,CAAEL,YAAY,CAAGN,IAAI,CAACY,QAAQ,CAAC,CAE1Db,KAAK,CAACc,MAAM,CAACX,UAAU,CAAE,CACvBM,aAAa,CAAEC,QAAQ,CACvBlB,SAAS,CAAEtB,eAAe,CAAC,CAC7B,CAAC,CAAC,CACJ,CACF,CACF,CAEA,KAAM,CAAA8B,KAAK,CAACe,MAAM,CAAC,CAAC,CACtB,CAAC,CAED;AACA,KAAM,CAAAC,eAAe,CAAG,KAAAA,CACtBC,SAAoB,CACpBC,WAKC,CACDC,WAAmB,GACC,CACpB1C,UAAU,CAAC,IAAI,CAAC,CAChBE,QAAQ,CAAC,IAAI,CAAC,CAEd,GAAI,KAAAyC,kBAAA,CACF;AACA,KAAM,CAAAC,gBAAmC,CAAGJ,SAAS,CAAClB,KAAK,CAACuB,GAAG,CAACrB,IAAI,OAAAsB,WAAA,OAClE,CAAAjD,WAAW,CAAC,CACVqB,EAAE,CAAEM,IAAI,CAACN,EAAE,CACXO,IAAI,CAAED,IAAI,CAACC,IAAI,CACfE,MAAM,CAAEH,IAAI,CAACG,MAAM,CACnBnB,IAAI,CAAEgB,IAAI,CAAChB,IAAI,CACf4B,QAAQ,CAAEZ,IAAI,CAACY,QAAQ,CACvBW,SAAS,CAAEvB,IAAI,CAACuB,SAAS,CACzBC,UAAU,CAAExB,IAAI,CAACwB,UAAU,CAC3BC,KAAK,EAAAH,WAAA,CAAEtB,IAAI,CAACyB,KAAK,UAAAH,WAAA,iBAAVA,WAAA,CAAYrC,IAAI,CAAC,CAC1B,CAAC,CAAC,EACJ,CAAC,CAED;AACA,GAAI,CAAAyC,UAA8B,CAClC,GAAIT,WAAW,CAACU,aAAa,GAAK,MAAM,EAAIV,WAAW,CAACrC,YAAY,CAAE,CACpE8C,UAAU,CAAG,KAAM,CAAA/C,sBAAsB,CAACsC,WAAW,CAACrC,YAAY,CAAEqC,WAAW,CAACpC,aAAa,CAAC,CAChG,CAEA;AACA,KAAM,CAAA+C,kBAAkB,CAAG,CACzB9B,KAAK,CAAEsB,gBAAgB,CACvBS,QAAQ,CAAEb,SAAS,CAACa,QAAQ,CAC5BC,QAAQ,CAAEd,SAAS,CAACc,QAAQ,EAAI,CAAC,CACjCC,KAAK,CAAEf,SAAS,CAACe,KAAK,CACtBJ,aAAa,CAAEV,WAAW,CAACU,aAAa,CACxCD,UAAU,CACVR,WAAW,CACXc,MAAM,CAAE,WAAW,CACnBP,KAAK,EAAAN,kBAAA,CAAEF,WAAW,CAACQ,KAAK,UAAAN,kBAAA,iBAAjBA,kBAAA,CAAmBlC,IAAI,CAAC,CAAC,CAChCI,SAAS,CAAE,GAAI,CAAAC,IAAI,CAAC,CAAC,CACrBC,SAAS,CAAE,GAAI,CAAAD,IAAI,CAAC,CACtB,CAAC,CAED;AACA,KAAM,CAAA2C,eAAe,CAAG5D,WAAW,CAACuD,kBAAkB,CAAC,CAEvD;AACA,KAAM,CAAAM,aAAa,CAAG,KAAM,CAAAhE,cAAc,CAACE,EAAE,CAAE,KAAO,CAAA+D,WAAW,EAAK,CACpE;AACA,KAAM,CAAAC,cAAc,CAAGtE,GAAG,CAACD,UAAU,CAACO,EAAE,CAAE,cAAc,CAAC,CAAC,CAC1D+D,WAAW,CAACE,GAAG,CAACD,cAAc,CAAA3C,aAAA,CAAAA,aAAA,IACzBwC,eAAe,MAClB5C,SAAS,CAAEpB,eAAe,CAAC,CAAC,CAC5BsB,SAAS,CAAEtB,eAAe,CAAC,CAAC,EAC7B,CAAC,CAEF;AACA,GAAIyD,UAAU,EAAIT,WAAW,CAACU,aAAa,GAAK,MAAM,CAAE,CACtD,KAAM,CAAAW,WAAW,CAAGxE,GAAG,CAACM,EAAE,CAAE,WAAW,CAAEsD,UAAU,CAAC,CACpD,KAAM,CAAAlC,WAAW,CAAG,KAAM,CAAA2C,WAAW,CAACI,GAAG,CAACD,WAAW,CAAC,CAEtD,GAAI9C,WAAW,CAACa,MAAM,CAAC,CAAC,CAAE,CACxB,KAAM,CAAAmC,WAAW,CAAGhD,WAAW,CAACe,IAAI,CAAC,CAAC,CAACpB,SAAS,EAAI,CAAC,CACrDgD,WAAW,CAACtB,MAAM,CAACyB,WAAW,CAAE,CAC9BnD,SAAS,CAAEqD,WAAW,CAAGxB,SAAS,CAACe,KAAK,CACxCxC,SAAS,CAAEtB,eAAe,CAAC,CAC7B,CAAC,CAAC,CACJ,CACF,CAEA,MAAO,CAAAmE,cAAc,CAAC1C,EAAE,CAC1B,CAAC,CAAC,CAEF;AACA,KAAM,CAAAG,kBAAkB,CAACuB,gBAAgB,CAAC,CAE1C,MAAO,CAAAc,aAAa,CACtB,CAAE,MAAOzD,KAAK,CAAE,CACdkB,OAAO,CAAClB,KAAK,CAAC,2BAA2B,CAAEA,KAAK,CAAC,CACjDC,QAAQ,CAAC,4BAA4B,CAAC,CACtC,KAAM,CAAAD,KAAK,CACb,CAAC,OAAS,CACRD,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED,MAAO,CACLuC,eAAe,CACfxC,OAAO,CACPE,KACF,CAAC,CACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}