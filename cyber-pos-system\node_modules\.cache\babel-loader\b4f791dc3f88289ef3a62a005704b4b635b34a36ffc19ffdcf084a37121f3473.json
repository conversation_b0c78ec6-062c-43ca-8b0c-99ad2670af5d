{"ast": null, "code": "import React,{useState}from'react';import{testFirebaseConnection,testServicesQuery}from'../../utils/firebaseConnectionTest';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const FirebaseConnectionTest=()=>{const[connectionResult,setConnectionResult]=useState(null);const[servicesResult,setServicesResult]=useState(null);const[testing,setTesting]=useState(false);const runConnectionTest=async()=>{setTesting(true);try{const result=await testFirebaseConnection();setConnectionResult(result);}catch(error){setConnectionResult({success:false,message:\"Test failed: \".concat(error)});}setTesting(false);};const runServicesTest=async()=>{setTesting(true);try{const result=await testServicesQuery();setServicesResult(result);}catch(error){setServicesResult({success:false,message:\"Test failed: \".concat(error)});}setTesting(false);};const runAllTests=async()=>{await runConnectionTest();await runServicesTest();};return/*#__PURE__*/_jsxs(\"div\",{className:\"p-6 bg-white rounded-lg shadow-md\",children:[/*#__PURE__*/_jsx(\"h2\",{className:\"text-xl font-bold mb-4\",children:\"Firebase Connection Test\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-4\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex space-x-2\",children:[/*#__PURE__*/_jsx(\"button\",{onClick:runConnectionTest,disabled:testing,className:\"px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50\",children:\"Test Connection\"}),/*#__PURE__*/_jsx(\"button\",{onClick:runServicesTest,disabled:testing,className:\"px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 disabled:opacity-50\",children:\"Test Services Query\"}),/*#__PURE__*/_jsx(\"button\",{onClick:runAllTests,disabled:testing,className:\"px-4 py-2 bg-purple-500 text-white rounded hover:bg-purple-600 disabled:opacity-50\",children:\"Run All Tests\"})]}),testing&&/*#__PURE__*/_jsx(\"div\",{className:\"text-blue-600\",children:\"\\uD83D\\uDD04 Running tests...\"}),connectionResult&&/*#__PURE__*/_jsxs(\"div\",{className:\"p-4 rounded \".concat(connectionResult.success?'bg-green-100 text-green-800':'bg-red-100 text-red-800'),children:[/*#__PURE__*/_jsx(\"h3\",{className:\"font-semibold\",children:\"Connection Test Result:\"}),/*#__PURE__*/_jsx(\"p\",{children:connectionResult.message}),connectionResult.details&&/*#__PURE__*/_jsx(\"pre\",{className:\"mt-2 text-sm\",children:JSON.stringify(connectionResult.details,null,2)})]}),servicesResult&&/*#__PURE__*/_jsxs(\"div\",{className:\"p-4 rounded \".concat(servicesResult.success?'bg-green-100 text-green-800':'bg-red-100 text-red-800'),children:[/*#__PURE__*/_jsx(\"h3\",{className:\"font-semibold\",children:\"Services Query Test Result:\"}),/*#__PURE__*/_jsx(\"p\",{children:servicesResult.message}),servicesResult.servicesCount!==undefined&&/*#__PURE__*/_jsxs(\"p\",{className:\"mt-1\",children:[\"Services found: \",servicesResult.servicesCount]})]})]})]});};export default FirebaseConnectionTest;", "map": {"version": 3, "names": ["React", "useState", "testFirebaseConnection", "testServicesQuery", "jsx", "_jsx", "jsxs", "_jsxs", "FirebaseConnectionTest", "connectionResult", "setConnectionResult", "servicesResult", "setServicesResult", "testing", "setTesting", "runConnectionTest", "result", "error", "success", "message", "concat", "runServicesTest", "runAllTests", "className", "children", "onClick", "disabled", "details", "JSON", "stringify", "servicesCount", "undefined"], "sources": ["E:/FX/Cyber POS/cyber-pos-system/src/components/debug/FirebaseConnectionTest.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { testFirebaseConnection, testServicesQuery } from '../../utils/firebaseConnectionTest';\n\ninterface TestResult {\n  success: boolean;\n  message: string;\n  details?: any;\n  servicesCount?: number;\n}\n\nconst FirebaseConnectionTest: React.FC = () => {\n  const [connectionResult, setConnectionResult] = useState<TestResult | null>(null);\n  const [servicesResult, setServicesResult] = useState<TestResult | null>(null);\n  const [testing, setTesting] = useState(false);\n\n  const runConnectionTest = async () => {\n    setTesting(true);\n    try {\n      const result = await testFirebaseConnection();\n      setConnectionResult(result);\n    } catch (error) {\n      setConnectionResult({\n        success: false,\n        message: `Test failed: ${error}`,\n      });\n    }\n    setTesting(false);\n  };\n\n  const runServicesTest = async () => {\n    setTesting(true);\n    try {\n      const result = await testServicesQuery();\n      setServicesResult(result);\n    } catch (error) {\n      setServicesResult({\n        success: false,\n        message: `Test failed: ${error}`,\n      });\n    }\n    setTesting(false);\n  };\n\n  const runAllTests = async () => {\n    await runConnectionTest();\n    await runServicesTest();\n  };\n\n  return (\n    <div className=\"p-6 bg-white rounded-lg shadow-md\">\n      <h2 className=\"text-xl font-bold mb-4\">Firebase Connection Test</h2>\n      \n      <div className=\"space-y-4\">\n        <div className=\"flex space-x-2\">\n          <button\n            onClick={runConnectionTest}\n            disabled={testing}\n            className=\"px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50\"\n          >\n            Test Connection\n          </button>\n          <button\n            onClick={runServicesTest}\n            disabled={testing}\n            className=\"px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 disabled:opacity-50\"\n          >\n            Test Services Query\n          </button>\n          <button\n            onClick={runAllTests}\n            disabled={testing}\n            className=\"px-4 py-2 bg-purple-500 text-white rounded hover:bg-purple-600 disabled:opacity-50\"\n          >\n            Run All Tests\n          </button>\n        </div>\n\n        {testing && (\n          <div className=\"text-blue-600\">\n            🔄 Running tests...\n          </div>\n        )}\n\n        {connectionResult && (\n          <div className={`p-4 rounded ${connectionResult.success ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>\n            <h3 className=\"font-semibold\">Connection Test Result:</h3>\n            <p>{connectionResult.message}</p>\n            {connectionResult.details && (\n              <pre className=\"mt-2 text-sm\">\n                {JSON.stringify(connectionResult.details, null, 2)}\n              </pre>\n            )}\n          </div>\n        )}\n\n        {servicesResult && (\n          <div className={`p-4 rounded ${servicesResult.success ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>\n            <h3 className=\"font-semibold\">Services Query Test Result:</h3>\n            <p>{servicesResult.message}</p>\n            {servicesResult.servicesCount !== undefined && (\n              <p className=\"mt-1\">Services found: {servicesResult.servicesCount}</p>\n            )}\n          </div>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default FirebaseConnectionTest;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,KAAQ,OAAO,CACvC,OAASC,sBAAsB,CAAEC,iBAAiB,KAAQ,oCAAoC,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAS/F,KAAM,CAAAC,sBAAgC,CAAGA,CAAA,GAAM,CAC7C,KAAM,CAACC,gBAAgB,CAAEC,mBAAmB,CAAC,CAAGT,QAAQ,CAAoB,IAAI,CAAC,CACjF,KAAM,CAACU,cAAc,CAAEC,iBAAiB,CAAC,CAAGX,QAAQ,CAAoB,IAAI,CAAC,CAC7E,KAAM,CAACY,OAAO,CAAEC,UAAU,CAAC,CAAGb,QAAQ,CAAC,KAAK,CAAC,CAE7C,KAAM,CAAAc,iBAAiB,CAAG,KAAAA,CAAA,GAAY,CACpCD,UAAU,CAAC,IAAI,CAAC,CAChB,GAAI,CACF,KAAM,CAAAE,MAAM,CAAG,KAAM,CAAAd,sBAAsB,CAAC,CAAC,CAC7CQ,mBAAmB,CAACM,MAAM,CAAC,CAC7B,CAAE,MAAOC,KAAK,CAAE,CACdP,mBAAmB,CAAC,CAClBQ,OAAO,CAAE,KAAK,CACdC,OAAO,iBAAAC,MAAA,CAAkBH,KAAK,CAChC,CAAC,CAAC,CACJ,CACAH,UAAU,CAAC,KAAK,CAAC,CACnB,CAAC,CAED,KAAM,CAAAO,eAAe,CAAG,KAAAA,CAAA,GAAY,CAClCP,UAAU,CAAC,IAAI,CAAC,CAChB,GAAI,CACF,KAAM,CAAAE,MAAM,CAAG,KAAM,CAAAb,iBAAiB,CAAC,CAAC,CACxCS,iBAAiB,CAACI,MAAM,CAAC,CAC3B,CAAE,MAAOC,KAAK,CAAE,CACdL,iBAAiB,CAAC,CAChBM,OAAO,CAAE,KAAK,CACdC,OAAO,iBAAAC,MAAA,CAAkBH,KAAK,CAChC,CAAC,CAAC,CACJ,CACAH,UAAU,CAAC,KAAK,CAAC,CACnB,CAAC,CAED,KAAM,CAAAQ,WAAW,CAAG,KAAAA,CAAA,GAAY,CAC9B,KAAM,CAAAP,iBAAiB,CAAC,CAAC,CACzB,KAAM,CAAAM,eAAe,CAAC,CAAC,CACzB,CAAC,CAED,mBACEd,KAAA,QAAKgB,SAAS,CAAC,mCAAmC,CAAAC,QAAA,eAChDnB,IAAA,OAAIkB,SAAS,CAAC,wBAAwB,CAAAC,QAAA,CAAC,0BAAwB,CAAI,CAAC,cAEpEjB,KAAA,QAAKgB,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxBjB,KAAA,QAAKgB,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eAC7BnB,IAAA,WACEoB,OAAO,CAAEV,iBAAkB,CAC3BW,QAAQ,CAAEb,OAAQ,CAClBU,SAAS,CAAC,gFAAgF,CAAAC,QAAA,CAC3F,iBAED,CAAQ,CAAC,cACTnB,IAAA,WACEoB,OAAO,CAAEJ,eAAgB,CACzBK,QAAQ,CAAEb,OAAQ,CAClBU,SAAS,CAAC,kFAAkF,CAAAC,QAAA,CAC7F,qBAED,CAAQ,CAAC,cACTnB,IAAA,WACEoB,OAAO,CAAEH,WAAY,CACrBI,QAAQ,CAAEb,OAAQ,CAClBU,SAAS,CAAC,oFAAoF,CAAAC,QAAA,CAC/F,eAED,CAAQ,CAAC,EACN,CAAC,CAELX,OAAO,eACNR,IAAA,QAAKkB,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,+BAE/B,CAAK,CACN,CAEAf,gBAAgB,eACfF,KAAA,QAAKgB,SAAS,gBAAAH,MAAA,CAAiBX,gBAAgB,CAACS,OAAO,CAAG,6BAA6B,CAAG,yBAAyB,CAAG,CAAAM,QAAA,eACpHnB,IAAA,OAAIkB,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,yBAAuB,CAAI,CAAC,cAC1DnB,IAAA,MAAAmB,QAAA,CAAIf,gBAAgB,CAACU,OAAO,CAAI,CAAC,CAChCV,gBAAgB,CAACkB,OAAO,eACvBtB,IAAA,QAAKkB,SAAS,CAAC,cAAc,CAAAC,QAAA,CAC1BI,IAAI,CAACC,SAAS,CAACpB,gBAAgB,CAACkB,OAAO,CAAE,IAAI,CAAE,CAAC,CAAC,CAC/C,CACN,EACE,CACN,CAEAhB,cAAc,eACbJ,KAAA,QAAKgB,SAAS,gBAAAH,MAAA,CAAiBT,cAAc,CAACO,OAAO,CAAG,6BAA6B,CAAG,yBAAyB,CAAG,CAAAM,QAAA,eAClHnB,IAAA,OAAIkB,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,6BAA2B,CAAI,CAAC,cAC9DnB,IAAA,MAAAmB,QAAA,CAAIb,cAAc,CAACQ,OAAO,CAAI,CAAC,CAC9BR,cAAc,CAACmB,aAAa,GAAKC,SAAS,eACzCxB,KAAA,MAAGgB,SAAS,CAAC,MAAM,CAAAC,QAAA,EAAC,kBAAgB,CAACb,cAAc,CAACmB,aAAa,EAAI,CACtE,EACE,CACN,EACE,CAAC,EACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAAtB,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}