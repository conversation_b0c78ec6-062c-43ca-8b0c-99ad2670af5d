{"ast": null, "code": "import _objectSpread from\"E:/FX/Cyber POS/cyber-pos-system/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import{doc,getDoc,setDoc}from'firebase/firestore';import{ref,uploadBytes,getDownloadURL,deleteObject}from'firebase/storage';import{db,storage}from'../config/firebase';const SETTINGS_COLLECTION='settings';const BUSINESS_SETTINGS_DOC='business';const LOGO_STORAGE_PATH='business/logos';/**\n * Get business settings from Firestore\n */export const getBusinessSettings=async()=>{try{const docRef=doc(db,SETTINGS_COLLECTION,BUSINESS_SETTINGS_DOC);const docSnap=await getDoc(docRef);if(docSnap.exists()){var _data$createdAt,_data$updatedAt;const data=docSnap.data();return _objectSpread(_objectSpread({},data),{},{createdAt:((_data$createdAt=data.createdAt)===null||_data$createdAt===void 0?void 0:_data$createdAt.toDate())||new Date(),updatedAt:((_data$updatedAt=data.updatedAt)===null||_data$updatedAt===void 0?void 0:_data$updatedAt.toDate())||new Date()});}return null;}catch(error){console.error('Error fetching business settings:',error);throw new Error('Failed to fetch business settings');}};/**\n * Save business settings to Firestore\n */export const saveBusinessSettings=async settings=>{try{var _existingDoc$data,_existingDoc$data$cre;const now=new Date();const docRef=doc(db,SETTINGS_COLLECTION,BUSINESS_SETTINGS_DOC);// Check if document exists to determine if this is an update or create\nconst existingDoc=await getDoc(docRef);const isUpdate=existingDoc.exists();const businessSettings=_objectSpread(_objectSpread({id:BUSINESS_SETTINGS_DOC},settings),{},{createdAt:isUpdate?((_existingDoc$data=existingDoc.data())===null||_existingDoc$data===void 0?void 0:(_existingDoc$data$cre=_existingDoc$data.createdAt)===null||_existingDoc$data$cre===void 0?void 0:_existingDoc$data$cre.toDate())||now:now,updatedAt:now});await setDoc(docRef,_objectSpread(_objectSpread({},businessSettings),{},{createdAt:businessSettings.createdAt,updatedAt:businessSettings.updatedAt}));return businessSettings;}catch(error){console.error('Error saving business settings:',error);throw new Error('Failed to save business settings');}};/**\n * Upload logo file to Firebase Storage\n */export const uploadLogo=async file=>{try{// Validate file type\nif(!file.type.startsWith('image/')){throw new Error('Please select an image file');}// Validate file size (max 5MB)\nconst maxSize=5*1024*1024;// 5MB\nif(file.size>maxSize){throw new Error('Image file size must be less than 5MB');}// Generate unique filename\nconst timestamp=Date.now();const fileName=\"logo_\".concat(timestamp,\"_\").concat(file.name);const logoRef=ref(storage,\"\".concat(LOGO_STORAGE_PATH,\"/\").concat(fileName));// Upload file\nconst uploadResult=await uploadBytes(logoRef,file);const downloadURL=await getDownloadURL(uploadResult.ref);return{url:downloadURL,fileName:fileName};}catch(error){console.error('Error uploading logo:',error);throw new Error(error instanceof Error?error.message:'Failed to upload logo');}};/**\n * Delete logo from Firebase Storage\n */export const deleteLogo=async fileName=>{try{const logoRef=ref(storage,\"\".concat(LOGO_STORAGE_PATH,\"/\").concat(fileName));await deleteObject(logoRef);}catch(error){console.error('Error deleting logo:',error);// Don't throw error for delete operations as the file might not exist\n}};/**\n * Get default business settings\n */export const getDefaultBusinessSettings=()=>{return{businessName:'Cyber Services & Stationery',address:'Your Business Address',phone:'+*********** 000',email:'<EMAIL>',currency:'KSh',taxRate:16,receiptFooter:'Thank you for your business!\\nVisit us again soon',operatingHours:'Mon-Fri: 8:00 AM - 6:00 PM\\nSat: 9:00 AM - 4:00 PM'};};/**\n * Initialize business settings with defaults if none exist\n */export const initializeBusinessSettings=async()=>{try{const existing=await getBusinessSettings();if(existing){return existing;}const defaultSettings=getDefaultBusinessSettings();return await saveBusinessSettings(defaultSettings);}catch(error){console.error('Error initializing business settings:',error);throw new Error('Failed to initialize business settings');}};", "map": {"version": 3, "names": ["doc", "getDoc", "setDoc", "ref", "uploadBytes", "getDownloadURL", "deleteObject", "db", "storage", "SETTINGS_COLLECTION", "BUSINESS_SETTINGS_DOC", "LOGO_STORAGE_PATH", "getBusinessSettings", "doc<PERSON>ef", "docSnap", "exists", "_data$createdAt", "_data$updatedAt", "data", "_objectSpread", "createdAt", "toDate", "Date", "updatedAt", "error", "console", "Error", "saveBusinessSettings", "settings", "_existingDoc$data", "_existingDoc$data$cre", "now", "existingDoc", "isUpdate", "businessSettings", "id", "uploadLogo", "file", "type", "startsWith", "maxSize", "size", "timestamp", "fileName", "concat", "name", "logoRef", "uploadResult", "downloadURL", "url", "message", "deleteLogo", "getDefaultBusinessSettings", "businessName", "address", "phone", "email", "currency", "taxRate", "receiptFooter", "operatingHours", "initializeBusinessSettings", "existing", "defaultSettings"], "sources": ["E:/FX/Cyber POS/cyber-pos-system/src/services/businessSettingsService.ts"], "sourcesContent": ["import { \n  doc, \n  getDoc, \n  setDoc, \n  collection, \n  query, \n  getDocs, \n  limit \n} from 'firebase/firestore';\nimport { \n  ref, \n  uploadBytes, \n  getDownloadURL, \n  deleteObject \n} from 'firebase/storage';\nimport { db, storage } from '../config/firebase';\nimport { BusinessSettings } from '../types';\n\nconst SETTINGS_COLLECTION = 'settings';\nconst BUSINESS_SETTINGS_DOC = 'business';\nconst LOGO_STORAGE_PATH = 'business/logos';\n\n/**\n * Get business settings from Firestore\n */\nexport const getBusinessSettings = async (): Promise<BusinessSettings | null> => {\n  try {\n    const docRef = doc(db, SETTINGS_COLLECTION, BUSINESS_SETTINGS_DOC);\n    const docSnap = await getDoc(docRef);\n    \n    if (docSnap.exists()) {\n      const data = docSnap.data();\n      return {\n        ...data,\n        createdAt: data.createdAt?.toDate() || new Date(),\n        updatedAt: data.updatedAt?.toDate() || new Date(),\n      } as BusinessSettings;\n    }\n    \n    return null;\n  } catch (error) {\n    console.error('Error fetching business settings:', error);\n    throw new Error('Failed to fetch business settings');\n  }\n};\n\n/**\n * Save business settings to Firestore\n */\nexport const saveBusinessSettings = async (settings: Omit<BusinessSettings, 'id' | 'createdAt' | 'updatedAt'>): Promise<BusinessSettings> => {\n  try {\n    const now = new Date();\n    const docRef = doc(db, SETTINGS_COLLECTION, BUSINESS_SETTINGS_DOC);\n    \n    // Check if document exists to determine if this is an update or create\n    const existingDoc = await getDoc(docRef);\n    const isUpdate = existingDoc.exists();\n    \n    const businessSettings: BusinessSettings = {\n      id: BUSINESS_SETTINGS_DOC,\n      ...settings,\n      createdAt: isUpdate ? existingDoc.data()?.createdAt?.toDate() || now : now,\n      updatedAt: now,\n    };\n    \n    await setDoc(docRef, {\n      ...businessSettings,\n      createdAt: businessSettings.createdAt,\n      updatedAt: businessSettings.updatedAt,\n    });\n    \n    return businessSettings;\n  } catch (error) {\n    console.error('Error saving business settings:', error);\n    throw new Error('Failed to save business settings');\n  }\n};\n\n/**\n * Upload logo file to Firebase Storage\n */\nexport const uploadLogo = async (file: File): Promise<{ url: string; fileName: string }> => {\n  try {\n    // Validate file type\n    if (!file.type.startsWith('image/')) {\n      throw new Error('Please select an image file');\n    }\n    \n    // Validate file size (max 5MB)\n    const maxSize = 5 * 1024 * 1024; // 5MB\n    if (file.size > maxSize) {\n      throw new Error('Image file size must be less than 5MB');\n    }\n    \n    // Generate unique filename\n    const timestamp = Date.now();\n    const fileName = `logo_${timestamp}_${file.name}`;\n    const logoRef = ref(storage, `${LOGO_STORAGE_PATH}/${fileName}`);\n    \n    // Upload file\n    const uploadResult = await uploadBytes(logoRef, file);\n    const downloadURL = await getDownloadURL(uploadResult.ref);\n    \n    return {\n      url: downloadURL,\n      fileName: fileName,\n    };\n  } catch (error) {\n    console.error('Error uploading logo:', error);\n    throw new Error(error instanceof Error ? error.message : 'Failed to upload logo');\n  }\n};\n\n/**\n * Delete logo from Firebase Storage\n */\nexport const deleteLogo = async (fileName: string): Promise<void> => {\n  try {\n    const logoRef = ref(storage, `${LOGO_STORAGE_PATH}/${fileName}`);\n    await deleteObject(logoRef);\n  } catch (error) {\n    console.error('Error deleting logo:', error);\n    // Don't throw error for delete operations as the file might not exist\n  }\n};\n\n/**\n * Get default business settings\n */\nexport const getDefaultBusinessSettings = (): Omit<BusinessSettings, 'id' | 'createdAt' | 'updatedAt'> => {\n  return {\n    businessName: 'Cyber Services & Stationery',\n    address: 'Your Business Address',\n    phone: '+*********** 000',\n    email: '<EMAIL>',\n    currency: 'KSh',\n    taxRate: 16,\n    receiptFooter: 'Thank you for your business!\\nVisit us again soon',\n    operatingHours: 'Mon-Fri: 8:00 AM - 6:00 PM\\nSat: 9:00 AM - 4:00 PM',\n  };\n};\n\n/**\n * Initialize business settings with defaults if none exist\n */\nexport const initializeBusinessSettings = async (): Promise<BusinessSettings> => {\n  try {\n    const existing = await getBusinessSettings();\n    if (existing) {\n      return existing;\n    }\n    \n    const defaultSettings = getDefaultBusinessSettings();\n    return await saveBusinessSettings(defaultSettings);\n  } catch (error) {\n    console.error('Error initializing business settings:', error);\n    throw new Error('Failed to initialize business settings');\n  }\n};\n"], "mappings": "qHAAA,OACEA,GAAG,CACHC,MAAM,CACNC,MAAM,KAKD,oBAAoB,CAC3B,OACEC,GAAG,CACHC,WAAW,CACXC,cAAc,CACdC,YAAY,KACP,kBAAkB,CACzB,OAASC,EAAE,CAAEC,OAAO,KAAQ,oBAAoB,CAGhD,KAAM,CAAAC,mBAAmB,CAAG,UAAU,CACtC,KAAM,CAAAC,qBAAqB,CAAG,UAAU,CACxC,KAAM,CAAAC,iBAAiB,CAAG,gBAAgB,CAE1C;AACA;AACA,GACA,MAAO,MAAM,CAAAC,mBAAmB,CAAG,KAAAA,CAAA,GAA8C,CAC/E,GAAI,CACF,KAAM,CAAAC,MAAM,CAAGb,GAAG,CAACO,EAAE,CAAEE,mBAAmB,CAAEC,qBAAqB,CAAC,CAClE,KAAM,CAAAI,OAAO,CAAG,KAAM,CAAAb,MAAM,CAACY,MAAM,CAAC,CAEpC,GAAIC,OAAO,CAACC,MAAM,CAAC,CAAC,CAAE,KAAAC,eAAA,CAAAC,eAAA,CACpB,KAAM,CAAAC,IAAI,CAAGJ,OAAO,CAACI,IAAI,CAAC,CAAC,CAC3B,OAAAC,aAAA,CAAAA,aAAA,IACKD,IAAI,MACPE,SAAS,CAAE,EAAAJ,eAAA,CAAAE,IAAI,CAACE,SAAS,UAAAJ,eAAA,iBAAdA,eAAA,CAAgBK,MAAM,CAAC,CAAC,GAAI,GAAI,CAAAC,IAAI,CAAC,CAAC,CACjDC,SAAS,CAAE,EAAAN,eAAA,CAAAC,IAAI,CAACK,SAAS,UAAAN,eAAA,iBAAdA,eAAA,CAAgBI,MAAM,CAAC,CAAC,GAAI,GAAI,CAAAC,IAAI,CAAC,CAAC,GAErD,CAEA,MAAO,KAAI,CACb,CAAE,MAAOE,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,mCAAmC,CAAEA,KAAK,CAAC,CACzD,KAAM,IAAI,CAAAE,KAAK,CAAC,mCAAmC,CAAC,CACtD,CACF,CAAC,CAED;AACA;AACA,GACA,MAAO,MAAM,CAAAC,oBAAoB,CAAG,KAAO,CAAAC,QAAkE,EAAgC,CAC3I,GAAI,KAAAC,iBAAA,CAAAC,qBAAA,CACF,KAAM,CAAAC,GAAG,CAAG,GAAI,CAAAT,IAAI,CAAC,CAAC,CACtB,KAAM,CAAAT,MAAM,CAAGb,GAAG,CAACO,EAAE,CAAEE,mBAAmB,CAAEC,qBAAqB,CAAC,CAElE;AACA,KAAM,CAAAsB,WAAW,CAAG,KAAM,CAAA/B,MAAM,CAACY,MAAM,CAAC,CACxC,KAAM,CAAAoB,QAAQ,CAAGD,WAAW,CAACjB,MAAM,CAAC,CAAC,CAErC,KAAM,CAAAmB,gBAAkC,CAAAf,aAAA,CAAAA,aAAA,EACtCgB,EAAE,CAAEzB,qBAAqB,EACtBkB,QAAQ,MACXR,SAAS,CAAEa,QAAQ,CAAG,EAAAJ,iBAAA,CAAAG,WAAW,CAACd,IAAI,CAAC,CAAC,UAAAW,iBAAA,kBAAAC,qBAAA,CAAlBD,iBAAA,CAAoBT,SAAS,UAAAU,qBAAA,iBAA7BA,qBAAA,CAA+BT,MAAM,CAAC,CAAC,GAAIU,GAAG,CAAGA,GAAG,CAC1ER,SAAS,CAAEQ,GAAG,EACf,CAED,KAAM,CAAA7B,MAAM,CAACW,MAAM,CAAAM,aAAA,CAAAA,aAAA,IACde,gBAAgB,MACnBd,SAAS,CAAEc,gBAAgB,CAACd,SAAS,CACrCG,SAAS,CAAEW,gBAAgB,CAACX,SAAS,EACtC,CAAC,CAEF,MAAO,CAAAW,gBAAgB,CACzB,CAAE,MAAOV,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,iCAAiC,CAAEA,KAAK,CAAC,CACvD,KAAM,IAAI,CAAAE,KAAK,CAAC,kCAAkC,CAAC,CACrD,CACF,CAAC,CAED;AACA;AACA,GACA,MAAO,MAAM,CAAAU,UAAU,CAAG,KAAO,CAAAC,IAAU,EAAiD,CAC1F,GAAI,CACF;AACA,GAAI,CAACA,IAAI,CAACC,IAAI,CAACC,UAAU,CAAC,QAAQ,CAAC,CAAE,CACnC,KAAM,IAAI,CAAAb,KAAK,CAAC,6BAA6B,CAAC,CAChD,CAEA;AACA,KAAM,CAAAc,OAAO,CAAG,CAAC,CAAG,IAAI,CAAG,IAAI,CAAE;AACjC,GAAIH,IAAI,CAACI,IAAI,CAAGD,OAAO,CAAE,CACvB,KAAM,IAAI,CAAAd,KAAK,CAAC,uCAAuC,CAAC,CAC1D,CAEA;AACA,KAAM,CAAAgB,SAAS,CAAGpB,IAAI,CAACS,GAAG,CAAC,CAAC,CAC5B,KAAM,CAAAY,QAAQ,SAAAC,MAAA,CAAWF,SAAS,MAAAE,MAAA,CAAIP,IAAI,CAACQ,IAAI,CAAE,CACjD,KAAM,CAAAC,OAAO,CAAG3C,GAAG,CAACK,OAAO,IAAAoC,MAAA,CAAKjC,iBAAiB,MAAAiC,MAAA,CAAID,QAAQ,CAAE,CAAC,CAEhE;AACA,KAAM,CAAAI,YAAY,CAAG,KAAM,CAAA3C,WAAW,CAAC0C,OAAO,CAAET,IAAI,CAAC,CACrD,KAAM,CAAAW,WAAW,CAAG,KAAM,CAAA3C,cAAc,CAAC0C,YAAY,CAAC5C,GAAG,CAAC,CAE1D,MAAO,CACL8C,GAAG,CAAED,WAAW,CAChBL,QAAQ,CAAEA,QACZ,CAAC,CACH,CAAE,MAAOnB,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,uBAAuB,CAAEA,KAAK,CAAC,CAC7C,KAAM,IAAI,CAAAE,KAAK,CAACF,KAAK,WAAY,CAAAE,KAAK,CAAGF,KAAK,CAAC0B,OAAO,CAAG,uBAAuB,CAAC,CACnF,CACF,CAAC,CAED;AACA;AACA,GACA,MAAO,MAAM,CAAAC,UAAU,CAAG,KAAO,CAAAR,QAAgB,EAAoB,CACnE,GAAI,CACF,KAAM,CAAAG,OAAO,CAAG3C,GAAG,CAACK,OAAO,IAAAoC,MAAA,CAAKjC,iBAAiB,MAAAiC,MAAA,CAAID,QAAQ,CAAE,CAAC,CAChE,KAAM,CAAArC,YAAY,CAACwC,OAAO,CAAC,CAC7B,CAAE,MAAOtB,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,CAAEA,KAAK,CAAC,CAC5C;AACF,CACF,CAAC,CAED;AACA;AACA,GACA,MAAO,MAAM,CAAA4B,0BAA0B,CAAGA,CAAA,GAAgE,CACxG,MAAO,CACLC,YAAY,CAAE,6BAA6B,CAC3CC,OAAO,CAAE,uBAAuB,CAChCC,KAAK,CAAE,kBAAkB,CACzBC,KAAK,CAAE,uBAAuB,CAC9BC,QAAQ,CAAE,KAAK,CACfC,OAAO,CAAE,EAAE,CACXC,aAAa,CAAE,mDAAmD,CAClEC,cAAc,CAAE,oDAClB,CAAC,CACH,CAAC,CAED;AACA;AACA,GACA,MAAO,MAAM,CAAAC,0BAA0B,CAAG,KAAAA,CAAA,GAAuC,CAC/E,GAAI,CACF,KAAM,CAAAC,QAAQ,CAAG,KAAM,CAAAlD,mBAAmB,CAAC,CAAC,CAC5C,GAAIkD,QAAQ,CAAE,CACZ,MAAO,CAAAA,QAAQ,CACjB,CAEA,KAAM,CAAAC,eAAe,CAAGX,0BAA0B,CAAC,CAAC,CACpD,MAAO,MAAM,CAAAzB,oBAAoB,CAACoC,eAAe,CAAC,CACpD,CAAE,MAAOvC,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,uCAAuC,CAAEA,KAAK,CAAC,CAC7D,KAAM,IAAI,CAAAE,KAAK,CAAC,wCAAwC,CAAC,CAC3D,CACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}