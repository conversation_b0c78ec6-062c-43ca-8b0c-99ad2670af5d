{"ast": null, "code": "var _jsxFileName = \"E:\\\\FX\\\\Cyber POS\\\\cyber-pos-system\\\\src\\\\components\\\\settings\\\\Settings.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\nimport { Settings as SettingsIcon, Save, Users, Database, Upload, X, Building2 } from 'lucide-react';\nimport { useAuth } from '../../contexts/AuthContext';\nimport { useBusinessSettings } from '../../contexts/BusinessSettingsContext';\nimport UserManagement from '../users/UserManagement';\nimport { initializeDemoData } from '../../utils/seedData';\nimport { seedTestData } from '../../utils/testDataSeeder';\nimport { getBusinessSettings, saveBusinessSettings, uploadLogo, deleteLogo, getDefaultBusinessSettings } from '../../services/businessSettingsService';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Settings = () => {\n  _s();\n  const {\n    hasPermission,\n    currentUser\n  } = useAuth();\n  const {\n    refreshBusinessSettings\n  } = useBusinessSettings();\n  const [activeTab, setActiveTab] = useState('general');\n  const [loading, setLoading] = useState(false);\n  const [message, setMessage] = useState('');\n\n  // Business settings state\n  const [businessSettings, setBusinessSettings] = useState(null);\n  const [businessFormData, setBusinessFormData] = useState(getDefaultBusinessSettings());\n  const [logoFile, setLogoFile] = useState(null);\n  const [logoPreview, setLogoPreview] = useState(null);\n  const [savingSettings, setSavingSettings] = useState(false);\n  const fileInputRef = useRef(null);\n  const tabs = [{\n    id: 'general',\n    name: 'Business Settings',\n    icon: Building2\n  }, ...(hasPermission('admin') ? [{\n    id: 'users',\n    name: 'User Management',\n    icon: Users\n  }] : []), {\n    id: 'data',\n    name: 'Data Management',\n    icon: Database\n  }];\n\n  // Load business settings on component mount\n  useEffect(() => {\n    loadBusinessSettings();\n  }, []);\n  const loadBusinessSettings = async () => {\n    try {\n      const settings = await getBusinessSettings();\n      if (settings) {\n        setBusinessSettings(settings);\n        setBusinessFormData({\n          businessName: settings.businessName,\n          address: settings.address,\n          phone: settings.phone,\n          email: settings.email || '',\n          taxNumber: settings.taxNumber || '',\n          licenseNumber: settings.licenseNumber || '',\n          currency: settings.currency,\n          taxRate: settings.taxRate,\n          logoUrl: settings.logoUrl,\n          logoFileName: settings.logoFileName,\n          receiptHeader: settings.receiptHeader || '',\n          receiptFooter: settings.receiptFooter,\n          operatingHours: settings.operatingHours || '',\n          website: settings.website || ''\n        });\n        if (settings.logoUrl) {\n          setLogoPreview(settings.logoUrl);\n        }\n      }\n    } catch (error) {\n      console.error('Error loading business settings:', error);\n      setMessage('Error loading business settings');\n    }\n  };\n  const handleInitializeDemo = async () => {\n    setLoading(true);\n    setMessage('');\n    try {\n      await initializeDemoData();\n      setMessage('Demo data initialized successfully!');\n    } catch (error) {\n      setMessage(`Error: ${error.message}`);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleSeedTestData = async () => {\n    if (!currentUser) {\n      setMessage('Error: User not authenticated');\n      return;\n    }\n    setLoading(true);\n    setMessage('');\n    try {\n      await seedTestData(currentUser.id);\n      setMessage('Test data with transactions seeded successfully! You can now view reports with sample data.');\n    } catch (error) {\n      setMessage(`Error: ${error.message}`);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Business settings handlers\n  const handleBusinessFormChange = (field, value) => {\n    setBusinessFormData(prev => ({\n      ...prev,\n      [field]: value\n    }));\n  };\n  const handleLogoSelect = event => {\n    var _event$target$files;\n    const file = (_event$target$files = event.target.files) === null || _event$target$files === void 0 ? void 0 : _event$target$files[0];\n    if (file) {\n      setLogoFile(file);\n\n      // Create preview\n      const reader = new FileReader();\n      reader.onload = e => {\n        var _e$target;\n        setLogoPreview((_e$target = e.target) === null || _e$target === void 0 ? void 0 : _e$target.result);\n      };\n      reader.readAsDataURL(file);\n    }\n  };\n  const handleRemoveLogo = () => {\n    setLogoFile(null);\n    setLogoPreview(null);\n    setBusinessFormData(prev => ({\n      ...prev,\n      logoUrl: undefined,\n      logoFileName: undefined\n    }));\n    if (fileInputRef.current) {\n      fileInputRef.current.value = '';\n    }\n  };\n  const handleSaveBusinessSettings = async () => {\n    if (!hasPermission('admin')) {\n      setMessage('Error: Admin permission required');\n      return;\n    }\n    setSavingSettings(true);\n    setMessage('');\n    try {\n      let logoUrl = businessFormData.logoUrl;\n      let logoFileName = businessFormData.logoFileName;\n\n      // Upload new logo if selected\n      if (logoFile) {\n        // Delete old logo if exists\n        if (businessFormData.logoFileName) {\n          await deleteLogo(businessFormData.logoFileName);\n        }\n        const uploadResult = await uploadLogo(logoFile);\n        logoUrl = uploadResult.url;\n        logoFileName = uploadResult.fileName;\n      }\n      const settingsToSave = {\n        ...businessFormData,\n        logoUrl,\n        logoFileName\n      };\n      const savedSettings = await saveBusinessSettings(settingsToSave);\n      setBusinessSettings(savedSettings);\n      setLogoFile(null);\n\n      // Refresh the business settings context to update the UI\n      await refreshBusinessSettings();\n      setMessage('Business settings saved successfully!');\n    } catch (error) {\n      setMessage(`Error: ${error.message}`);\n    } finally {\n      setSavingSettings(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white shadow rounded-lg\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"px-6 py-4 border-b border-gray-200\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(SettingsIcon, {\n            className: \"h-6 w-6 text-primary-600 mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 194,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"text-2xl font-bold text-gray-900\",\n            children: \"System Settings\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 195,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 193,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 192,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"border-b border-gray-200\",\n        children: /*#__PURE__*/_jsxDEV(\"nav\", {\n          className: \"-mb-px flex space-x-8 px-6\",\n          children: tabs.map(tab => /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setActiveTab(tab.id),\n            className: `${activeTab === tab.id ? 'border-primary-500 text-primary-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'} whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm flex items-center`,\n            children: [/*#__PURE__*/_jsxDEV(tab.icon, {\n              className: \"h-4 w-4 mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 212,\n              columnNumber: 17\n            }, this), tab.name]\n          }, tab.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 203,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 201,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 200,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-6\",\n        children: [activeTab === 'general' && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-6\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-medium text-gray-900 mb-4\",\n              children: \"Business Settings\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 224,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-8 p-6 bg-gray-50 rounded-lg\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                className: \"text-md font-medium text-gray-900 mb-4\",\n                children: \"Company Logo\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 228,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-start space-x-6\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-shrink-0\",\n                  children: logoPreview ? /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"relative\",\n                    children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                      src: logoPreview,\n                      alt: \"Logo preview\",\n                      className: \"w-32 h-32 object-contain border border-gray-300 rounded-lg bg-white\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 233,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                      onClick: handleRemoveLogo,\n                      className: \"absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600\",\n                      children: /*#__PURE__*/_jsxDEV(X, {\n                        className: \"h-4 w-4\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 242,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 238,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 232,\n                    columnNumber: 25\n                  }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-32 h-32 border-2 border-dashed border-gray-300 rounded-lg flex items-center justify-center bg-white\",\n                    children: /*#__PURE__*/_jsxDEV(Upload, {\n                      className: \"h-8 w-8 text-gray-400\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 247,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 246,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 230,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-1\",\n                  children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                    ref: fileInputRef,\n                    type: \"file\",\n                    accept: \"image/*\",\n                    onChange: handleLogoSelect,\n                    className: \"hidden\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 252,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => {\n                      var _fileInputRef$current;\n                      return (_fileInputRef$current = fileInputRef.current) === null || _fileInputRef$current === void 0 ? void 0 : _fileInputRef$current.click();\n                    },\n                    className: \"bg-white border border-gray-300 rounded-md px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50\",\n                    children: logoPreview ? 'Change Logo' : 'Upload Logo'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 259,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"mt-2 text-sm text-gray-500\",\n                    children: \"Upload your company logo. Recommended size: 200x200px. Max file size: 5MB. Supported formats: JPG, PNG, GIF.\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 265,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 251,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 229,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 227,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-1 gap-6 sm:grid-cols-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700\",\n                  children: \"Business Name *\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 276,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: businessFormData.businessName,\n                  onChange: e => handleBusinessFormChange('businessName', e.target.value),\n                  className: \"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-primary-500 focus:border-primary-500\",\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 277,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 275,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700\",\n                  children: \"Phone Number *\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 286,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"tel\",\n                  value: businessFormData.phone,\n                  onChange: e => handleBusinessFormChange('phone', e.target.value),\n                  className: \"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-primary-500 focus:border-primary-500\",\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 287,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 285,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"sm:col-span-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700\",\n                  children: \"Business Address *\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 296,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                  value: businessFormData.address,\n                  onChange: e => handleBusinessFormChange('address', e.target.value),\n                  rows: 3,\n                  className: \"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-primary-500 focus:border-primary-500\",\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 297,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 295,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700\",\n                  children: \"Email Address\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 306,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"email\",\n                  value: businessFormData.email,\n                  onChange: e => handleBusinessFormChange('email', e.target.value),\n                  className: \"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-primary-500 focus:border-primary-500\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 307,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 305,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700\",\n                  children: \"Website\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 315,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"url\",\n                  value: businessFormData.website,\n                  onChange: e => handleBusinessFormChange('website', e.target.value),\n                  className: \"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-primary-500 focus:border-primary-500\",\n                  placeholder: \"https://www.yourbusiness.com\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 316,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 314,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 274,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-8\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                className: \"text-md font-medium text-gray-900 mb-4\",\n                children: \"Tax & Legal Information\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 328,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"grid grid-cols-1 gap-6 sm:grid-cols-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-sm font-medium text-gray-700\",\n                    children: \"Tax Registration Number (PIN)\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 331,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    value: businessFormData.taxNumber,\n                    onChange: e => handleBusinessFormChange('taxNumber', e.target.value),\n                    className: \"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-primary-500 focus:border-primary-500\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 332,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 330,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-sm font-medium text-gray-700\",\n                    children: \"Business License Number\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 340,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    value: businessFormData.licenseNumber,\n                    onChange: e => handleBusinessFormChange('licenseNumber', e.target.value),\n                    className: \"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-primary-500 focus:border-primary-500\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 341,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 339,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-sm font-medium text-gray-700\",\n                    children: \"Currency\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 349,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                    value: businessFormData.currency,\n                    onChange: e => handleBusinessFormChange('currency', e.target.value),\n                    className: \"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-primary-500 focus:border-primary-500\",\n                    children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"KSh\",\n                      children: \"KSh (Kenyan Shilling)\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 355,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"USD\",\n                      children: \"USD (US Dollar)\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 356,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"EUR\",\n                      children: \"EUR (Euro)\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 357,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"GBP\",\n                      children: \"GBP (British Pound)\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 358,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 350,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 348,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-sm font-medium text-gray-700\",\n                    children: \"Tax Rate (%)\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 362,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"number\",\n                    value: businessFormData.taxRate,\n                    onChange: e => handleBusinessFormChange('taxRate', parseFloat(e.target.value) || 0),\n                    step: \"0.01\",\n                    min: \"0\",\n                    max: \"100\",\n                    className: \"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-primary-500 focus:border-primary-500\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 363,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 361,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 329,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 327,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-8\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                className: \"text-md font-medium text-gray-900 mb-4\",\n                children: \"Receipt Configuration\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 378,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"grid grid-cols-1 gap-6\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-sm font-medium text-gray-700\",\n                    children: \"Receipt Header (Optional)\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 381,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    value: businessFormData.receiptHeader,\n                    onChange: e => handleBusinessFormChange('receiptHeader', e.target.value),\n                    className: \"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-primary-500 focus:border-primary-500\",\n                    placeholder: \"Special message for receipt header\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 382,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 380,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-sm font-medium text-gray-700\",\n                    children: \"Receipt Footer\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 391,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                    value: businessFormData.receiptFooter,\n                    onChange: e => handleBusinessFormChange('receiptFooter', e.target.value),\n                    rows: 3,\n                    className: \"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-primary-500 focus:border-primary-500\",\n                    placeholder: \"Thank you message and additional information\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 392,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 390,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-sm font-medium text-gray-700\",\n                    children: \"Operating Hours\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 401,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                    value: businessFormData.operatingHours,\n                    onChange: e => handleBusinessFormChange('operatingHours', e.target.value),\n                    rows: 2,\n                    className: \"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-primary-500 focus:border-primary-500\",\n                    placeholder: \"Mon-Fri: 8:00 AM - 6:00 PM\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 402,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 400,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 379,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 377,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-8 pt-6 border-t border-gray-200\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-sm text-gray-500\",\n                  children: \"* Required fields\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 416,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: handleSaveBusinessSettings,\n                  disabled: savingSettings || !hasPermission('admin'),\n                  className: \"bg-primary-600 text-white px-6 py-2 rounded-lg hover:bg-primary-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(Save, {\n                    className: \"h-4 w-4 mr-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 424,\n                    columnNumber: 23\n                  }, this), savingSettings ? 'Saving...' : 'Save Business Settings']\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 419,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 415,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 414,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 223,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 222,\n          columnNumber: 13\n        }, this), activeTab === 'users' && hasPermission('admin') && /*#__PURE__*/_jsxDEV(UserManagement, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 434,\n          columnNumber: 13\n        }, this), activeTab === 'data' && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-6\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-medium text-gray-900 mb-4\",\n              children: \"Data Management\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 440,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-yellow-50 border border-yellow-200 rounded-md p-4 mb-6\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-shrink-0\",\n                  children: /*#__PURE__*/_jsxDEV(Database, {\n                    className: \"h-5 w-5 text-yellow-400\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 444,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 443,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"ml-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                    className: \"text-sm font-medium text-yellow-800\",\n                    children: \"Demo Data Initialization\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 447,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"mt-2 text-sm text-yellow-700\",\n                    children: /*#__PURE__*/_jsxDEV(\"p\", {\n                      children: \"Initialize the system with demo services and products for testing purposes. This will add sample cyber services and stationery items to your database.\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 451,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 450,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 446,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 442,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 441,\n              columnNumber: 17\n            }, this), message && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: `p-4 rounded-md mb-4 ${message.includes('Error') ? 'bg-red-50 text-red-700 border border-red-200' : 'bg-green-50 text-green-700 border border-green-200'}`,\n              children: message\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 461,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: handleInitializeDemo,\n                disabled: loading,\n                className: \"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 disabled:opacity-50 mr-4\",\n                children: [/*#__PURE__*/_jsxDEV(Database, {\n                  className: \"h-4 w-4 mr-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 476,\n                  columnNumber: 21\n                }, this), loading ? 'Initializing...' : 'Initialize Demo Data']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 471,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: handleSeedTestData,\n                disabled: loading,\n                className: \"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 disabled:opacity-50\",\n                children: [/*#__PURE__*/_jsxDEV(Database, {\n                  className: \"h-4 w-4 mr-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 485,\n                  columnNumber: 21\n                }, this), loading ? 'Seeding...' : 'Seed Test Data with Transactions']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 480,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-sm text-gray-600 mt-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"Initialize Demo Data:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 490,\n                    columnNumber: 24\n                  }, this), \" Adds basic services and products\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 490,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"Seed Test Data:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 491,\n                    columnNumber: 24\n                  }, this), \" Adds comprehensive test data including 30 days of sample transactions for testing reports\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 491,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 489,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 470,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 439,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 438,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 220,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 191,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 189,\n    columnNumber: 5\n  }, this);\n};\n_s(Settings, \"bDuhEMujf3A5Q4U4/vM++/tyU2w=\", false, function () {\n  return [useAuth, useBusinessSettings];\n});\n_c = Settings;\nexport default Settings;\nvar _c;\n$RefreshReg$(_c, \"Settings\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "Settings", "SettingsIcon", "Save", "Users", "Database", "Upload", "X", "Building2", "useAuth", "useBusinessSettings", "UserManagement", "initializeDemoData", "seedTestData", "getBusinessSettings", "saveBusinessSettings", "uploadLogo", "deleteLogo", "getDefaultBusinessSettings", "jsxDEV", "_jsxDEV", "_s", "hasPermission", "currentUser", "refreshBusinessSettings", "activeTab", "setActiveTab", "loading", "setLoading", "message", "setMessage", "businessSettings", "setBusinessSettings", "businessFormData", "setBusinessFormData", "logoFile", "setLogoFile", "logoPreview", "setLogoPreview", "savingSettings", "setSavingSettings", "fileInputRef", "tabs", "id", "name", "icon", "loadBusinessSettings", "settings", "businessName", "address", "phone", "email", "taxNumber", "licenseNumber", "currency", "taxRate", "logoUrl", "logoFileName", "receiptHeader", "receiptFooter", "operatingHours", "website", "error", "console", "handleInitializeDemo", "handleSeedTestData", "handleBusinessFormChange", "field", "value", "prev", "handleLogoSelect", "event", "_event$target$files", "file", "target", "files", "reader", "FileReader", "onload", "e", "_e$target", "result", "readAsDataURL", "handleRemoveLogo", "undefined", "current", "handleSaveBusinessSettings", "uploadResult", "url", "fileName", "settingsToSave", "savedSettings", "className", "children", "_jsxFileName", "lineNumber", "columnNumber", "map", "tab", "onClick", "src", "alt", "ref", "type", "accept", "onChange", "_fileInputRef$current", "click", "required", "rows", "placeholder", "parseFloat", "step", "min", "max", "disabled", "includes", "_c", "$RefreshReg$"], "sources": ["E:/FX/Cyber POS/cyber-pos-system/src/components/settings/Settings.tsx"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\nimport { Settings as SettingsIcon, Save, Users, Database, Upload, X, Building2 } from 'lucide-react';\nimport { useAuth } from '../../contexts/AuthContext';\nimport { useBusinessSettings } from '../../contexts/BusinessSettingsContext';\nimport UserManagement from '../users/UserManagement';\nimport { initializeDemoData } from '../../utils/seedData';\nimport { seedTestData } from '../../utils/testDataSeeder';\nimport { BusinessSettings } from '../../types';\nimport {\n  getBusinessSettings,\n  saveBusinessSettings,\n  uploadLogo,\n  deleteLogo,\n  getDefaultBusinessSettings\n} from '../../services/businessSettingsService';\n\nconst Settings: React.FC = () => {\n  const { hasPermission, currentUser } = useAuth();\n  const { refreshBusinessSettings } = useBusinessSettings();\n  const [activeTab, setActiveTab] = useState('general');\n  const [loading, setLoading] = useState(false);\n  const [message, setMessage] = useState('');\n\n  // Business settings state\n  const [businessSettings, setBusinessSettings] = useState<BusinessSettings | null>(null);\n  const [businessFormData, setBusinessFormData] = useState(getDefaultBusinessSettings());\n  const [logoFile, setLogoFile] = useState<File | null>(null);\n  const [logoPreview, setLogoPreview] = useState<string | null>(null);\n  const [savingSettings, setSavingSettings] = useState(false);\n  const fileInputRef = useRef<HTMLInputElement>(null);\n\n  const tabs = [\n    { id: 'general', name: 'Business Settings', icon: Building2 },\n    ...(hasPermission('admin') ? [{ id: 'users', name: 'User Management', icon: Users }] : []),\n    { id: 'data', name: 'Data Management', icon: Database },\n  ];\n\n  // Load business settings on component mount\n  useEffect(() => {\n    loadBusinessSettings();\n  }, []);\n\n  const loadBusinessSettings = async () => {\n    try {\n      const settings = await getBusinessSettings();\n      if (settings) {\n        setBusinessSettings(settings);\n        setBusinessFormData({\n          businessName: settings.businessName,\n          address: settings.address,\n          phone: settings.phone,\n          email: settings.email || '',\n          taxNumber: settings.taxNumber || '',\n          licenseNumber: settings.licenseNumber || '',\n          currency: settings.currency,\n          taxRate: settings.taxRate,\n          logoUrl: settings.logoUrl,\n          logoFileName: settings.logoFileName,\n          receiptHeader: settings.receiptHeader || '',\n          receiptFooter: settings.receiptFooter,\n          operatingHours: settings.operatingHours || '',\n          website: settings.website || '',\n        });\n        if (settings.logoUrl) {\n          setLogoPreview(settings.logoUrl);\n        }\n      }\n    } catch (error) {\n      console.error('Error loading business settings:', error);\n      setMessage('Error loading business settings');\n    }\n  };\n\n  const handleInitializeDemo = async () => {\n    setLoading(true);\n    setMessage('');\n\n    try {\n      await initializeDemoData();\n      setMessage('Demo data initialized successfully!');\n    } catch (error: any) {\n      setMessage(`Error: ${error.message}`);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleSeedTestData = async () => {\n    if (!currentUser) {\n      setMessage('Error: User not authenticated');\n      return;\n    }\n\n    setLoading(true);\n    setMessage('');\n\n    try {\n      await seedTestData(currentUser.id);\n      setMessage('Test data with transactions seeded successfully! You can now view reports with sample data.');\n    } catch (error: any) {\n      setMessage(`Error: ${error.message}`);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Business settings handlers\n  const handleBusinessFormChange = (field: string, value: string | number) => {\n    setBusinessFormData(prev => ({\n      ...prev,\n      [field]: value\n    }));\n  };\n\n  const handleLogoSelect = (event: React.ChangeEvent<HTMLInputElement>) => {\n    const file = event.target.files?.[0];\n    if (file) {\n      setLogoFile(file);\n\n      // Create preview\n      const reader = new FileReader();\n      reader.onload = (e) => {\n        setLogoPreview(e.target?.result as string);\n      };\n      reader.readAsDataURL(file);\n    }\n  };\n\n  const handleRemoveLogo = () => {\n    setLogoFile(null);\n    setLogoPreview(null);\n    setBusinessFormData(prev => ({\n      ...prev,\n      logoUrl: undefined,\n      logoFileName: undefined\n    }));\n    if (fileInputRef.current) {\n      fileInputRef.current.value = '';\n    }\n  };\n\n  const handleSaveBusinessSettings = async () => {\n    if (!hasPermission('admin')) {\n      setMessage('Error: Admin permission required');\n      return;\n    }\n\n    setSavingSettings(true);\n    setMessage('');\n\n    try {\n      let logoUrl = businessFormData.logoUrl;\n      let logoFileName = businessFormData.logoFileName;\n\n      // Upload new logo if selected\n      if (logoFile) {\n        // Delete old logo if exists\n        if (businessFormData.logoFileName) {\n          await deleteLogo(businessFormData.logoFileName);\n        }\n\n        const uploadResult = await uploadLogo(logoFile);\n        logoUrl = uploadResult.url;\n        logoFileName = uploadResult.fileName;\n      }\n\n      const settingsToSave = {\n        ...businessFormData,\n        logoUrl,\n        logoFileName,\n      };\n\n      const savedSettings = await saveBusinessSettings(settingsToSave);\n      setBusinessSettings(savedSettings);\n      setLogoFile(null);\n\n      // Refresh the business settings context to update the UI\n      await refreshBusinessSettings();\n\n      setMessage('Business settings saved successfully!');\n    } catch (error: any) {\n      setMessage(`Error: ${error.message}`);\n    } finally {\n      setSavingSettings(false);\n    }\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"bg-white shadow rounded-lg\">\n        <div className=\"px-6 py-4 border-b border-gray-200\">\n          <div className=\"flex items-center\">\n            <SettingsIcon className=\"h-6 w-6 text-primary-600 mr-2\" />\n            <h1 className=\"text-2xl font-bold text-gray-900\">System Settings</h1>\n          </div>\n        </div>\n\n        {/* Tabs */}\n        <div className=\"border-b border-gray-200\">\n          <nav className=\"-mb-px flex space-x-8 px-6\">\n            {tabs.map((tab) => (\n              <button\n                key={tab.id}\n                onClick={() => setActiveTab(tab.id)}\n                className={`${\n                  activeTab === tab.id\n                    ? 'border-primary-500 text-primary-600'\n                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n                } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm flex items-center`}\n              >\n                <tab.icon className=\"h-4 w-4 mr-2\" />\n                {tab.name}\n              </button>\n            ))}\n          </nav>\n        </div>\n\n        {/* Tab Content */}\n        <div className=\"p-6\">\n          {activeTab === 'general' && (\n            <div className=\"space-y-6\">\n              <div>\n                <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Business Settings</h3>\n\n                {/* Logo Upload Section */}\n                <div className=\"mb-8 p-6 bg-gray-50 rounded-lg\">\n                  <h4 className=\"text-md font-medium text-gray-900 mb-4\">Company Logo</h4>\n                  <div className=\"flex items-start space-x-6\">\n                    <div className=\"flex-shrink-0\">\n                      {logoPreview ? (\n                        <div className=\"relative\">\n                          <img\n                            src={logoPreview}\n                            alt=\"Logo preview\"\n                            className=\"w-32 h-32 object-contain border border-gray-300 rounded-lg bg-white\"\n                          />\n                          <button\n                            onClick={handleRemoveLogo}\n                            className=\"absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600\"\n                          >\n                            <X className=\"h-4 w-4\" />\n                          </button>\n                        </div>\n                      ) : (\n                        <div className=\"w-32 h-32 border-2 border-dashed border-gray-300 rounded-lg flex items-center justify-center bg-white\">\n                          <Upload className=\"h-8 w-8 text-gray-400\" />\n                        </div>\n                      )}\n                    </div>\n                    <div className=\"flex-1\">\n                      <input\n                        ref={fileInputRef}\n                        type=\"file\"\n                        accept=\"image/*\"\n                        onChange={handleLogoSelect}\n                        className=\"hidden\"\n                      />\n                      <button\n                        onClick={() => fileInputRef.current?.click()}\n                        className=\"bg-white border border-gray-300 rounded-md px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50\"\n                      >\n                        {logoPreview ? 'Change Logo' : 'Upload Logo'}\n                      </button>\n                      <p className=\"mt-2 text-sm text-gray-500\">\n                        Upload your company logo. Recommended size: 200x200px. Max file size: 5MB.\n                        Supported formats: JPG, PNG, GIF.\n                      </p>\n                    </div>\n                  </div>\n                </div>\n\n                {/* Business Information Form */}\n                <div className=\"grid grid-cols-1 gap-6 sm:grid-cols-2\">\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700\">Business Name *</label>\n                    <input\n                      type=\"text\"\n                      value={businessFormData.businessName}\n                      onChange={(e) => handleBusinessFormChange('businessName', e.target.value)}\n                      className=\"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-primary-500 focus:border-primary-500\"\n                      required\n                    />\n                  </div>\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700\">Phone Number *</label>\n                    <input\n                      type=\"tel\"\n                      value={businessFormData.phone}\n                      onChange={(e) => handleBusinessFormChange('phone', e.target.value)}\n                      className=\"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-primary-500 focus:border-primary-500\"\n                      required\n                    />\n                  </div>\n                  <div className=\"sm:col-span-2\">\n                    <label className=\"block text-sm font-medium text-gray-700\">Business Address *</label>\n                    <textarea\n                      value={businessFormData.address}\n                      onChange={(e) => handleBusinessFormChange('address', e.target.value)}\n                      rows={3}\n                      className=\"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-primary-500 focus:border-primary-500\"\n                      required\n                    />\n                  </div>\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700\">Email Address</label>\n                    <input\n                      type=\"email\"\n                      value={businessFormData.email}\n                      onChange={(e) => handleBusinessFormChange('email', e.target.value)}\n                      className=\"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-primary-500 focus:border-primary-500\"\n                    />\n                  </div>\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700\">Website</label>\n                    <input\n                      type=\"url\"\n                      value={businessFormData.website}\n                      onChange={(e) => handleBusinessFormChange('website', e.target.value)}\n                      className=\"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-primary-500 focus:border-primary-500\"\n                      placeholder=\"https://www.yourbusiness.com\"\n                    />\n                  </div>\n                </div>\n\n                {/* Tax and Legal Information */}\n                <div className=\"mt-8\">\n                  <h4 className=\"text-md font-medium text-gray-900 mb-4\">Tax & Legal Information</h4>\n                  <div className=\"grid grid-cols-1 gap-6 sm:grid-cols-2\">\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700\">Tax Registration Number (PIN)</label>\n                      <input\n                        type=\"text\"\n                        value={businessFormData.taxNumber}\n                        onChange={(e) => handleBusinessFormChange('taxNumber', e.target.value)}\n                        className=\"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-primary-500 focus:border-primary-500\"\n                      />\n                    </div>\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700\">Business License Number</label>\n                      <input\n                        type=\"text\"\n                        value={businessFormData.licenseNumber}\n                        onChange={(e) => handleBusinessFormChange('licenseNumber', e.target.value)}\n                        className=\"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-primary-500 focus:border-primary-500\"\n                      />\n                    </div>\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700\">Currency</label>\n                      <select\n                        value={businessFormData.currency}\n                        onChange={(e) => handleBusinessFormChange('currency', e.target.value)}\n                        className=\"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-primary-500 focus:border-primary-500\"\n                      >\n                        <option value=\"KSh\">KSh (Kenyan Shilling)</option>\n                        <option value=\"USD\">USD (US Dollar)</option>\n                        <option value=\"EUR\">EUR (Euro)</option>\n                        <option value=\"GBP\">GBP (British Pound)</option>\n                      </select>\n                    </div>\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700\">Tax Rate (%)</label>\n                      <input\n                        type=\"number\"\n                        value={businessFormData.taxRate}\n                        onChange={(e) => handleBusinessFormChange('taxRate', parseFloat(e.target.value) || 0)}\n                        step=\"0.01\"\n                        min=\"0\"\n                        max=\"100\"\n                        className=\"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-primary-500 focus:border-primary-500\"\n                      />\n                    </div>\n                  </div>\n                </div>\n\n                {/* Receipt Configuration */}\n                <div className=\"mt-8\">\n                  <h4 className=\"text-md font-medium text-gray-900 mb-4\">Receipt Configuration</h4>\n                  <div className=\"grid grid-cols-1 gap-6\">\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700\">Receipt Header (Optional)</label>\n                      <input\n                        type=\"text\"\n                        value={businessFormData.receiptHeader}\n                        onChange={(e) => handleBusinessFormChange('receiptHeader', e.target.value)}\n                        className=\"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-primary-500 focus:border-primary-500\"\n                        placeholder=\"Special message for receipt header\"\n                      />\n                    </div>\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700\">Receipt Footer</label>\n                      <textarea\n                        value={businessFormData.receiptFooter}\n                        onChange={(e) => handleBusinessFormChange('receiptFooter', e.target.value)}\n                        rows={3}\n                        className=\"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-primary-500 focus:border-primary-500\"\n                        placeholder=\"Thank you message and additional information\"\n                      />\n                    </div>\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700\">Operating Hours</label>\n                      <textarea\n                        value={businessFormData.operatingHours}\n                        onChange={(e) => handleBusinessFormChange('operatingHours', e.target.value)}\n                        rows={2}\n                        className=\"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-primary-500 focus:border-primary-500\"\n                        placeholder=\"Mon-Fri: 8:00 AM - 6:00 PM\"\n                      />\n                    </div>\n                  </div>\n                </div>\n\n                {/* Save Button */}\n                <div className=\"mt-8 pt-6 border-t border-gray-200\">\n                  <div className=\"flex items-center justify-between\">\n                    <div className=\"text-sm text-gray-500\">\n                      * Required fields\n                    </div>\n                    <button\n                      onClick={handleSaveBusinessSettings}\n                      disabled={savingSettings || !hasPermission('admin')}\n                      className=\"bg-primary-600 text-white px-6 py-2 rounded-lg hover:bg-primary-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center\"\n                    >\n                      <Save className=\"h-4 w-4 mr-2\" />\n                      {savingSettings ? 'Saving...' : 'Save Business Settings'}\n                    </button>\n                  </div>\n                </div>\n              </div>\n            </div>\n          )}\n\n          {activeTab === 'users' && hasPermission('admin') && (\n            <UserManagement />\n          )}\n\n          {activeTab === 'data' && (\n            <div className=\"space-y-6\">\n              <div>\n                <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Data Management</h3>\n                <div className=\"bg-yellow-50 border border-yellow-200 rounded-md p-4 mb-6\">\n                  <div className=\"flex\">\n                    <div className=\"flex-shrink-0\">\n                      <Database className=\"h-5 w-5 text-yellow-400\" />\n                    </div>\n                    <div className=\"ml-3\">\n                      <h3 className=\"text-sm font-medium text-yellow-800\">\n                        Demo Data Initialization\n                      </h3>\n                      <div className=\"mt-2 text-sm text-yellow-700\">\n                        <p>\n                          Initialize the system with demo services and products for testing purposes.\n                          This will add sample cyber services and stationery items to your database.\n                        </p>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n\n                {message && (\n                  <div className={`p-4 rounded-md mb-4 ${\n                    message.includes('Error')\n                      ? 'bg-red-50 text-red-700 border border-red-200'\n                      : 'bg-green-50 text-green-700 border border-green-200'\n                  }`}>\n                    {message}\n                  </div>\n                )}\n\n                <div className=\"space-y-4\">\n                  <button\n                    onClick={handleInitializeDemo}\n                    disabled={loading}\n                    className=\"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 disabled:opacity-50 mr-4\"\n                  >\n                    <Database className=\"h-4 w-4 mr-2\" />\n                    {loading ? 'Initializing...' : 'Initialize Demo Data'}\n                  </button>\n\n                  <button\n                    onClick={handleSeedTestData}\n                    disabled={loading}\n                    className=\"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 disabled:opacity-50\"\n                  >\n                    <Database className=\"h-4 w-4 mr-2\" />\n                    {loading ? 'Seeding...' : 'Seed Test Data with Transactions'}\n                  </button>\n\n                  <div className=\"text-sm text-gray-600 mt-2\">\n                    <p><strong>Initialize Demo Data:</strong> Adds basic services and products</p>\n                    <p><strong>Seed Test Data:</strong> Adds comprehensive test data including 30 days of sample transactions for testing reports</p>\n                  </div>\n                </div>\n              </div>\n            </div>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Settings;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SAASC,QAAQ,IAAIC,YAAY,EAAEC,IAAI,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,CAAC,EAAEC,SAAS,QAAQ,cAAc;AACpG,SAASC,OAAO,QAAQ,4BAA4B;AACpD,SAASC,mBAAmB,QAAQ,wCAAwC;AAC5E,OAAOC,cAAc,MAAM,yBAAyB;AACpD,SAASC,kBAAkB,QAAQ,sBAAsB;AACzD,SAASC,YAAY,QAAQ,4BAA4B;AAEzD,SACEC,mBAAmB,EACnBC,oBAAoB,EACpBC,UAAU,EACVC,UAAU,EACVC,0BAA0B,QACrB,wCAAwC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhD,MAAMnB,QAAkB,GAAGA,CAAA,KAAM;EAAAoB,EAAA;EAC/B,MAAM;IAAEC,aAAa;IAAEC;EAAY,CAAC,GAAGd,OAAO,CAAC,CAAC;EAChD,MAAM;IAAEe;EAAwB,CAAC,GAAGd,mBAAmB,CAAC,CAAC;EACzD,MAAM,CAACe,SAAS,EAAEC,YAAY,CAAC,GAAG5B,QAAQ,CAAC,SAAS,CAAC;EACrD,MAAM,CAAC6B,OAAO,EAAEC,UAAU,CAAC,GAAG9B,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC+B,OAAO,EAAEC,UAAU,CAAC,GAAGhC,QAAQ,CAAC,EAAE,CAAC;;EAE1C;EACA,MAAM,CAACiC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGlC,QAAQ,CAA0B,IAAI,CAAC;EACvF,MAAM,CAACmC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGpC,QAAQ,CAACoB,0BAA0B,CAAC,CAAC,CAAC;EACtF,MAAM,CAACiB,QAAQ,EAAEC,WAAW,CAAC,GAAGtC,QAAQ,CAAc,IAAI,CAAC;EAC3D,MAAM,CAACuC,WAAW,EAAEC,cAAc,CAAC,GAAGxC,QAAQ,CAAgB,IAAI,CAAC;EACnE,MAAM,CAACyC,cAAc,EAAEC,iBAAiB,CAAC,GAAG1C,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM2C,YAAY,GAAGzC,MAAM,CAAmB,IAAI,CAAC;EAEnD,MAAM0C,IAAI,GAAG,CACX;IAAEC,EAAE,EAAE,SAAS;IAAEC,IAAI,EAAE,mBAAmB;IAAEC,IAAI,EAAErC;EAAU,CAAC,EAC7D,IAAIc,aAAa,CAAC,OAAO,CAAC,GAAG,CAAC;IAAEqB,EAAE,EAAE,OAAO;IAAEC,IAAI,EAAE,iBAAiB;IAAEC,IAAI,EAAEzC;EAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAC1F;IAAEuC,EAAE,EAAE,MAAM;IAAEC,IAAI,EAAE,iBAAiB;IAAEC,IAAI,EAAExC;EAAS,CAAC,CACxD;;EAED;EACAN,SAAS,CAAC,MAAM;IACd+C,oBAAoB,CAAC,CAAC;EACxB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACvC,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMjC,mBAAmB,CAAC,CAAC;MAC5C,IAAIiC,QAAQ,EAAE;QACZf,mBAAmB,CAACe,QAAQ,CAAC;QAC7Bb,mBAAmB,CAAC;UAClBc,YAAY,EAAED,QAAQ,CAACC,YAAY;UACnCC,OAAO,EAAEF,QAAQ,CAACE,OAAO;UACzBC,KAAK,EAAEH,QAAQ,CAACG,KAAK;UACrBC,KAAK,EAAEJ,QAAQ,CAACI,KAAK,IAAI,EAAE;UAC3BC,SAAS,EAAEL,QAAQ,CAACK,SAAS,IAAI,EAAE;UACnCC,aAAa,EAAEN,QAAQ,CAACM,aAAa,IAAI,EAAE;UAC3CC,QAAQ,EAAEP,QAAQ,CAACO,QAAQ;UAC3BC,OAAO,EAAER,QAAQ,CAACQ,OAAO;UACzBC,OAAO,EAAET,QAAQ,CAACS,OAAO;UACzBC,YAAY,EAAEV,QAAQ,CAACU,YAAY;UACnCC,aAAa,EAAEX,QAAQ,CAACW,aAAa,IAAI,EAAE;UAC3CC,aAAa,EAAEZ,QAAQ,CAACY,aAAa;UACrCC,cAAc,EAAEb,QAAQ,CAACa,cAAc,IAAI,EAAE;UAC7CC,OAAO,EAAEd,QAAQ,CAACc,OAAO,IAAI;QAC/B,CAAC,CAAC;QACF,IAAId,QAAQ,CAACS,OAAO,EAAE;UACpBlB,cAAc,CAACS,QAAQ,CAACS,OAAO,CAAC;QAClC;MACF;IACF,CAAC,CAAC,OAAOM,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;MACxDhC,UAAU,CAAC,iCAAiC,CAAC;IAC/C;EACF,CAAC;EAED,MAAMkC,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACvCpC,UAAU,CAAC,IAAI,CAAC;IAChBE,UAAU,CAAC,EAAE,CAAC;IAEd,IAAI;MACF,MAAMlB,kBAAkB,CAAC,CAAC;MAC1BkB,UAAU,CAAC,qCAAqC,CAAC;IACnD,CAAC,CAAC,OAAOgC,KAAU,EAAE;MACnBhC,UAAU,CAAC,UAAUgC,KAAK,CAACjC,OAAO,EAAE,CAAC;IACvC,CAAC,SAAS;MACRD,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMqC,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrC,IAAI,CAAC1C,WAAW,EAAE;MAChBO,UAAU,CAAC,+BAA+B,CAAC;MAC3C;IACF;IAEAF,UAAU,CAAC,IAAI,CAAC;IAChBE,UAAU,CAAC,EAAE,CAAC;IAEd,IAAI;MACF,MAAMjB,YAAY,CAACU,WAAW,CAACoB,EAAE,CAAC;MAClCb,UAAU,CAAC,6FAA6F,CAAC;IAC3G,CAAC,CAAC,OAAOgC,KAAU,EAAE;MACnBhC,UAAU,CAAC,UAAUgC,KAAK,CAACjC,OAAO,EAAE,CAAC;IACvC,CAAC,SAAS;MACRD,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMsC,wBAAwB,GAAGA,CAACC,KAAa,EAAEC,KAAsB,KAAK;IAC1ElC,mBAAmB,CAACmC,IAAI,KAAK;MAC3B,GAAGA,IAAI;MACP,CAACF,KAAK,GAAGC;IACX,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAME,gBAAgB,GAAIC,KAA0C,IAAK;IAAA,IAAAC,mBAAA;IACvE,MAAMC,IAAI,IAAAD,mBAAA,GAAGD,KAAK,CAACG,MAAM,CAACC,KAAK,cAAAH,mBAAA,uBAAlBA,mBAAA,CAAqB,CAAC,CAAC;IACpC,IAAIC,IAAI,EAAE;MACRrC,WAAW,CAACqC,IAAI,CAAC;;MAEjB;MACA,MAAMG,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;MAC/BD,MAAM,CAACE,MAAM,GAAIC,CAAC,IAAK;QAAA,IAAAC,SAAA;QACrB1C,cAAc,EAAA0C,SAAA,GAACD,CAAC,CAACL,MAAM,cAAAM,SAAA,uBAARA,SAAA,CAAUC,MAAgB,CAAC;MAC5C,CAAC;MACDL,MAAM,CAACM,aAAa,CAACT,IAAI,CAAC;IAC5B;EACF,CAAC;EAED,MAAMU,gBAAgB,GAAGA,CAAA,KAAM;IAC7B/C,WAAW,CAAC,IAAI,CAAC;IACjBE,cAAc,CAAC,IAAI,CAAC;IACpBJ,mBAAmB,CAACmC,IAAI,KAAK;MAC3B,GAAGA,IAAI;MACPb,OAAO,EAAE4B,SAAS;MAClB3B,YAAY,EAAE2B;IAChB,CAAC,CAAC,CAAC;IACH,IAAI3C,YAAY,CAAC4C,OAAO,EAAE;MACxB5C,YAAY,CAAC4C,OAAO,CAACjB,KAAK,GAAG,EAAE;IACjC;EACF,CAAC;EAED,MAAMkB,0BAA0B,GAAG,MAAAA,CAAA,KAAY;IAC7C,IAAI,CAAChE,aAAa,CAAC,OAAO,CAAC,EAAE;MAC3BQ,UAAU,CAAC,kCAAkC,CAAC;MAC9C;IACF;IAEAU,iBAAiB,CAAC,IAAI,CAAC;IACvBV,UAAU,CAAC,EAAE,CAAC;IAEd,IAAI;MACF,IAAI0B,OAAO,GAAGvB,gBAAgB,CAACuB,OAAO;MACtC,IAAIC,YAAY,GAAGxB,gBAAgB,CAACwB,YAAY;;MAEhD;MACA,IAAItB,QAAQ,EAAE;QACZ;QACA,IAAIF,gBAAgB,CAACwB,YAAY,EAAE;UACjC,MAAMxC,UAAU,CAACgB,gBAAgB,CAACwB,YAAY,CAAC;QACjD;QAEA,MAAM8B,YAAY,GAAG,MAAMvE,UAAU,CAACmB,QAAQ,CAAC;QAC/CqB,OAAO,GAAG+B,YAAY,CAACC,GAAG;QAC1B/B,YAAY,GAAG8B,YAAY,CAACE,QAAQ;MACtC;MAEA,MAAMC,cAAc,GAAG;QACrB,GAAGzD,gBAAgB;QACnBuB,OAAO;QACPC;MACF,CAAC;MAED,MAAMkC,aAAa,GAAG,MAAM5E,oBAAoB,CAAC2E,cAAc,CAAC;MAChE1D,mBAAmB,CAAC2D,aAAa,CAAC;MAClCvD,WAAW,CAAC,IAAI,CAAC;;MAEjB;MACA,MAAMZ,uBAAuB,CAAC,CAAC;MAE/BM,UAAU,CAAC,uCAAuC,CAAC;IACrD,CAAC,CAAC,OAAOgC,KAAU,EAAE;MACnBhC,UAAU,CAAC,UAAUgC,KAAK,CAACjC,OAAO,EAAE,CAAC;IACvC,CAAC,SAAS;MACRW,iBAAiB,CAAC,KAAK,CAAC;IAC1B;EACF,CAAC;EAED,oBACEpB,OAAA;IAAKwE,SAAS,EAAC,WAAW;IAAAC,QAAA,eAExBzE,OAAA;MAAKwE,SAAS,EAAC,4BAA4B;MAAAC,QAAA,gBACzCzE,OAAA;QAAKwE,SAAS,EAAC,oCAAoC;QAAAC,QAAA,eACjDzE,OAAA;UAAKwE,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChCzE,OAAA,CAAClB,YAAY;YAAC0F,SAAS,EAAC;UAA+B;YAAAH,QAAA,EAAAK,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC1D5E,OAAA;YAAIwE,SAAS,EAAC,kCAAkC;YAAAC,QAAA,EAAC;UAAe;YAAAJ,QAAA,EAAAK,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAP,QAAA,EAAAK,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClE;MAAC;QAAAP,QAAA,EAAAK,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGN5E,OAAA;QAAKwE,SAAS,EAAC,0BAA0B;QAAAC,QAAA,eACvCzE,OAAA;UAAKwE,SAAS,EAAC,4BAA4B;UAAAC,QAAA,EACxCnD,IAAI,CAACuD,GAAG,CAAEC,GAAG,iBACZ9E,OAAA;YAEE+E,OAAO,EAAEA,CAAA,KAAMzE,YAAY,CAACwE,GAAG,CAACvD,EAAE,CAAE;YACpCiD,SAAS,EAAE,GACTnE,SAAS,KAAKyE,GAAG,CAACvD,EAAE,GAChB,qCAAqC,GACrC,4EAA4E,+EACF;YAAAkD,QAAA,gBAEhFzE,OAAA,CAAC8E,GAAG,CAACrD,IAAI;cAAC+C,SAAS,EAAC;YAAc;cAAAH,QAAA,EAAAK,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,EACpCE,GAAG,CAACtD,IAAI;UAAA,GATJsD,GAAG,CAACvD,EAAE;YAAA8C,QAAA,EAAAK,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAUL,CACT;QAAC;UAAAP,QAAA,EAAAK,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC;QAAAP,QAAA,EAAAK,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGN5E,OAAA;QAAKwE,SAAS,EAAC,KAAK;QAAAC,QAAA,GACjBpE,SAAS,KAAK,SAAS,iBACtBL,OAAA;UAAKwE,SAAS,EAAC,WAAW;UAAAC,QAAA,eACxBzE,OAAA;YAAAyE,QAAA,gBACEzE,OAAA;cAAIwE,SAAS,EAAC,wCAAwC;cAAAC,QAAA,EAAC;YAAiB;cAAAJ,QAAA,EAAAK,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAG7E5E,OAAA;cAAKwE,SAAS,EAAC,gCAAgC;cAAAC,QAAA,gBAC7CzE,OAAA;gBAAIwE,SAAS,EAAC,wCAAwC;gBAAAC,QAAA,EAAC;cAAY;gBAAAJ,QAAA,EAAAK,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACxE5E,OAAA;gBAAKwE,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,gBACzCzE,OAAA;kBAAKwE,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAC3BxD,WAAW,gBACVjB,OAAA;oBAAKwE,SAAS,EAAC,UAAU;oBAAAC,QAAA,gBACvBzE,OAAA;sBACEgF,GAAG,EAAE/D,WAAY;sBACjBgE,GAAG,EAAC,cAAc;sBAClBT,SAAS,EAAC;oBAAqE;sBAAAH,QAAA,EAAAK,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAChF,CAAC,eACF5E,OAAA;sBACE+E,OAAO,EAAEhB,gBAAiB;sBAC1BS,SAAS,EAAC,kFAAkF;sBAAAC,QAAA,eAE5FzE,OAAA,CAACb,CAAC;wBAACqF,SAAS,EAAC;sBAAS;wBAAAH,QAAA,EAAAK,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAP,QAAA,EAAAK,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACnB,CAAC;kBAAA;oBAAAP,QAAA,EAAAK,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC,gBAEN5E,OAAA;oBAAKwE,SAAS,EAAC,uGAAuG;oBAAAC,QAAA,eACpHzE,OAAA,CAACd,MAAM;sBAACsF,SAAS,EAAC;oBAAuB;sBAAAH,QAAA,EAAAK,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAP,QAAA,EAAAK,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzC;gBACN;kBAAAP,QAAA,EAAAK,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,eACN5E,OAAA;kBAAKwE,SAAS,EAAC,QAAQ;kBAAAC,QAAA,gBACrBzE,OAAA;oBACEkF,GAAG,EAAE7D,YAAa;oBAClB8D,IAAI,EAAC,MAAM;oBACXC,MAAM,EAAC,SAAS;oBAChBC,QAAQ,EAAEnC,gBAAiB;oBAC3BsB,SAAS,EAAC;kBAAQ;oBAAAH,QAAA,EAAAK,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnB,CAAC,eACF5E,OAAA;oBACE+E,OAAO,EAAEA,CAAA;sBAAA,IAAAO,qBAAA;sBAAA,QAAAA,qBAAA,GAAMjE,YAAY,CAAC4C,OAAO,cAAAqB,qBAAA,uBAApBA,qBAAA,CAAsBC,KAAK,CAAC,CAAC;oBAAA,CAAC;oBAC7Cf,SAAS,EAAC,yGAAyG;oBAAAC,QAAA,EAElHxD,WAAW,GAAG,aAAa,GAAG;kBAAa;oBAAAoD,QAAA,EAAAK,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtC,CAAC,eACT5E,OAAA;oBAAGwE,SAAS,EAAC,4BAA4B;oBAAAC,QAAA,EAAC;kBAG1C;oBAAAJ,QAAA,EAAAK,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAP,QAAA,EAAAK,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC;cAAA;gBAAAP,QAAA,EAAAK,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAP,QAAA,EAAAK,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGN5E,OAAA;cAAKwE,SAAS,EAAC,uCAAuC;cAAAC,QAAA,gBACpDzE,OAAA;gBAAAyE,QAAA,gBACEzE,OAAA;kBAAOwE,SAAS,EAAC,yCAAyC;kBAAAC,QAAA,EAAC;gBAAe;kBAAAJ,QAAA,EAAAK,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAClF5E,OAAA;kBACEmF,IAAI,EAAC,MAAM;kBACXnC,KAAK,EAAEnC,gBAAgB,CAACe,YAAa;kBACrCyD,QAAQ,EAAG1B,CAAC,IAAKb,wBAAwB,CAAC,cAAc,EAAEa,CAAC,CAACL,MAAM,CAACN,KAAK,CAAE;kBAC1EwB,SAAS,EAAC,kIAAkI;kBAC5IgB,QAAQ;gBAAA;kBAAAnB,QAAA,EAAAK,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA;gBAAAP,QAAA,EAAAK,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACN5E,OAAA;gBAAAyE,QAAA,gBACEzE,OAAA;kBAAOwE,SAAS,EAAC,yCAAyC;kBAAAC,QAAA,EAAC;gBAAc;kBAAAJ,QAAA,EAAAK,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACjF5E,OAAA;kBACEmF,IAAI,EAAC,KAAK;kBACVnC,KAAK,EAAEnC,gBAAgB,CAACiB,KAAM;kBAC9BuD,QAAQ,EAAG1B,CAAC,IAAKb,wBAAwB,CAAC,OAAO,EAAEa,CAAC,CAACL,MAAM,CAACN,KAAK,CAAE;kBACnEwB,SAAS,EAAC,kIAAkI;kBAC5IgB,QAAQ;gBAAA;kBAAAnB,QAAA,EAAAK,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA;gBAAAP,QAAA,EAAAK,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACN5E,OAAA;gBAAKwE,SAAS,EAAC,eAAe;gBAAAC,QAAA,gBAC5BzE,OAAA;kBAAOwE,SAAS,EAAC,yCAAyC;kBAAAC,QAAA,EAAC;gBAAkB;kBAAAJ,QAAA,EAAAK,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACrF5E,OAAA;kBACEgD,KAAK,EAAEnC,gBAAgB,CAACgB,OAAQ;kBAChCwD,QAAQ,EAAG1B,CAAC,IAAKb,wBAAwB,CAAC,SAAS,EAAEa,CAAC,CAACL,MAAM,CAACN,KAAK,CAAE;kBACrEyC,IAAI,EAAE,CAAE;kBACRjB,SAAS,EAAC,kIAAkI;kBAC5IgB,QAAQ;gBAAA;kBAAAnB,QAAA,EAAAK,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA;gBAAAP,QAAA,EAAAK,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACN5E,OAAA;gBAAAyE,QAAA,gBACEzE,OAAA;kBAAOwE,SAAS,EAAC,yCAAyC;kBAAAC,QAAA,EAAC;gBAAa;kBAAAJ,QAAA,EAAAK,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAChF5E,OAAA;kBACEmF,IAAI,EAAC,OAAO;kBACZnC,KAAK,EAAEnC,gBAAgB,CAACkB,KAAM;kBAC9BsD,QAAQ,EAAG1B,CAAC,IAAKb,wBAAwB,CAAC,OAAO,EAAEa,CAAC,CAACL,MAAM,CAACN,KAAK,CAAE;kBACnEwB,SAAS,EAAC;gBAAkI;kBAAAH,QAAA,EAAAK,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7I,CAAC;cAAA;gBAAAP,QAAA,EAAAK,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACN5E,OAAA;gBAAAyE,QAAA,gBACEzE,OAAA;kBAAOwE,SAAS,EAAC,yCAAyC;kBAAAC,QAAA,EAAC;gBAAO;kBAAAJ,QAAA,EAAAK,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC1E5E,OAAA;kBACEmF,IAAI,EAAC,KAAK;kBACVnC,KAAK,EAAEnC,gBAAgB,CAAC4B,OAAQ;kBAChC4C,QAAQ,EAAG1B,CAAC,IAAKb,wBAAwB,CAAC,SAAS,EAAEa,CAAC,CAACL,MAAM,CAACN,KAAK,CAAE;kBACrEwB,SAAS,EAAC,kIAAkI;kBAC5IkB,WAAW,EAAC;gBAA8B;kBAAArB,QAAA,EAAAK,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3C,CAAC;cAAA;gBAAAP,QAAA,EAAAK,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAP,QAAA,EAAAK,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGN5E,OAAA;cAAKwE,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnBzE,OAAA;gBAAIwE,SAAS,EAAC,wCAAwC;gBAAAC,QAAA,EAAC;cAAuB;gBAAAJ,QAAA,EAAAK,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACnF5E,OAAA;gBAAKwE,SAAS,EAAC,uCAAuC;gBAAAC,QAAA,gBACpDzE,OAAA;kBAAAyE,QAAA,gBACEzE,OAAA;oBAAOwE,SAAS,EAAC,yCAAyC;oBAAAC,QAAA,EAAC;kBAA6B;oBAAAJ,QAAA,EAAAK,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAChG5E,OAAA;oBACEmF,IAAI,EAAC,MAAM;oBACXnC,KAAK,EAAEnC,gBAAgB,CAACmB,SAAU;oBAClCqD,QAAQ,EAAG1B,CAAC,IAAKb,wBAAwB,CAAC,WAAW,EAAEa,CAAC,CAACL,MAAM,CAACN,KAAK,CAAE;oBACvEwB,SAAS,EAAC;kBAAkI;oBAAAH,QAAA,EAAAK,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7I,CAAC;gBAAA;kBAAAP,QAAA,EAAAK,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACN5E,OAAA;kBAAAyE,QAAA,gBACEzE,OAAA;oBAAOwE,SAAS,EAAC,yCAAyC;oBAAAC,QAAA,EAAC;kBAAuB;oBAAAJ,QAAA,EAAAK,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC1F5E,OAAA;oBACEmF,IAAI,EAAC,MAAM;oBACXnC,KAAK,EAAEnC,gBAAgB,CAACoB,aAAc;oBACtCoD,QAAQ,EAAG1B,CAAC,IAAKb,wBAAwB,CAAC,eAAe,EAAEa,CAAC,CAACL,MAAM,CAACN,KAAK,CAAE;oBAC3EwB,SAAS,EAAC;kBAAkI;oBAAAH,QAAA,EAAAK,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7I,CAAC;gBAAA;kBAAAP,QAAA,EAAAK,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACN5E,OAAA;kBAAAyE,QAAA,gBACEzE,OAAA;oBAAOwE,SAAS,EAAC,yCAAyC;oBAAAC,QAAA,EAAC;kBAAQ;oBAAAJ,QAAA,EAAAK,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC3E5E,OAAA;oBACEgD,KAAK,EAAEnC,gBAAgB,CAACqB,QAAS;oBACjCmD,QAAQ,EAAG1B,CAAC,IAAKb,wBAAwB,CAAC,UAAU,EAAEa,CAAC,CAACL,MAAM,CAACN,KAAK,CAAE;oBACtEwB,SAAS,EAAC,kIAAkI;oBAAAC,QAAA,gBAE5IzE,OAAA;sBAAQgD,KAAK,EAAC,KAAK;sBAAAyB,QAAA,EAAC;oBAAqB;sBAAAJ,QAAA,EAAAK,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eAClD5E,OAAA;sBAAQgD,KAAK,EAAC,KAAK;sBAAAyB,QAAA,EAAC;oBAAe;sBAAAJ,QAAA,EAAAK,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eAC5C5E,OAAA;sBAAQgD,KAAK,EAAC,KAAK;sBAAAyB,QAAA,EAAC;oBAAU;sBAAAJ,QAAA,EAAAK,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACvC5E,OAAA;sBAAQgD,KAAK,EAAC,KAAK;sBAAAyB,QAAA,EAAC;oBAAmB;sBAAAJ,QAAA,EAAAK,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAP,QAAA,EAAAK,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1C,CAAC;gBAAA;kBAAAP,QAAA,EAAAK,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC,eACN5E,OAAA;kBAAAyE,QAAA,gBACEzE,OAAA;oBAAOwE,SAAS,EAAC,yCAAyC;oBAAAC,QAAA,EAAC;kBAAY;oBAAAJ,QAAA,EAAAK,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC/E5E,OAAA;oBACEmF,IAAI,EAAC,QAAQ;oBACbnC,KAAK,EAAEnC,gBAAgB,CAACsB,OAAQ;oBAChCkD,QAAQ,EAAG1B,CAAC,IAAKb,wBAAwB,CAAC,SAAS,EAAE6C,UAAU,CAAChC,CAAC,CAACL,MAAM,CAACN,KAAK,CAAC,IAAI,CAAC,CAAE;oBACtF4C,IAAI,EAAC,MAAM;oBACXC,GAAG,EAAC,GAAG;oBACPC,GAAG,EAAC,KAAK;oBACTtB,SAAS,EAAC;kBAAkI;oBAAAH,QAAA,EAAAK,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7I,CAAC;gBAAA;kBAAAP,QAAA,EAAAK,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAP,QAAA,EAAAK,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAP,QAAA,EAAAK,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGN5E,OAAA;cAAKwE,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnBzE,OAAA;gBAAIwE,SAAS,EAAC,wCAAwC;gBAAAC,QAAA,EAAC;cAAqB;gBAAAJ,QAAA,EAAAK,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACjF5E,OAAA;gBAAKwE,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,gBACrCzE,OAAA;kBAAAyE,QAAA,gBACEzE,OAAA;oBAAOwE,SAAS,EAAC,yCAAyC;oBAAAC,QAAA,EAAC;kBAAyB;oBAAAJ,QAAA,EAAAK,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC5F5E,OAAA;oBACEmF,IAAI,EAAC,MAAM;oBACXnC,KAAK,EAAEnC,gBAAgB,CAACyB,aAAc;oBACtC+C,QAAQ,EAAG1B,CAAC,IAAKb,wBAAwB,CAAC,eAAe,EAAEa,CAAC,CAACL,MAAM,CAACN,KAAK,CAAE;oBAC3EwB,SAAS,EAAC,kIAAkI;oBAC5IkB,WAAW,EAAC;kBAAoC;oBAAArB,QAAA,EAAAK,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjD,CAAC;gBAAA;kBAAAP,QAAA,EAAAK,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACN5E,OAAA;kBAAAyE,QAAA,gBACEzE,OAAA;oBAAOwE,SAAS,EAAC,yCAAyC;oBAAAC,QAAA,EAAC;kBAAc;oBAAAJ,QAAA,EAAAK,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACjF5E,OAAA;oBACEgD,KAAK,EAAEnC,gBAAgB,CAAC0B,aAAc;oBACtC8C,QAAQ,EAAG1B,CAAC,IAAKb,wBAAwB,CAAC,eAAe,EAAEa,CAAC,CAACL,MAAM,CAACN,KAAK,CAAE;oBAC3EyC,IAAI,EAAE,CAAE;oBACRjB,SAAS,EAAC,kIAAkI;oBAC5IkB,WAAW,EAAC;kBAA8C;oBAAArB,QAAA,EAAAK,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3D,CAAC;gBAAA;kBAAAP,QAAA,EAAAK,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACN5E,OAAA;kBAAAyE,QAAA,gBACEzE,OAAA;oBAAOwE,SAAS,EAAC,yCAAyC;oBAAAC,QAAA,EAAC;kBAAe;oBAAAJ,QAAA,EAAAK,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAClF5E,OAAA;oBACEgD,KAAK,EAAEnC,gBAAgB,CAAC2B,cAAe;oBACvC6C,QAAQ,EAAG1B,CAAC,IAAKb,wBAAwB,CAAC,gBAAgB,EAAEa,CAAC,CAACL,MAAM,CAACN,KAAK,CAAE;oBAC5EyC,IAAI,EAAE,CAAE;oBACRjB,SAAS,EAAC,kIAAkI;oBAC5IkB,WAAW,EAAC;kBAA4B;oBAAArB,QAAA,EAAAK,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzC,CAAC;gBAAA;kBAAAP,QAAA,EAAAK,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAP,QAAA,EAAAK,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAP,QAAA,EAAAK,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGN5E,OAAA;cAAKwE,SAAS,EAAC,oCAAoC;cAAAC,QAAA,eACjDzE,OAAA;gBAAKwE,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,gBAChDzE,OAAA;kBAAKwE,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAC;gBAEvC;kBAAAJ,QAAA,EAAAK,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACN5E,OAAA;kBACE+E,OAAO,EAAEb,0BAA2B;kBACpC6B,QAAQ,EAAE5E,cAAc,IAAI,CAACjB,aAAa,CAAC,OAAO,CAAE;kBACpDsE,SAAS,EAAC,uIAAuI;kBAAAC,QAAA,gBAEjJzE,OAAA,CAACjB,IAAI;oBAACyF,SAAS,EAAC;kBAAc;oBAAAH,QAAA,EAAAK,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,EAChCzD,cAAc,GAAG,WAAW,GAAG,wBAAwB;gBAAA;kBAAAkD,QAAA,EAAAK,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClD,CAAC;cAAA;gBAAAP,QAAA,EAAAK,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN;YAAC;cAAAP,QAAA,EAAAK,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAP,QAAA,EAAAK,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAP,QAAA,EAAAK,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,EAEAvE,SAAS,KAAK,OAAO,IAAIH,aAAa,CAAC,OAAO,CAAC,iBAC9CF,OAAA,CAACT,cAAc;UAAA8E,QAAA,EAAAK,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAClB,EAEAvE,SAAS,KAAK,MAAM,iBACnBL,OAAA;UAAKwE,SAAS,EAAC,WAAW;UAAAC,QAAA,eACxBzE,OAAA;YAAAyE,QAAA,gBACEzE,OAAA;cAAIwE,SAAS,EAAC,wCAAwC;cAAAC,QAAA,EAAC;YAAe;cAAAJ,QAAA,EAAAK,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC3E5E,OAAA;cAAKwE,SAAS,EAAC,2DAA2D;cAAAC,QAAA,eACxEzE,OAAA;gBAAKwE,SAAS,EAAC,MAAM;gBAAAC,QAAA,gBACnBzE,OAAA;kBAAKwE,SAAS,EAAC,eAAe;kBAAAC,QAAA,eAC5BzE,OAAA,CAACf,QAAQ;oBAACuF,SAAS,EAAC;kBAAyB;oBAAAH,QAAA,EAAAK,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAP,QAAA,EAAAK,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7C,CAAC,eACN5E,OAAA;kBAAKwE,SAAS,EAAC,MAAM;kBAAAC,QAAA,gBACnBzE,OAAA;oBAAIwE,SAAS,EAAC,qCAAqC;oBAAAC,QAAA,EAAC;kBAEpD;oBAAAJ,QAAA,EAAAK,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACL5E,OAAA;oBAAKwE,SAAS,EAAC,8BAA8B;oBAAAC,QAAA,eAC3CzE,OAAA;sBAAAyE,QAAA,EAAG;oBAGH;sBAAAJ,QAAA,EAAAK,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG;kBAAC;oBAAAP,QAAA,EAAAK,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD,CAAC;gBAAA;kBAAAP,QAAA,EAAAK,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAP,QAAA,EAAAK,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAP,QAAA,EAAAK,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,EAELnE,OAAO,iBACNT,OAAA;cAAKwE,SAAS,EAAE,uBACd/D,OAAO,CAACuF,QAAQ,CAAC,OAAO,CAAC,GACrB,8CAA8C,GAC9C,oDAAoD,EACvD;cAAAvB,QAAA,EACAhE;YAAO;cAAA4D,QAAA,EAAAK,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CACN,eAED5E,OAAA;cAAKwE,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxBzE,OAAA;gBACE+E,OAAO,EAAEnC,oBAAqB;gBAC9BmD,QAAQ,EAAExF,OAAQ;gBAClBiE,SAAS,EAAC,+KAA+K;gBAAAC,QAAA,gBAEzLzE,OAAA,CAACf,QAAQ;kBAACuF,SAAS,EAAC;gBAAc;kBAAAH,QAAA,EAAAK,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,EACpCrE,OAAO,GAAG,iBAAiB,GAAG,sBAAsB;cAAA;gBAAA8D,QAAA,EAAAK,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/C,CAAC,eAET5E,OAAA;gBACE+E,OAAO,EAAElC,kBAAmB;gBAC5BkD,QAAQ,EAAExF,OAAQ;gBAClBiE,SAAS,EAAC,sKAAsK;gBAAAC,QAAA,gBAEhLzE,OAAA,CAACf,QAAQ;kBAACuF,SAAS,EAAC;gBAAc;kBAAAH,QAAA,EAAAK,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,EACpCrE,OAAO,GAAG,YAAY,GAAG,kCAAkC;cAAA;gBAAA8D,QAAA,EAAAK,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtD,CAAC,eAET5E,OAAA;gBAAKwE,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,gBACzCzE,OAAA;kBAAAyE,QAAA,gBAAGzE,OAAA;oBAAAyE,QAAA,EAAQ;kBAAqB;oBAAAJ,QAAA,EAAAK,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,qCAAiC;gBAAA;kBAAAP,QAAA,EAAAK,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eAC9E5E,OAAA;kBAAAyE,QAAA,gBAAGzE,OAAA;oBAAAyE,QAAA,EAAQ;kBAAe;oBAAAJ,QAAA,EAAAK,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,8FAA0F;gBAAA;kBAAAP,QAAA,EAAAK,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAP,QAAA,EAAAK,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9H,CAAC;YAAA;cAAAP,QAAA,EAAAK,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAP,QAAA,EAAAK,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAP,QAAA,EAAAK,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN;MAAA;QAAAP,QAAA,EAAAK,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAP,QAAA,EAAAK,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAP,QAAA,EAAAK,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC3E,EAAA,CApeIpB,QAAkB;EAAA,QACiBQ,OAAO,EACVC,mBAAmB;AAAA;AAAA2G,EAAA,GAFnDpH,QAAkB;AAsexB,eAAeA,QAAQ;AAAC,IAAAoH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}