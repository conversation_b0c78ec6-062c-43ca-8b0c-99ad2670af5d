{"ast": null, "code": "import{collection,getDocs,query,limit}from'firebase/firestore';import{db}from'../config/firebase';/**\n * Test Firebase Firestore connection and basic operations\n */export const testFirebaseConnection=async()=>{try{console.log('🔍 Testing Firebase connection...');// Test 1: Basic connection by trying to read from a collection\nconst testQuery=query(collection(db,'services'),limit(1));const snapshot=await getDocs(testQuery);console.log('✅ Firebase connection successful');console.log(\"\\uD83D\\uDCCA Found \".concat(snapshot.size,\" documents in services collection\"));return{success:true,message:\"Firebase connection successful. Found \".concat(snapshot.size,\" services.\"),details:{documentsFound:snapshot.size,connectionTime:new Date().toISOString()}};}catch(error){console.error('❌ Firebase connection failed:',error);let message='Firebase connection failed';if(error.code==='failed-precondition'&&error.message.includes('index')){message='Database index is being created. Please wait a few minutes.';}else if(error.code==='permission-denied'){message='Permission denied. Please check Firebase security rules.';}else if(error.code==='unavailable'){message='Firebase service is temporarily unavailable.';}else{message=\"Firebase error: \".concat(error.message);}return{success:false,message,details:{errorCode:error.code,errorMessage:error.message,timestamp:new Date().toISOString()}};}};/**\n * Test services query specifically\n */export const testServicesQuery=async()=>{try{console.log('🔍 Testing services query...');// Test the exact query that was failing\nconst servicesQuery=query(collection(db,'services'),// Note: We'll test without orderBy first to see if the collection exists\nlimit(10));const snapshot=await getDocs(servicesQuery);console.log(\"\\u2705 Services query successful - found \".concat(snapshot.size,\" services\"));return{success:true,message:\"Services query successful. Found \".concat(snapshot.size,\" services.\"),servicesCount:snapshot.size};}catch(error){console.error('❌ Services query failed:',error);return{success:false,message:\"Services query failed: \".concat(error.message),servicesCount:0};}};", "map": {"version": 3, "names": ["collection", "getDocs", "query", "limit", "db", "testFirebaseConnection", "console", "log", "testQuery", "snapshot", "concat", "size", "success", "message", "details", "documentsFound", "connectionTime", "Date", "toISOString", "error", "code", "includes", "errorCode", "errorMessage", "timestamp", "testServicesQuery", "servicesQuery", "servicesCount"], "sources": ["E:/FX/Cyber POS/cyber-pos-system/src/utils/firebaseConnectionTest.ts"], "sourcesContent": ["import { collection, getDocs, query, limit } from 'firebase/firestore';\nimport { db } from '../config/firebase';\n\n/**\n * Test Firebase Firestore connection and basic operations\n */\nexport const testFirebaseConnection = async (): Promise<{\n  success: boolean;\n  message: string;\n  details?: any;\n}> => {\n  try {\n    console.log('🔍 Testing Firebase connection...');\n    \n    // Test 1: Basic connection by trying to read from a collection\n    const testQuery = query(collection(db, 'services'), limit(1));\n    const snapshot = await getDocs(testQuery);\n    \n    console.log('✅ Firebase connection successful');\n    console.log(`📊 Found ${snapshot.size} documents in services collection`);\n    \n    return {\n      success: true,\n      message: `Firebase connection successful. Found ${snapshot.size} services.`,\n      details: {\n        documentsFound: snapshot.size,\n        connectionTime: new Date().toISOString()\n      }\n    };\n  } catch (error: any) {\n    console.error('❌ Firebase connection failed:', error);\n    \n    let message = 'Firebase connection failed';\n    if (error.code === 'failed-precondition' && error.message.includes('index')) {\n      message = 'Database index is being created. Please wait a few minutes.';\n    } else if (error.code === 'permission-denied') {\n      message = 'Permission denied. Please check Firebase security rules.';\n    } else if (error.code === 'unavailable') {\n      message = 'Firebase service is temporarily unavailable.';\n    } else {\n      message = `Firebase error: ${error.message}`;\n    }\n    \n    return {\n      success: false,\n      message,\n      details: {\n        errorCode: error.code,\n        errorMessage: error.message,\n        timestamp: new Date().toISOString()\n      }\n    };\n  }\n};\n\n/**\n * Test services query specifically\n */\nexport const testServicesQuery = async (): Promise<{\n  success: boolean;\n  message: string;\n  servicesCount?: number;\n}> => {\n  try {\n    console.log('🔍 Testing services query...');\n    \n    // Test the exact query that was failing\n    const servicesQuery = query(\n      collection(db, 'services'),\n      // Note: We'll test without orderBy first to see if the collection exists\n      limit(10)\n    );\n    \n    const snapshot = await getDocs(servicesQuery);\n    \n    console.log(`✅ Services query successful - found ${snapshot.size} services`);\n    \n    return {\n      success: true,\n      message: `Services query successful. Found ${snapshot.size} services.`,\n      servicesCount: snapshot.size\n    };\n  } catch (error: any) {\n    console.error('❌ Services query failed:', error);\n    \n    return {\n      success: false,\n      message: `Services query failed: ${error.message}`,\n      servicesCount: 0\n    };\n  }\n};\n"], "mappings": "AAAA,OAASA,UAAU,CAAEC,OAAO,CAAEC,KAAK,CAAEC,KAAK,KAAQ,oBAAoB,CACtE,OAASC,EAAE,KAAQ,oBAAoB,CAEvC;AACA;AACA,GACA,MAAO,MAAM,CAAAC,sBAAsB,CAAG,KAAAA,CAAA,GAIhC,CACJ,GAAI,CACFC,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC,CAEhD;AACA,KAAM,CAAAC,SAAS,CAAGN,KAAK,CAACF,UAAU,CAACI,EAAE,CAAE,UAAU,CAAC,CAAED,KAAK,CAAC,CAAC,CAAC,CAAC,CAC7D,KAAM,CAAAM,QAAQ,CAAG,KAAM,CAAAR,OAAO,CAACO,SAAS,CAAC,CAEzCF,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC,CAC/CD,OAAO,CAACC,GAAG,uBAAAG,MAAA,CAAaD,QAAQ,CAACE,IAAI,qCAAmC,CAAC,CAEzE,MAAO,CACLC,OAAO,CAAE,IAAI,CACbC,OAAO,0CAAAH,MAAA,CAA2CD,QAAQ,CAACE,IAAI,cAAY,CAC3EG,OAAO,CAAE,CACPC,cAAc,CAAEN,QAAQ,CAACE,IAAI,CAC7BK,cAAc,CAAE,GAAI,CAAAC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CACzC,CACF,CAAC,CACH,CAAE,MAAOC,KAAU,CAAE,CACnBb,OAAO,CAACa,KAAK,CAAC,+BAA+B,CAAEA,KAAK,CAAC,CAErD,GAAI,CAAAN,OAAO,CAAG,4BAA4B,CAC1C,GAAIM,KAAK,CAACC,IAAI,GAAK,qBAAqB,EAAID,KAAK,CAACN,OAAO,CAACQ,QAAQ,CAAC,OAAO,CAAC,CAAE,CAC3ER,OAAO,CAAG,6DAA6D,CACzE,CAAC,IAAM,IAAIM,KAAK,CAACC,IAAI,GAAK,mBAAmB,CAAE,CAC7CP,OAAO,CAAG,0DAA0D,CACtE,CAAC,IAAM,IAAIM,KAAK,CAACC,IAAI,GAAK,aAAa,CAAE,CACvCP,OAAO,CAAG,8CAA8C,CAC1D,CAAC,IAAM,CACLA,OAAO,oBAAAH,MAAA,CAAsBS,KAAK,CAACN,OAAO,CAAE,CAC9C,CAEA,MAAO,CACLD,OAAO,CAAE,KAAK,CACdC,OAAO,CACPC,OAAO,CAAE,CACPQ,SAAS,CAAEH,KAAK,CAACC,IAAI,CACrBG,YAAY,CAAEJ,KAAK,CAACN,OAAO,CAC3BW,SAAS,CAAE,GAAI,CAAAP,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CACpC,CACF,CAAC,CACH,CACF,CAAC,CAED;AACA;AACA,GACA,MAAO,MAAM,CAAAO,iBAAiB,CAAG,KAAAA,CAAA,GAI3B,CACJ,GAAI,CACFnB,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC,CAE3C;AACA,KAAM,CAAAmB,aAAa,CAAGxB,KAAK,CACzBF,UAAU,CAACI,EAAE,CAAE,UAAU,CAAC,CAC1B;AACAD,KAAK,CAAC,EAAE,CACV,CAAC,CAED,KAAM,CAAAM,QAAQ,CAAG,KAAM,CAAAR,OAAO,CAACyB,aAAa,CAAC,CAE7CpB,OAAO,CAACC,GAAG,6CAAAG,MAAA,CAAwCD,QAAQ,CAACE,IAAI,aAAW,CAAC,CAE5E,MAAO,CACLC,OAAO,CAAE,IAAI,CACbC,OAAO,qCAAAH,MAAA,CAAsCD,QAAQ,CAACE,IAAI,cAAY,CACtEgB,aAAa,CAAElB,QAAQ,CAACE,IAC1B,CAAC,CACH,CAAE,MAAOQ,KAAU,CAAE,CACnBb,OAAO,CAACa,KAAK,CAAC,0BAA0B,CAAEA,KAAK,CAAC,CAEhD,MAAO,CACLP,OAAO,CAAE,KAAK,CACdC,OAAO,2BAAAH,MAAA,CAA4BS,KAAK,CAACN,OAAO,CAAE,CAClDc,aAAa,CAAE,CACjB,CAAC,CACH,CACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}