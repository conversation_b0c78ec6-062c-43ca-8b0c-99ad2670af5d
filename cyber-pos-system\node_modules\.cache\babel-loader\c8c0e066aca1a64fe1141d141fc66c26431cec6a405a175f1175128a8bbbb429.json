{"ast": null, "code": "var _jsxFileName = \"E:\\\\FX\\\\Cyber POS\\\\cyber-pos-system\\\\src\\\\contexts\\\\BusinessSettingsContext.tsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext, useState, useEffect } from 'react';\nimport { getBusinessSettings, initializeBusinessSettings } from '../services/businessSettingsService';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst BusinessSettingsContext = /*#__PURE__*/createContext(undefined);\nexport const BusinessSettingsProvider = ({\n  children\n}) => {\n  _s();\n  const [businessSettings, setBusinessSettings] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const loadBusinessSettings = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n      let settings = await getBusinessSettings();\n\n      // If no settings exist, initialize with defaults\n      if (!settings) {\n        settings = await initializeBusinessSettings();\n      }\n      setBusinessSettings(settings);\n    } catch (err) {\n      console.error('Error loading business settings:', err);\n      setError(err instanceof Error ? err.message : 'Failed to load business settings');\n\n      // Set fallback settings\n      setBusinessSettings({\n        id: 'fallback',\n        businessName: 'Cyber Services & Stationery',\n        address: 'Your Business Address',\n        phone: '+*********** 000',\n        email: '<EMAIL>',\n        currency: 'KSh',\n        taxRate: 16,\n        receiptFooter: 'Thank you for your business!',\n        createdAt: new Date(),\n        updatedAt: new Date()\n      });\n    } finally {\n      setLoading(false);\n    }\n  };\n  const refreshBusinessSettings = async () => {\n    await loadBusinessSettings();\n  };\n  useEffect(() => {\n    loadBusinessSettings();\n  }, []);\n  const value = {\n    businessSettings,\n    loading,\n    error,\n    refreshBusinessSettings\n  };\n  return /*#__PURE__*/_jsxDEV(BusinessSettingsContext.Provider, {\n    value: value,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 74,\n    columnNumber: 5\n  }, this);\n};\n_s(BusinessSettingsProvider, \"oPMXMqgFnkew4XxGwbrM5NI0DMA=\");\n_c = BusinessSettingsProvider;\nexport const useBusinessSettings = () => {\n  _s2();\n  const context = useContext(BusinessSettingsContext);\n  if (context === undefined) {\n    throw new Error('useBusinessSettings must be used within a BusinessSettingsProvider');\n  }\n  return context;\n};\n_s2(useBusinessSettings, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nexport default BusinessSettingsContext;\nvar _c;\n$RefreshReg$(_c, \"BusinessSettingsProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useState", "useEffect", "getBusinessSettings", "initializeBusinessSettings", "jsxDEV", "_jsxDEV", "BusinessSettingsContext", "undefined", "BusinessSettingsProvider", "children", "_s", "businessSettings", "setBusinessSettings", "loading", "setLoading", "error", "setError", "loadBusinessSettings", "settings", "err", "console", "Error", "message", "id", "businessName", "address", "phone", "email", "currency", "taxRate", "receiptFooter", "createdAt", "Date", "updatedAt", "refreshBusinessSettings", "value", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "useBusinessSettings", "_s2", "context", "$RefreshReg$"], "sources": ["E:/FX/Cyber POS/cyber-pos-system/src/contexts/BusinessSettingsContext.tsx"], "sourcesContent": ["import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';\nimport { BusinessSettings } from '../types';\nimport { getBusinessSettings, initializeBusinessSettings } from '../services/businessSettingsService';\n\ninterface BusinessSettingsContextType {\n  businessSettings: BusinessSettings | null;\n  loading: boolean;\n  error: string | null;\n  refreshBusinessSettings: () => Promise<void>;\n}\n\nconst BusinessSettingsContext = createContext<BusinessSettingsContextType | undefined>(undefined);\n\ninterface BusinessSettingsProviderProps {\n  children: ReactNode;\n}\n\nexport const BusinessSettingsProvider: React.FC<BusinessSettingsProviderProps> = ({ children }) => {\n  const [businessSettings, setBusinessSettings] = useState<BusinessSettings | null>(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n\n  const loadBusinessSettings = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n      \n      let settings = await getBusinessSettings();\n      \n      // If no settings exist, initialize with defaults\n      if (!settings) {\n        settings = await initializeBusinessSettings();\n      }\n      \n      setBusinessSettings(settings);\n    } catch (err) {\n      console.error('Error loading business settings:', err);\n      setError(err instanceof Error ? err.message : 'Failed to load business settings');\n      \n      // Set fallback settings\n      setBusinessSettings({\n        id: 'fallback',\n        businessName: 'Cyber Services & Stationery',\n        address: 'Your Business Address',\n        phone: '+*********** 000',\n        email: '<EMAIL>',\n        currency: 'KSh',\n        taxRate: 16,\n        receiptFooter: 'Thank you for your business!',\n        createdAt: new Date(),\n        updatedAt: new Date(),\n      });\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const refreshBusinessSettings = async () => {\n    await loadBusinessSettings();\n  };\n\n  useEffect(() => {\n    loadBusinessSettings();\n  }, []);\n\n  const value: BusinessSettingsContextType = {\n    businessSettings,\n    loading,\n    error,\n    refreshBusinessSettings,\n  };\n\n  return (\n    <BusinessSettingsContext.Provider value={value}>\n      {children}\n    </BusinessSettingsContext.Provider>\n  );\n};\n\nexport const useBusinessSettings = (): BusinessSettingsContextType => {\n  const context = useContext(BusinessSettingsContext);\n  if (context === undefined) {\n    throw new Error('useBusinessSettings must be used within a BusinessSettingsProvider');\n  }\n  return context;\n};\n\nexport default BusinessSettingsContext;\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,SAAS,QAAmB,OAAO;AAExF,SAASC,mBAAmB,EAAEC,0BAA0B,QAAQ,qCAAqC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAStG,MAAMC,uBAAuB,gBAAGR,aAAa,CAA0CS,SAAS,CAAC;AAMjG,OAAO,MAAMC,wBAAiE,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EACjG,MAAM,CAACC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGZ,QAAQ,CAA0B,IAAI,CAAC;EACvF,MAAM,CAACa,OAAO,EAAEC,UAAU,CAAC,GAAGd,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACe,KAAK,EAAEC,QAAQ,CAAC,GAAGhB,QAAQ,CAAgB,IAAI,CAAC;EAEvD,MAAMiB,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACvC,IAAI;MACFH,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;MAEd,IAAIE,QAAQ,GAAG,MAAMhB,mBAAmB,CAAC,CAAC;;MAE1C;MACA,IAAI,CAACgB,QAAQ,EAAE;QACbA,QAAQ,GAAG,MAAMf,0BAA0B,CAAC,CAAC;MAC/C;MAEAS,mBAAmB,CAACM,QAAQ,CAAC;IAC/B,CAAC,CAAC,OAAOC,GAAG,EAAE;MACZC,OAAO,CAACL,KAAK,CAAC,kCAAkC,EAAEI,GAAG,CAAC;MACtDH,QAAQ,CAACG,GAAG,YAAYE,KAAK,GAAGF,GAAG,CAACG,OAAO,GAAG,kCAAkC,CAAC;;MAEjF;MACAV,mBAAmB,CAAC;QAClBW,EAAE,EAAE,UAAU;QACdC,YAAY,EAAE,6BAA6B;QAC3CC,OAAO,EAAE,uBAAuB;QAChCC,KAAK,EAAE,kBAAkB;QACzBC,KAAK,EAAE,uBAAuB;QAC9BC,QAAQ,EAAE,KAAK;QACfC,OAAO,EAAE,EAAE;QACXC,aAAa,EAAE,8BAA8B;QAC7CC,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC;QACrBC,SAAS,EAAE,IAAID,IAAI,CAAC;MACtB,CAAC,CAAC;IACJ,CAAC,SAAS;MACRlB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMoB,uBAAuB,GAAG,MAAAA,CAAA,KAAY;IAC1C,MAAMjB,oBAAoB,CAAC,CAAC;EAC9B,CAAC;EAEDhB,SAAS,CAAC,MAAM;IACdgB,oBAAoB,CAAC,CAAC;EACxB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMkB,KAAkC,GAAG;IACzCxB,gBAAgB;IAChBE,OAAO;IACPE,KAAK;IACLmB;EACF,CAAC;EAED,oBACE7B,OAAA,CAACC,uBAAuB,CAAC8B,QAAQ;IAACD,KAAK,EAAEA,KAAM;IAAA1B,QAAA,EAC5CA;EAAQ;IAAA4B,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACuB,CAAC;AAEvC,CAAC;AAAC9B,EAAA,CA5DWF,wBAAiE;AAAAiC,EAAA,GAAjEjC,wBAAiE;AA8D9E,OAAO,MAAMkC,mBAAmB,GAAGA,CAAA,KAAmC;EAAAC,GAAA;EACpE,MAAMC,OAAO,GAAG7C,UAAU,CAACO,uBAAuB,CAAC;EACnD,IAAIsC,OAAO,KAAKrC,SAAS,EAAE;IACzB,MAAM,IAAIc,KAAK,CAAC,oEAAoE,CAAC;EACvF;EACA,OAAOuB,OAAO;AAChB,CAAC;AAACD,GAAA,CANWD,mBAAmB;AAQhC,eAAepC,uBAAuB;AAAC,IAAAmC,EAAA;AAAAI,YAAA,CAAAJ,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}