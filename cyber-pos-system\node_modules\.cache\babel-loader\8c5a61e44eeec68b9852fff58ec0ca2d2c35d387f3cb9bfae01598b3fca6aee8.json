{"ast": null, "code": "import React,{createContext,useContext,useState,useEffect}from'react';import{getBusinessSettings,initializeBusinessSettings}from'../services/businessSettingsService';import{jsx as _jsx}from\"react/jsx-runtime\";const BusinessSettingsContext=/*#__PURE__*/createContext(undefined);export const BusinessSettingsProvider=_ref=>{let{children}=_ref;const[businessSettings,setBusinessSettings]=useState(null);const[loading,setLoading]=useState(true);const[error,setError]=useState(null);const loadBusinessSettings=async()=>{try{setLoading(true);setError(null);let settings=await getBusinessSettings();// If no settings exist, initialize with defaults\nif(!settings){settings=await initializeBusinessSettings();}setBusinessSettings(settings);}catch(err){console.error('Error loading business settings:',err);setError(err instanceof Error?err.message:'Failed to load business settings');// Set fallback settings\nsetBusinessSettings({id:'fallback',businessName:'Cyber Services & Stationery',address:'Your Business Address',phone:'+*********** 000',email:'<EMAIL>',currency:'KSh',taxRate:16,receiptFooter:'Thank you for your business!',createdAt:new Date(),updatedAt:new Date()});}finally{setLoading(false);}};const refreshBusinessSettings=async()=>{await loadBusinessSettings();};useEffect(()=>{loadBusinessSettings();},[]);const value={businessSettings,loading,error,refreshBusinessSettings};return/*#__PURE__*/_jsx(BusinessSettingsContext.Provider,{value:value,children:children});};export const useBusinessSettings=()=>{const context=useContext(BusinessSettingsContext);if(context===undefined){throw new Error('useBusinessSettings must be used within a BusinessSettingsProvider');}return context;};export default BusinessSettingsContext;", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useState", "useEffect", "getBusinessSettings", "initializeBusinessSettings", "jsx", "_jsx", "BusinessSettingsContext", "undefined", "BusinessSettingsProvider", "_ref", "children", "businessSettings", "setBusinessSettings", "loading", "setLoading", "error", "setError", "loadBusinessSettings", "settings", "err", "console", "Error", "message", "id", "businessName", "address", "phone", "email", "currency", "taxRate", "receiptFooter", "createdAt", "Date", "updatedAt", "refreshBusinessSettings", "value", "Provider", "useBusinessSettings", "context"], "sources": ["E:/FX/Cyber POS/cyber-pos-system/src/contexts/BusinessSettingsContext.tsx"], "sourcesContent": ["import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';\nimport { BusinessSettings } from '../types';\nimport { getBusinessSettings, initializeBusinessSettings } from '../services/businessSettingsService';\n\ninterface BusinessSettingsContextType {\n  businessSettings: BusinessSettings | null;\n  loading: boolean;\n  error: string | null;\n  refreshBusinessSettings: () => Promise<void>;\n}\n\nconst BusinessSettingsContext = createContext<BusinessSettingsContextType | undefined>(undefined);\n\ninterface BusinessSettingsProviderProps {\n  children: ReactNode;\n}\n\nexport const BusinessSettingsProvider: React.FC<BusinessSettingsProviderProps> = ({ children }) => {\n  const [businessSettings, setBusinessSettings] = useState<BusinessSettings | null>(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n\n  const loadBusinessSettings = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n      \n      let settings = await getBusinessSettings();\n      \n      // If no settings exist, initialize with defaults\n      if (!settings) {\n        settings = await initializeBusinessSettings();\n      }\n      \n      setBusinessSettings(settings);\n    } catch (err) {\n      console.error('Error loading business settings:', err);\n      setError(err instanceof Error ? err.message : 'Failed to load business settings');\n      \n      // Set fallback settings\n      setBusinessSettings({\n        id: 'fallback',\n        businessName: 'Cyber Services & Stationery',\n        address: 'Your Business Address',\n        phone: '+*********** 000',\n        email: '<EMAIL>',\n        currency: 'KSh',\n        taxRate: 16,\n        receiptFooter: 'Thank you for your business!',\n        createdAt: new Date(),\n        updatedAt: new Date(),\n      });\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const refreshBusinessSettings = async () => {\n    await loadBusinessSettings();\n  };\n\n  useEffect(() => {\n    loadBusinessSettings();\n  }, []);\n\n  const value: BusinessSettingsContextType = {\n    businessSettings,\n    loading,\n    error,\n    refreshBusinessSettings,\n  };\n\n  return (\n    <BusinessSettingsContext.Provider value={value}>\n      {children}\n    </BusinessSettingsContext.Provider>\n  );\n};\n\nexport const useBusinessSettings = (): BusinessSettingsContextType => {\n  const context = useContext(BusinessSettingsContext);\n  if (context === undefined) {\n    throw new Error('useBusinessSettings must be used within a BusinessSettingsProvider');\n  }\n  return context;\n};\n\nexport default BusinessSettingsContext;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,aAAa,CAAEC,UAAU,CAAEC,QAAQ,CAAEC,SAAS,KAAmB,OAAO,CAExF,OAASC,mBAAmB,CAAEC,0BAA0B,KAAQ,qCAAqC,CAAC,OAAAC,GAAA,IAAAC,IAAA,yBAStG,KAAM,CAAAC,uBAAuB,cAAGR,aAAa,CAA0CS,SAAS,CAAC,CAMjG,MAAO,MAAM,CAAAC,wBAAiE,CAAGC,IAAA,EAAkB,IAAjB,CAAEC,QAAS,CAAC,CAAAD,IAAA,CAC5F,KAAM,CAACE,gBAAgB,CAAEC,mBAAmB,CAAC,CAAGZ,QAAQ,CAA0B,IAAI,CAAC,CACvF,KAAM,CAACa,OAAO,CAAEC,UAAU,CAAC,CAAGd,QAAQ,CAAC,IAAI,CAAC,CAC5C,KAAM,CAACe,KAAK,CAAEC,QAAQ,CAAC,CAAGhB,QAAQ,CAAgB,IAAI,CAAC,CAEvD,KAAM,CAAAiB,oBAAoB,CAAG,KAAAA,CAAA,GAAY,CACvC,GAAI,CACFH,UAAU,CAAC,IAAI,CAAC,CAChBE,QAAQ,CAAC,IAAI,CAAC,CAEd,GAAI,CAAAE,QAAQ,CAAG,KAAM,CAAAhB,mBAAmB,CAAC,CAAC,CAE1C;AACA,GAAI,CAACgB,QAAQ,CAAE,CACbA,QAAQ,CAAG,KAAM,CAAAf,0BAA0B,CAAC,CAAC,CAC/C,CAEAS,mBAAmB,CAACM,QAAQ,CAAC,CAC/B,CAAE,MAAOC,GAAG,CAAE,CACZC,OAAO,CAACL,KAAK,CAAC,kCAAkC,CAAEI,GAAG,CAAC,CACtDH,QAAQ,CAACG,GAAG,WAAY,CAAAE,KAAK,CAAGF,GAAG,CAACG,OAAO,CAAG,kCAAkC,CAAC,CAEjF;AACAV,mBAAmB,CAAC,CAClBW,EAAE,CAAE,UAAU,CACdC,YAAY,CAAE,6BAA6B,CAC3CC,OAAO,CAAE,uBAAuB,CAChCC,KAAK,CAAE,kBAAkB,CACzBC,KAAK,CAAE,uBAAuB,CAC9BC,QAAQ,CAAE,KAAK,CACfC,OAAO,CAAE,EAAE,CACXC,aAAa,CAAE,8BAA8B,CAC7CC,SAAS,CAAE,GAAI,CAAAC,IAAI,CAAC,CAAC,CACrBC,SAAS,CAAE,GAAI,CAAAD,IAAI,CAAC,CACtB,CAAC,CAAC,CACJ,CAAC,OAAS,CACRlB,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED,KAAM,CAAAoB,uBAAuB,CAAG,KAAAA,CAAA,GAAY,CAC1C,KAAM,CAAAjB,oBAAoB,CAAC,CAAC,CAC9B,CAAC,CAEDhB,SAAS,CAAC,IAAM,CACdgB,oBAAoB,CAAC,CAAC,CACxB,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAkB,KAAkC,CAAG,CACzCxB,gBAAgB,CAChBE,OAAO,CACPE,KAAK,CACLmB,uBACF,CAAC,CAED,mBACE7B,IAAA,CAACC,uBAAuB,CAAC8B,QAAQ,EAACD,KAAK,CAAEA,KAAM,CAAAzB,QAAA,CAC5CA,QAAQ,CACuB,CAAC,CAEvC,CAAC,CAED,MAAO,MAAM,CAAA2B,mBAAmB,CAAGA,CAAA,GAAmC,CACpE,KAAM,CAAAC,OAAO,CAAGvC,UAAU,CAACO,uBAAuB,CAAC,CACnD,GAAIgC,OAAO,GAAK/B,SAAS,CAAE,CACzB,KAAM,IAAI,CAAAc,KAAK,CAAC,oEAAoE,CAAC,CACvF,CACA,MAAO,CAAAiB,OAAO,CAChB,CAAC,CAED,cAAe,CAAAhC,uBAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}