import { Transaction, User, BusinessSettings } from '../types';
import { getBusinessSettings } from '../services/businessSettingsService';

export interface ReceiptData {
  transaction: Transaction;
  attendant: User;
  businessInfo?: {
    name: string;
    address: string;
    phone: string;
    email?: string;
  };
  businessSettings?: BusinessSettings;
}

export const generateReceiptHTML = (receiptData: ReceiptData): string => {
  const { transaction, attendant, businessInfo, businessSettings } = receiptData;
  const receiptDate = transaction.createdAt.toLocaleString();

  // Use business settings if available, otherwise fall back to businessInfo
  const companyName = businessSettings?.businessName || businessInfo?.name || 'Business Name';
  const companyAddress = businessSettings?.address || businessInfo?.address || 'Business Address';
  const companyPhone = businessSettings?.phone || businessInfo?.phone || 'Phone Number';
  const companyEmail = businessSettings?.email || businessInfo?.email;
  const currency = businessSettings?.currency || 'KSh';
  const logoUrl = businessSettings?.logoUrl;
  const receiptHeader = businessSettings?.receiptHeader;
  const receiptFooter = businessSettings?.receiptFooter || 'Thank you for your business!';
  const operatingHours = businessSettings?.operatingHours;

  return `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <title>Receipt - ${transaction.id}</title>
      <style>
        @media print {
          body { margin: 0; }
          .no-print { display: none; }
        }
        
        body {
          font-family: 'Courier New', monospace;
          font-size: 12px;
          line-height: 1.4;
          max-width: 300px;
          margin: 0 auto;
          padding: 10px;
        }
        
        .header {
          text-align: center;
          border-bottom: 2px solid #000;
          padding-bottom: 10px;
          margin-bottom: 15px;
        }

        .logo {
          max-width: 80px;
          max-height: 80px;
          margin: 0 auto 10px auto;
          display: block;
        }

        .business-name {
          font-size: 16px;
          font-weight: bold;
          margin-bottom: 5px;
        }

        .business-info {
          font-size: 10px;
          margin-bottom: 2px;
        }

        .receipt-header-message {
          font-size: 11px;
          font-style: italic;
          margin-bottom: 5px;
          color: #666;
        }
        
        .receipt-info {
          margin-bottom: 15px;
          font-size: 10px;
        }
        
        .items-table {
          width: 100%;
          border-collapse: collapse;
          margin-bottom: 15px;
        }
        
        .items-table th,
        .items-table td {
          text-align: left;
          padding: 2px 0;
          border-bottom: 1px dotted #ccc;
        }
        
        .items-table th {
          font-weight: bold;
          border-bottom: 1px solid #000;
        }
        
        .item-name {
          width: 60%;
        }
        
        .item-qty {
          width: 15%;
          text-align: center;
        }
        
        .item-price {
          width: 25%;
          text-align: right;
        }
        
        .totals {
          border-top: 2px solid #000;
          padding-top: 10px;
          margin-bottom: 15px;
        }
        
        .total-line {
          display: flex;
          justify-content: space-between;
          margin-bottom: 3px;
        }
        
        .total-line.final {
          font-weight: bold;
          font-size: 14px;
          border-top: 1px solid #000;
          padding-top: 5px;
          margin-top: 5px;
        }
        
        .payment-info {
          margin-bottom: 15px;
          font-size: 10px;
        }
        
        .footer {
          text-align: center;
          border-top: 1px dotted #ccc;
          padding-top: 10px;
          font-size: 10px;
        }
        
        .print-button {
          background: #007bff;
          color: white;
          border: none;
          padding: 10px 20px;
          border-radius: 5px;
          cursor: pointer;
          margin: 20px auto;
          display: block;
        }
        
        .print-button:hover {
          background: #0056b3;
        }
      </style>
    </head>
    <body>
      <div class="header">
        ${logoUrl ? `<img src="${logoUrl}" alt="Company Logo" class="logo" />` : ''}
        <div class="business-name">${companyName}</div>
        ${receiptHeader ? `<div class="receipt-header-message">${receiptHeader}</div>` : ''}
        <div class="business-info">${companyAddress}</div>
        <div class="business-info">Tel: ${companyPhone}</div>
        ${companyEmail ? `<div class="business-info">Email: ${companyEmail}</div>` : ''}
        ${operatingHours ? `<div class="business-info">${operatingHours.replace(/\n/g, '<br>')}</div>` : ''}
      </div>
      
      <div class="receipt-info">
        <div><strong>Receipt #:</strong> ${transaction.id.substring(0, 8).toUpperCase()}</div>
        <div><strong>Date:</strong> ${receiptDate}</div>
        <div><strong>Attendant:</strong> ${attendant.name}</div>
        ${transaction.customerId ? `<div><strong>Customer:</strong> Credit Sale</div>` : ''}
      </div>
      
      <table class="items-table">
        <thead>
          <tr>
            <th class="item-name">Item</th>
            <th class="item-qty">Qty</th>
            <th class="item-price">Amount</th>
          </tr>
        </thead>
        <tbody>
          ${transaction.items.map(item => `
            <tr>
              <td class="item-name">
                ${item.name}
                ${item.notes ? `<br><small style="font-size: 9px; color: #666;">${item.notes}</small>` : ''}
              </td>
              <td class="item-qty">${item.quantity}</td>
              <td class="item-price">${currency} ${item.totalPrice.toLocaleString()}</td>
            </tr>
          `).join('')}
        </tbody>
      </table>
      
      <div class="totals">
        <div class="total-line">
          <span>Subtotal:</span>
          <span>${currency} ${transaction.subtotal.toLocaleString()}</span>
        </div>
        ${transaction.discount && transaction.discount > 0 ? `
          <div class="total-line">
            <span>Discount:</span>
            <span>-${currency} ${transaction.discount.toLocaleString()}</span>
          </div>
        ` : ''}
        <div class="total-line final">
          <span>TOTAL:</span>
          <span>${currency} ${transaction.total.toLocaleString()}</span>
        </div>
      </div>
      
      <div class="payment-info">
        <div><strong>Payment Method:</strong> ${getPaymentMethodName(transaction.paymentMethod)}</div>
        ${transaction.notes ? `<div><strong>Notes:</strong> ${transaction.notes}</div>` : ''}
      </div>
      
      <div class="footer">
        <div>${receiptFooter.replace(/\n/g, '<br>')}</div>
        <div style="margin-top: 10px; font-size: 9px;">
          Powered by Cyber POS System
        </div>
      </div>
      
      <button class="print-button no-print" onclick="window.print()">
        Print Receipt
      </button>
      
      <script>
        // Auto-focus for printing
        window.onload = function() {
          window.focus();
        };
      </script>
    </body>
    </html>
  `;
};

const getPaymentMethodName = (method: string): string => {
  switch (method) {
    case 'cash':
      return 'Cash';
    case 'mpesa':
      return 'M-PESA';
    case 'debt':
      return 'Credit/Debt';
    default:
      return method;
  }
};

export const printReceipt = (receiptData: ReceiptData): void => {
  const receiptHTML = generateReceiptHTML(receiptData);
  const printWindow = window.open('', '_blank', 'width=400,height=600');
  
  if (printWindow) {
    printWindow.document.write(receiptHTML);
    printWindow.document.close();
    
    // Wait for content to load then print
    printWindow.onload = () => {
      setTimeout(() => {
        printWindow.print();
      }, 250);
    };
  } else {
    alert('Please allow popups to print receipts');
  }
};

export const downloadReceiptPDF = async (receiptData: ReceiptData): Promise<void> => {
  // This would require a PDF library like jsPDF or html2pdf
  // For now, we'll just open the receipt in a new window
  const receiptHTML = generateReceiptHTML(receiptData);
  const printWindow = window.open('', '_blank');

  if (printWindow) {
    printWindow.document.write(receiptHTML);
    printWindow.document.close();
  }
};

/**
 * Create receipt data with business settings
 */
export const createReceiptData = async (
  transaction: Transaction,
  attendant: User
): Promise<ReceiptData> => {
  try {
    const businessSettings = await getBusinessSettings();
    return {
      transaction,
      attendant,
      businessSettings: businessSettings || undefined,
    };
  } catch (error) {
    console.error('Error loading business settings for receipt:', error);
    // Fallback to default business info
    return {
      transaction,
      attendant,
      businessInfo: {
        name: 'Cyber Services & Stationery',
        address: 'Your Business Address',
        phone: '+254 700 000 000',
        email: '<EMAIL>',
      },
    };
  }
};

/**
 * Print receipt with business settings
 */
export const printReceiptWithSettings = async (
  transaction: Transaction,
  attendant: User
): Promise<void> => {
  const receiptData = await createReceiptData(transaction, attendant);
  printReceipt(receiptData);
};

/**
 * Download receipt PDF with business settings
 */
export const downloadReceiptPDFWithSettings = async (
  transaction: Transaction,
  attendant: User
): Promise<void> => {
  const receiptData = await createReceiptData(transaction, attendant);
  await downloadReceiptPDF(receiptData);
};
