{"ast": null, "code": "import React from'react';import{BrowserRouter as Router,Routes,Route,Navigate}from'react-router-dom';import{AuthProvider,useAuth}from'./contexts/AuthContext';import{BusinessSettingsProvider}from'./contexts/BusinessSettingsContext';import Login from'./components/auth/Login';import Dashboard from'./components/dashboard/Dashboard';import POS from'./components/pos/POS';import Services from'./components/services/Services';import Inventory from'./components/inventory/Inventory';import Reports from'./components/reports/Reports';import Settings from'./components/settings/Settings';import Layout from'./components/layout/Layout';import LoadingSpinner from'./components/common/LoadingSpinner';import OfflineManager from'./components/offline/OfflineManager';import FirebaseConnectionTest from'./components/debug/FirebaseConnectionTest';import'./App.css';// Protected Route Component\nimport{jsx as _jsx,Fragment as _Fragment,jsxs as _jsxs}from\"react/jsx-runtime\";const ProtectedRoute=_ref=>{let{children}=_ref;const{currentUser,loading}=useAuth();if(loading){return/*#__PURE__*/_jsx(LoadingSpinner,{});}if(!currentUser){return/*#__PURE__*/_jsx(Navigate,{to:\"/login\",replace:true});}return/*#__PURE__*/_jsx(_Fragment,{children:children});};// Main App Routes\nconst AppRoutes=()=>{const{currentUser,loading}=useAuth();if(loading){return/*#__PURE__*/_jsx(LoadingSpinner,{});}if(!currentUser){return/*#__PURE__*/_jsxs(Routes,{children:[/*#__PURE__*/_jsx(Route,{path:\"/login\",element:/*#__PURE__*/_jsx(Login,{})}),/*#__PURE__*/_jsx(Route,{path:\"*\",element:/*#__PURE__*/_jsx(Navigate,{to:\"/login\",replace:true})})]});}return/*#__PURE__*/_jsx(BusinessSettingsProvider,{children:/*#__PURE__*/_jsx(Layout,{children:/*#__PURE__*/_jsxs(Routes,{children:[/*#__PURE__*/_jsx(Route,{path:\"/\",element:/*#__PURE__*/_jsx(Navigate,{to:\"/dashboard\",replace:true})}),/*#__PURE__*/_jsx(Route,{path:\"/dashboard\",element:/*#__PURE__*/_jsx(Dashboard,{})}),/*#__PURE__*/_jsx(Route,{path:\"/pos\",element:/*#__PURE__*/_jsx(POS,{})}),/*#__PURE__*/_jsx(Route,{path:\"/services\",element:/*#__PURE__*/_jsx(Services,{})}),/*#__PURE__*/_jsx(Route,{path:\"/inventory\",element:/*#__PURE__*/_jsx(Inventory,{})}),/*#__PURE__*/_jsx(Route,{path:\"/reports\",element:/*#__PURE__*/_jsx(Reports,{})}),/*#__PURE__*/_jsx(Route,{path:\"/settings\",element:/*#__PURE__*/_jsx(Settings,{})}),/*#__PURE__*/_jsx(Route,{path:\"/debug/firebase\",element:/*#__PURE__*/_jsx(FirebaseConnectionTest,{})}),/*#__PURE__*/_jsx(Route,{path:\"/login\",element:/*#__PURE__*/_jsx(Navigate,{to:\"/dashboard\",replace:true})}),/*#__PURE__*/_jsx(Route,{path:\"*\",element:/*#__PURE__*/_jsx(Navigate,{to:\"/dashboard\",replace:true})})]})})});};function App(){return/*#__PURE__*/_jsx(AuthProvider,{children:/*#__PURE__*/_jsx(Router,{children:/*#__PURE__*/_jsxs(\"div\",{className:\"App min-h-screen bg-gray-50\",children:[/*#__PURE__*/_jsx(AppRoutes,{}),/*#__PURE__*/_jsx(OfflineManager,{})]})})});}export default App;", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "Navigate", "<PERSON>th<PERSON><PERSON><PERSON>", "useAuth", "BusinessSettingsProvider", "<PERSON><PERSON>", "Dashboard", "POS", "Services", "Inventory", "Reports", "Settings", "Layout", "LoadingSpinner", "OfflineManager", "FirebaseConnectionTest", "jsx", "_jsx", "Fragment", "_Fragment", "jsxs", "_jsxs", "ProtectedRoute", "_ref", "children", "currentUser", "loading", "to", "replace", "AppRoutes", "path", "element", "App", "className"], "sources": ["E:/FX/Cyber POS/cyber-pos-system/src/App.tsx"], "sourcesContent": ["import React from 'react';\nimport { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';\nimport { AuthProvider, useAuth } from './contexts/AuthContext';\nimport { BusinessSettingsProvider } from './contexts/BusinessSettingsContext';\nimport Login from './components/auth/Login';\nimport Dashboard from './components/dashboard/Dashboard';\nimport POS from './components/pos/POS';\nimport Services from './components/services/Services';\nimport Inventory from './components/inventory/Inventory';\nimport Reports from './components/reports/Reports';\nimport Settings from './components/settings/Settings';\nimport Layout from './components/layout/Layout';\nimport LoadingSpinner from './components/common/LoadingSpinner';\nimport OfflineManager from './components/offline/OfflineManager';\nimport FirebaseConnectionTest from './components/debug/FirebaseConnectionTest';\nimport './App.css';\n\n// Protected Route Component\nconst ProtectedRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {\n  const { currentUser, loading } = useAuth();\n  \n  if (loading) {\n    return <LoadingSpinner />;\n  }\n  \n  if (!currentUser) {\n    return <Navigate to=\"/login\" replace />;\n  }\n  \n  return <>{children}</>;\n};\n\n// Main App Routes\nconst AppRoutes: React.FC = () => {\n  const { currentUser, loading } = useAuth();\n  \n  if (loading) {\n    return <LoadingSpinner />;\n  }\n  \n  if (!currentUser) {\n    return (\n      <Routes>\n        <Route path=\"/login\" element={<Login />} />\n        <Route path=\"*\" element={<Navigate to=\"/login\" replace />} />\n      </Routes>\n    );\n  }\n  \n  return (\n    <BusinessSettingsProvider>\n      <Layout>\n        <Routes>\n          <Route path=\"/\" element={<Navigate to=\"/dashboard\" replace />} />\n          <Route path=\"/dashboard\" element={<Dashboard />} />\n          <Route path=\"/pos\" element={<POS />} />\n          <Route path=\"/services\" element={<Services />} />\n          <Route path=\"/inventory\" element={<Inventory />} />\n          <Route path=\"/reports\" element={<Reports />} />\n          <Route path=\"/settings\" element={<Settings />} />\n          <Route path=\"/debug/firebase\" element={<FirebaseConnectionTest />} />\n          <Route path=\"/login\" element={<Navigate to=\"/dashboard\" replace />} />\n          <Route path=\"*\" element={<Navigate to=\"/dashboard\" replace />} />\n        </Routes>\n      </Layout>\n    </BusinessSettingsProvider>\n  );\n};\n\nfunction App() {\n  return (\n    <AuthProvider>\n      <Router>\n        <div className=\"App min-h-screen bg-gray-50\">\n          <AppRoutes />\n          <OfflineManager />\n        </div>\n      </Router>\n    </AuthProvider>\n  );\n}\n\nexport default App;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OAASC,aAAa,GAAI,CAAAC,MAAM,CAAEC,MAAM,CAAEC,KAAK,CAAEC,QAAQ,KAAQ,kBAAkB,CACnF,OAASC,YAAY,CAAEC,OAAO,KAAQ,wBAAwB,CAC9D,OAASC,wBAAwB,KAAQ,oCAAoC,CAC7E,MAAO,CAAAC,KAAK,KAAM,yBAAyB,CAC3C,MAAO,CAAAC,SAAS,KAAM,kCAAkC,CACxD,MAAO,CAAAC,GAAG,KAAM,sBAAsB,CACtC,MAAO,CAAAC,QAAQ,KAAM,gCAAgC,CACrD,MAAO,CAAAC,SAAS,KAAM,kCAAkC,CACxD,MAAO,CAAAC,OAAO,KAAM,8BAA8B,CAClD,MAAO,CAAAC,QAAQ,KAAM,gCAAgC,CACrD,MAAO,CAAAC,MAAM,KAAM,4BAA4B,CAC/C,MAAO,CAAAC,cAAc,KAAM,oCAAoC,CAC/D,MAAO,CAAAC,cAAc,KAAM,qCAAqC,CAChE,MAAO,CAAAC,sBAAsB,KAAM,2CAA2C,CAC9E,MAAO,WAAW,CAElB;AAAA,OAAAC,GAAA,IAAAC,IAAA,CAAAC,QAAA,IAAAC,SAAA,CAAAC,IAAA,IAAAC,KAAA,yBACA,KAAM,CAAAC,cAAuD,CAAGC,IAAA,EAAkB,IAAjB,CAAEC,QAAS,CAAC,CAAAD,IAAA,CAC3E,KAAM,CAAEE,WAAW,CAAEC,OAAQ,CAAC,CAAGvB,OAAO,CAAC,CAAC,CAE1C,GAAIuB,OAAO,CAAE,CACX,mBAAOT,IAAA,CAACJ,cAAc,GAAE,CAAC,CAC3B,CAEA,GAAI,CAACY,WAAW,CAAE,CAChB,mBAAOR,IAAA,CAAChB,QAAQ,EAAC0B,EAAE,CAAC,QAAQ,CAACC,OAAO,MAAE,CAAC,CACzC,CAEA,mBAAOX,IAAA,CAAAE,SAAA,EAAAK,QAAA,CAAGA,QAAQ,CAAG,CAAC,CACxB,CAAC,CAED;AACA,KAAM,CAAAK,SAAmB,CAAGA,CAAA,GAAM,CAChC,KAAM,CAAEJ,WAAW,CAAEC,OAAQ,CAAC,CAAGvB,OAAO,CAAC,CAAC,CAE1C,GAAIuB,OAAO,CAAE,CACX,mBAAOT,IAAA,CAACJ,cAAc,GAAE,CAAC,CAC3B,CAEA,GAAI,CAACY,WAAW,CAAE,CAChB,mBACEJ,KAAA,CAACtB,MAAM,EAAAyB,QAAA,eACLP,IAAA,CAACjB,KAAK,EAAC8B,IAAI,CAAC,QAAQ,CAACC,OAAO,cAAEd,IAAA,CAACZ,KAAK,GAAE,CAAE,CAAE,CAAC,cAC3CY,IAAA,CAACjB,KAAK,EAAC8B,IAAI,CAAC,GAAG,CAACC,OAAO,cAAEd,IAAA,CAAChB,QAAQ,EAAC0B,EAAE,CAAC,QAAQ,CAACC,OAAO,MAAE,CAAE,CAAE,CAAC,EACvD,CAAC,CAEb,CAEA,mBACEX,IAAA,CAACb,wBAAwB,EAAAoB,QAAA,cACvBP,IAAA,CAACL,MAAM,EAAAY,QAAA,cACLH,KAAA,CAACtB,MAAM,EAAAyB,QAAA,eACLP,IAAA,CAACjB,KAAK,EAAC8B,IAAI,CAAC,GAAG,CAACC,OAAO,cAAEd,IAAA,CAAChB,QAAQ,EAAC0B,EAAE,CAAC,YAAY,CAACC,OAAO,MAAE,CAAE,CAAE,CAAC,cACjEX,IAAA,CAACjB,KAAK,EAAC8B,IAAI,CAAC,YAAY,CAACC,OAAO,cAAEd,IAAA,CAACX,SAAS,GAAE,CAAE,CAAE,CAAC,cACnDW,IAAA,CAACjB,KAAK,EAAC8B,IAAI,CAAC,MAAM,CAACC,OAAO,cAAEd,IAAA,CAACV,GAAG,GAAE,CAAE,CAAE,CAAC,cACvCU,IAAA,CAACjB,KAAK,EAAC8B,IAAI,CAAC,WAAW,CAACC,OAAO,cAAEd,IAAA,CAACT,QAAQ,GAAE,CAAE,CAAE,CAAC,cACjDS,IAAA,CAACjB,KAAK,EAAC8B,IAAI,CAAC,YAAY,CAACC,OAAO,cAAEd,IAAA,CAACR,SAAS,GAAE,CAAE,CAAE,CAAC,cACnDQ,IAAA,CAACjB,KAAK,EAAC8B,IAAI,CAAC,UAAU,CAACC,OAAO,cAAEd,IAAA,CAACP,OAAO,GAAE,CAAE,CAAE,CAAC,cAC/CO,IAAA,CAACjB,KAAK,EAAC8B,IAAI,CAAC,WAAW,CAACC,OAAO,cAAEd,IAAA,CAACN,QAAQ,GAAE,CAAE,CAAE,CAAC,cACjDM,IAAA,CAACjB,KAAK,EAAC8B,IAAI,CAAC,iBAAiB,CAACC,OAAO,cAAEd,IAAA,CAACF,sBAAsB,GAAE,CAAE,CAAE,CAAC,cACrEE,IAAA,CAACjB,KAAK,EAAC8B,IAAI,CAAC,QAAQ,CAACC,OAAO,cAAEd,IAAA,CAAChB,QAAQ,EAAC0B,EAAE,CAAC,YAAY,CAACC,OAAO,MAAE,CAAE,CAAE,CAAC,cACtEX,IAAA,CAACjB,KAAK,EAAC8B,IAAI,CAAC,GAAG,CAACC,OAAO,cAAEd,IAAA,CAAChB,QAAQ,EAAC0B,EAAE,CAAC,YAAY,CAACC,OAAO,MAAE,CAAE,CAAE,CAAC,EAC3D,CAAC,CACH,CAAC,CACe,CAAC,CAE/B,CAAC,CAED,QAAS,CAAAI,GAAGA,CAAA,CAAG,CACb,mBACEf,IAAA,CAACf,YAAY,EAAAsB,QAAA,cACXP,IAAA,CAACnB,MAAM,EAAA0B,QAAA,cACLH,KAAA,QAAKY,SAAS,CAAC,6BAA6B,CAAAT,QAAA,eAC1CP,IAAA,CAACY,SAAS,GAAE,CAAC,cACbZ,IAAA,CAACH,cAAc,GAAE,CAAC,EACf,CAAC,CACA,CAAC,CACG,CAAC,CAEnB,CAEA,cAAe,CAAAkB,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}