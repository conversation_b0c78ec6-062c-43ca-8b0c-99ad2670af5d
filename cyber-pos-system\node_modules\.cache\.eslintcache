[{"E:\\FX\\Cyber POS\\cyber-pos-system\\src\\index.tsx": "1", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\reportWebVitals.js": "2", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\App.tsx": "3", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\contexts\\AuthContext.tsx": "4", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\dashboard\\Dashboard.tsx": "5", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\auth\\Login.tsx": "6", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\inventory\\Inventory.tsx": "7", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\pos\\POS.tsx": "8", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\services\\Services.tsx": "9", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\reports\\Reports.tsx": "10", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\settings\\Settings.tsx": "11", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\layout\\Layout.tsx": "12", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\common\\LoadingSpinner.tsx": "13", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\offline\\OfflineManager.tsx": "14", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\config\\firebase.ts": "15", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\hooks\\useDashboard.ts": "16", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\hooks\\useProducts.ts": "17", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\hooks\\useServices.ts": "18", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\hooks\\useReports.ts": "19", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\hooks\\useCart.ts": "20", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\utils\\seedData.ts": "21", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\utils\\testDataSeeder.ts": "22", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\inventory\\InventoryStats.tsx": "23", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\inventory\\ProductModal.tsx": "24", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\inventory\\StockAdjustmentModal.tsx": "25", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\inventory\\LowStockAlert.tsx": "26", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\pos\\POSCart.tsx": "27", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\pos\\ServiceSelector.tsx": "28", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\pos\\ProductSelector.tsx": "29", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\services\\ServiceCategories.tsx": "30", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\services\\ServiceStats.tsx": "31", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\services\\ServiceModal.tsx": "32", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\reports\\InventoryAnalytics.tsx": "33", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\users\\UserManagement.tsx": "34", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\hooks\\useTransactions.ts": "35", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\utils\\receiptGenerator.ts": "36", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\utils\\serviceUtils.ts": "37", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\pos\\CheckoutModal.tsx": "38", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\inventory\\BulkOperations.tsx": "39", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\utils\\excelImport.ts": "40", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\debug\\FirebaseConnectionTest.tsx": "41", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\utils\\firebaseConnectionTest.ts": "42", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\utils\\objectUtils.ts": "43", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\contexts\\BusinessSettingsContext.tsx": "44", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\services\\businessSettingsService.ts": "45"}, {"size": 550, "mtime": 1751002257124, "results": "46", "hashOfConfig": "47"}, {"size": 362, "mtime": 1751001516282, "results": "48", "hashOfConfig": "47"}, {"size": 2643, "mtime": 1751109135105, "results": "49", "hashOfConfig": "47"}, {"size": 5912, "mtime": 1751002889593, "results": "50", "hashOfConfig": "47"}, {"size": 9398, "mtime": 1751013933072, "results": "51", "hashOfConfig": "47"}, {"size": 7363, "mtime": 1751020900684, "results": "52", "hashOfConfig": "47"}, {"size": 17601, "mtime": 1751093334719, "results": "53", "hashOfConfig": "47"}, {"size": 8045, "mtime": 1751003816943, "results": "54", "hashOfConfig": "47"}, {"size": 13480, "mtime": 1751003649429, "results": "55", "hashOfConfig": "47"}, {"size": 26580, "mtime": 1751008212115, "results": "56", "hashOfConfig": "47"}, {"size": 21873, "mtime": 1751109697330, "results": "57", "hashOfConfig": "47"}, {"size": 7470, "mtime": 1751109211804, "results": "58", "hashOfConfig": "47"}, {"size": 665, "mtime": 1751001779724, "results": "59", "hashOfConfig": "47"}, {"size": 10403, "mtime": 1751017441838, "results": "60", "hashOfConfig": "47"}, {"size": 3887, "mtime": 1751019446562, "results": "61", "hashOfConfig": "47"}, {"size": 9454, "mtime": 1751013836375, "results": "62", "hashOfConfig": "47"}, {"size": 7430, "mtime": 1751098211368, "results": "63", "hashOfConfig": "47"}, {"size": 5279, "mtime": 1751092620848, "results": "64", "hashOfConfig": "47"}, {"size": 10667, "mtime": 1751006877684, "results": "65", "hashOfConfig": "47"}, {"size": 5107, "mtime": 1751003753867, "results": "66", "hashOfConfig": "47"}, {"size": 9579, "mtime": 1751020844856, "results": "67", "hashOfConfig": "47"}, {"size": 7956, "mtime": 1751007184267, "results": "68", "hashOfConfig": "47"}, {"size": 11510, "mtime": 1751009626258, "results": "69", "hashOfConfig": "47"}, {"size": 12807, "mtime": 1751094204600, "results": "70", "hashOfConfig": "47"}, {"size": 10775, "mtime": 1751004342324, "results": "71", "hashOfConfig": "47"}, {"size": 12479, "mtime": 1751004391960, "results": "72", "hashOfConfig": "47"}, {"size": 15195, "mtime": 1751005588991, "results": "73", "hashOfConfig": "47"}, {"size": 13401, "mtime": 1751003915007, "results": "74", "hashOfConfig": "47"}, {"size": 10910, "mtime": 1751003957303, "results": "75", "hashOfConfig": "47"}, {"size": 3964, "mtime": 1751003442458, "results": "76", "hashOfConfig": "47"}, {"size": 8498, "mtime": 1751003577995, "results": "77", "hashOfConfig": "47"}, {"size": 9054, "mtime": 1751003411814, "results": "78", "hashOfConfig": "47"}, {"size": 11145, "mtime": 1751009654679, "results": "79", "hashOfConfig": "47"}, {"size": 12716, "mtime": 1751002934527, "results": "80", "hashOfConfig": "47"}, {"size": 5339, "mtime": 1751099463810, "results": "81", "hashOfConfig": "47"}, {"size": 9887, "mtime": 1751109485670, "results": "82", "hashOfConfig": "47"}, {"size": 5162, "mtime": 1751003518513, "results": "83", "hashOfConfig": "47"}, {"size": 11914, "mtime": 1751004034012, "results": "84", "hashOfConfig": "47"}, {"size": 26303, "mtime": 1751093657648, "results": "85", "hashOfConfig": "47"}, {"size": 13824, "mtime": 1751026107868, "results": "86", "hashOfConfig": "47"}, {"size": 3407, "mtime": 1751030363309, "results": "87", "hashOfConfig": "47"}, {"size": 2725, "mtime": 1751030347462, "results": "88", "hashOfConfig": "47"}, {"size": 729, "mtime": 1751098798516, "results": "89", "hashOfConfig": "47"}, {"size": 2615, "mtime": 1751109112309, "results": "90", "hashOfConfig": "47"}, {"size": 4603, "mtime": 1751108857912, "results": "91", "hashOfConfig": "47"}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "do8dzb", {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "107", "messages": "108", "suppressedMessages": "109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "113", "messages": "114", "suppressedMessages": "115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "122", "messages": "123", "suppressedMessages": "124", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "125", "messages": "126", "suppressedMessages": "127", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "128", "messages": "129", "suppressedMessages": "130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "131", "messages": "132", "suppressedMessages": "133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "134", "messages": "135", "suppressedMessages": "136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "137", "messages": "138", "suppressedMessages": "139", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "140", "messages": "141", "suppressedMessages": "142", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "143", "messages": "144", "suppressedMessages": "145", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "146", "messages": "147", "suppressedMessages": "148", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "149", "messages": "150", "suppressedMessages": "151", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "152", "messages": "153", "suppressedMessages": "154", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "155", "messages": "156", "suppressedMessages": "157", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "158", "messages": "159", "suppressedMessages": "160", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "161", "messages": "162", "suppressedMessages": "163", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "164", "messages": "165", "suppressedMessages": "166", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "167", "messages": "168", "suppressedMessages": "169", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "170", "messages": "171", "suppressedMessages": "172", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "173", "messages": "174", "suppressedMessages": "175", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "176", "messages": "177", "suppressedMessages": "178", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "179", "messages": "180", "suppressedMessages": "181", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "182", "messages": "183", "suppressedMessages": "184", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "185", "messages": "186", "suppressedMessages": "187", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "188", "messages": "189", "suppressedMessages": "190", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "191", "messages": "192", "suppressedMessages": "193", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "194", "messages": "195", "suppressedMessages": "196", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "197", "messages": "198", "suppressedMessages": "199", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "200", "messages": "201", "suppressedMessages": "202", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "203", "messages": "204", "suppressedMessages": "205", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "206", "messages": "207", "suppressedMessages": "208", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "209", "messages": "210", "suppressedMessages": "211", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "212", "messages": "213", "suppressedMessages": "214", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "215", "messages": "216", "suppressedMessages": "217", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "218", "messages": "219", "suppressedMessages": "220", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "221", "messages": "222", "suppressedMessages": "223", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "224", "messages": "225", "suppressedMessages": "226", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\index.tsx", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\reportWebVitals.js", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\App.tsx", ["227"], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\contexts\\AuthContext.tsx", ["228"], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\dashboard\\Dashboard.tsx", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\auth\\Login.tsx", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\inventory\\Inventory.tsx", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\pos\\POS.tsx", ["229", "230", "231", "232", "233", "234", "235", "236"], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\services\\Services.tsx", ["237", "238", "239", "240"], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\reports\\Reports.tsx", ["241", "242", "243"], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\settings\\Settings.tsx", ["244"], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\layout\\Layout.tsx", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\common\\LoadingSpinner.tsx", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\offline\\OfflineManager.tsx", ["245", "246", "247"], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\config\\firebase.ts", ["248"], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\hooks\\useDashboard.ts", ["249", "250"], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\hooks\\useProducts.ts", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\hooks\\useServices.ts", ["251", "252"], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\hooks\\useReports.ts", ["253", "254", "255", "256", "257", "258"], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\hooks\\useCart.ts", ["259", "260"], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\utils\\seedData.ts", ["261", "262", "263"], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\utils\\testDataSeeder.ts", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\inventory\\InventoryStats.tsx", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\inventory\\ProductModal.tsx", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\inventory\\StockAdjustmentModal.tsx", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\inventory\\LowStockAlert.tsx", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\pos\\POSCart.tsx", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\pos\\ServiceSelector.tsx", ["264"], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\pos\\ProductSelector.tsx", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\services\\ServiceCategories.tsx", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\services\\ServiceStats.tsx", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\services\\ServiceModal.tsx", ["265"], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\reports\\InventoryAnalytics.tsx", ["266", "267"], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\users\\UserManagement.tsx", ["268", "269"], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\hooks\\useTransactions.ts", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\utils\\receiptGenerator.ts", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\utils\\serviceUtils.ts", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\pos\\CheckoutModal.tsx", ["270"], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\inventory\\BulkOperations.tsx", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\utils\\excelImport.ts", ["271"], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\debug\\FirebaseConnectionTest.tsx", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\utils\\firebaseConnectionTest.ts", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\utils\\objectUtils.ts", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\contexts\\BusinessSettingsContext.tsx", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\services\\businessSettingsService.ts", ["272", "273", "274", "275"], [], {"ruleId": "276", "severity": 1, "message": "277", "line": 19, "column": 7, "nodeType": "278", "messageId": "279", "endLine": 19, "endColumn": 62}, {"ruleId": "276", "severity": 1, "message": "280", "line": 16, "column": 3, "nodeType": "278", "messageId": "279", "endLine": 16, "endColumn": 8}, {"ruleId": "276", "severity": 1, "message": "281", "line": 10, "column": 3, "nodeType": "278", "messageId": "279", "endLine": 10, "endColumn": 7}, {"ruleId": "276", "severity": 1, "message": "282", "line": 11, "column": 3, "nodeType": "278", "messageId": "279", "endLine": 11, "endColumn": 13}, {"ruleId": "276", "severity": 1, "message": "283", "line": 12, "column": 3, "nodeType": "278", "messageId": "279", "endLine": 12, "endColumn": 10}, {"ruleId": "276", "severity": 1, "message": "284", "line": 13, "column": 3, "nodeType": "278", "messageId": "279", "endLine": 13, "endColumn": 9}, {"ruleId": "276", "severity": 1, "message": "285", "line": 14, "column": 3, "nodeType": "278", "messageId": "279", "endLine": 14, "endColumn": 7}, {"ruleId": "276", "severity": 1, "message": "286", "line": 15, "column": 3, "nodeType": "278", "messageId": "279", "endLine": 15, "endColumn": 8}, {"ruleId": "276", "severity": 1, "message": "287", "line": 21, "column": 10, "nodeType": "278", "messageId": "279", "endLine": 21, "endColumn": 17}, {"ruleId": "276", "severity": 1, "message": "288", "line": 21, "column": 19, "nodeType": "278", "messageId": "279", "endLine": 21, "endColumn": 26}, {"ruleId": "276", "severity": 1, "message": "289", "line": 9, "column": 3, "nodeType": "278", "messageId": "279", "endLine": 9, "endColumn": 13}, {"ruleId": "276", "severity": 1, "message": "290", "line": 10, "column": 3, "nodeType": "278", "messageId": "279", "endLine": 10, "endColumn": 6}, {"ruleId": "276", "severity": 1, "message": "291", "line": 14, "column": 3, "nodeType": "278", "messageId": "279", "endLine": 14, "endColumn": 4}, {"ruleId": "276", "severity": 1, "message": "292", "line": 15, "column": 3, "nodeType": "278", "messageId": "279", "endLine": 15, "endColumn": 7}, {"ruleId": "276", "severity": 1, "message": "293", "line": 11, "column": 3, "nodeType": "278", "messageId": "279", "endLine": 11, "endColumn": 9}, {"ruleId": "276", "severity": 1, "message": "294", "line": 26, "column": 10, "nodeType": "278", "messageId": "279", "endLine": 26, "endColumn": 13}, {"ruleId": "295", "severity": 1, "message": "296", "line": 57, "column": 6, "nodeType": "297", "endLine": 57, "endColumn": 22, "suggestions": "298"}, {"ruleId": "276", "severity": 1, "message": "299", "line": 25, "column": 10, "nodeType": "278", "messageId": "279", "endLine": 25, "endColumn": 31}, {"ruleId": "276", "severity": 1, "message": "300", "line": 6, "column": 3, "nodeType": "278", "messageId": "279", "endLine": 6, "endColumn": 14}, {"ruleId": "276", "severity": 1, "message": "301", "line": 7, "column": 3, "nodeType": "278", "messageId": "279", "endLine": 7, "endColumn": 14}, {"ruleId": "295", "severity": 1, "message": "302", "line": 51, "column": 6, "nodeType": "297", "endLine": 51, "endColumn": 27, "suggestions": "303"}, {"ruleId": "276", "severity": 1, "message": "304", "line": 8, "column": 3, "nodeType": "278", "messageId": "279", "endLine": 8, "endColumn": 23}, {"ruleId": "276", "severity": 1, "message": "288", "line": 12, "column": 23, "nodeType": "278", "messageId": "279", "endLine": 12, "endColumn": 30}, {"ruleId": "295", "severity": 1, "message": "305", "line": 300, "column": 6, "nodeType": "297", "endLine": 300, "endColumn": 8, "suggestions": "306"}, {"ruleId": "276", "severity": 1, "message": "307", "line": 5, "column": 3, "nodeType": "278", "messageId": "279", "endLine": 5, "endColumn": 10}, {"ruleId": "276", "severity": 1, "message": "280", "line": 11, "column": 3, "nodeType": "278", "messageId": "279", "endLine": 11, "endColumn": 8}, {"ruleId": "276", "severity": 1, "message": "308", "line": 1, "column": 20, "nodeType": "278", "messageId": "279", "endLine": 1, "endColumn": 29}, {"ruleId": "276", "severity": 1, "message": "309", "line": 9, "column": 3, "nodeType": "278", "messageId": "279", "endLine": 9, "endColumn": 13}, {"ruleId": "276", "severity": 1, "message": "310", "line": 10, "column": 3, "nodeType": "278", "messageId": "279", "endLine": 10, "endColumn": 8}, {"ruleId": "276", "severity": 1, "message": "288", "line": 13, "column": 23, "nodeType": "278", "messageId": "279", "endLine": 13, "endColumn": 30}, {"ruleId": "276", "severity": 1, "message": "287", "line": 13, "column": 32, "nodeType": "278", "messageId": "279", "endLine": 13, "endColumn": 39}, {"ruleId": "276", "severity": 1, "message": "281", "line": 13, "column": 41, "nodeType": "278", "messageId": "279", "endLine": 13, "endColumn": 45}, {"ruleId": "276", "severity": 1, "message": "311", "line": 3, "column": 10, "nodeType": "278", "messageId": "279", "endLine": 3, "endColumn": 31}, {"ruleId": "295", "severity": 1, "message": "312", "line": 87, "column": 6, "nodeType": "297", "endLine": 87, "endColumn": 8, "suggestions": "313"}, {"ruleId": "276", "severity": 1, "message": "281", "line": 14, "column": 10, "nodeType": "278", "messageId": "279", "endLine": 14, "endColumn": 14}, {"ruleId": "276", "severity": 1, "message": "287", "line": 14, "column": 16, "nodeType": "278", "messageId": "279", "endLine": 14, "endColumn": 23}, {"ruleId": "276", "severity": 1, "message": "288", "line": 14, "column": 25, "nodeType": "278", "messageId": "279", "endLine": 14, "endColumn": 32}, {"ruleId": "276", "severity": 1, "message": "289", "line": 5, "column": 3, "nodeType": "278", "messageId": "279", "endLine": 5, "endColumn": 13}, {"ruleId": "276", "severity": 1, "message": "290", "line": 2, "column": 25, "nodeType": "278", "messageId": "279", "endLine": 2, "endColumn": 28}, {"ruleId": "276", "severity": 1, "message": "288", "line": 12, "column": 10, "nodeType": "278", "messageId": "279", "endLine": 12, "endColumn": 17}, {"ruleId": "295", "severity": 1, "message": "314", "line": 45, "column": 6, "nodeType": "297", "endLine": 45, "endColumn": 16, "suggestions": "315"}, {"ruleId": "276", "severity": 1, "message": "284", "line": 8, "column": 3, "nodeType": "278", "messageId": "279", "endLine": 8, "endColumn": 9}, {"ruleId": "295", "severity": 1, "message": "316", "line": 34, "column": 6, "nodeType": "297", "endLine": 34, "endColumn": 8, "suggestions": "317"}, {"ruleId": "276", "severity": 1, "message": "318", "line": 9, "column": 3, "nodeType": "278", "messageId": "279", "endLine": 9, "endColumn": 10}, {"ruleId": "276", "severity": 1, "message": "319", "line": 21, "column": 7, "nodeType": "278", "messageId": "279", "endLine": 21, "endColumn": 23}, {"ruleId": "276", "severity": 1, "message": "320", "line": 5, "column": 3, "nodeType": "278", "messageId": "279", "endLine": 5, "endColumn": 13}, {"ruleId": "276", "severity": 1, "message": "321", "line": 6, "column": 3, "nodeType": "278", "messageId": "279", "endLine": 6, "endColumn": 8}, {"ruleId": "276", "severity": 1, "message": "307", "line": 7, "column": 3, "nodeType": "278", "messageId": "279", "endLine": 7, "endColumn": 10}, {"ruleId": "276", "severity": 1, "message": "310", "line": 8, "column": 3, "nodeType": "278", "messageId": "279", "endLine": 8, "endColumn": 8}, "@typescript-eslint/no-unused-vars", "'ProtectedRoute' is assigned a value but never used.", "Identifier", "unusedVar", "'where' is defined but never used.", "'User' is defined but never used.", "'CreditCard' is defined but never used.", "'Receipt' is defined but never used.", "'Trash2' is defined but never used.", "'Plus' is defined but never used.", "'Minus' is defined but never used.", "'Service' is defined but never used.", "'Product' is defined but never used.", "'DollarSign' is defined but never used.", "'Tag' is defined but never used.", "'X' is defined but never used.", "'Save' is defined but never used.", "'Filter' is defined but never used.", "'Bar' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'loadReport'. Either include it or remove the dependency array.", "ArrayExpression", ["322"], "'localBusinessSettings' is assigned a value but never used.", "'AlertCircle' is defined but never used.", "'CheckCircle' is defined but never used.", "React Hook useEffect has a missing dependency: 'syncOfflineData'. Either include it or remove the dependency array.", ["323"], "'persistentLocalCache' is defined but never used.", "React Hook useEffect has a missing dependency: 'loadDashboardData'. Either include it or remove the dependency array.", ["324"], "'getDocs' is defined but never used.", "'useEffect' is defined but never used.", "'startAfter' is defined but never used.", "'limit' is defined but never used.", "'calculateServicePrice' is defined but never used.", "React Hook useCallback has a missing dependency: 'removeFromCart'. Either include it or remove the dependency array.", ["325"], "React Hook useEffect has missing dependencies: 'calculateMetrics' and 'generateAlerts'. Either include them or remove the dependency array.", ["326"], "React Hook useEffect has a missing dependency: 'loadUsers'. Either include it or remove the dependency array.", ["327"], "'Printer' is defined but never used.", "'OPTIONAL_COLUMNS' is assigned a value but never used.", "'collection' is defined but never used.", "'query' is defined but never used.", {"desc": "328", "fix": "329"}, {"desc": "330", "fix": "331"}, {"desc": "332", "fix": "333"}, {"desc": "334", "fix": "335"}, {"desc": "336", "fix": "337"}, {"desc": "338", "fix": "339"}, "Update the dependencies array to be: [loadReport, selectedPeriod]", {"range": "340", "text": "341"}, "Update the dependencies array to be: [offlineQueue.length, syncOfflineData]", {"range": "342", "text": "343"}, "Update the dependencies array to be: [loadDashboardData]", {"range": "344", "text": "345"}, "Update the dependencies array to be: [removeFromCart]", {"range": "346", "text": "347"}, "Update the dependencies array to be: [calculateMetrics, generateAlerts, products]", {"range": "348", "text": "349"}, "Update the dependencies array to be: [loadUsers]", {"range": "350", "text": "351"}, [1381, 1397], "[load<PERSON><PERSON><PERSON>, selected<PERSON><PERSON><PERSON>]", [1357, 1378], "[offlineQueue.length, syncOfflineData]", [9246, 9248], "[loadDashboardData]", [2571, 2573], "[remove<PERSON><PERSON><PERSON><PERSON>]", [1163, 1173], "[calculateMetrics, generateAlerts, products]", [865, 867], "[loadUsers]"]