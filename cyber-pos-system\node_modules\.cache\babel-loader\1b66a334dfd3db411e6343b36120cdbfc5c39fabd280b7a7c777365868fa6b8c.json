{"ast": null, "code": "import _objectWithoutProperties from\"E:/FX/Cyber POS/cyber-pos-system/node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js\";import _objectSpread from\"E:/FX/Cyber POS/cyber-pos-system/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";const _excluded=[\"id\",\"createdAt\"];import{useState,useEffect}from'react';import{collection,doc,getDoc,addDoc,updateDoc,deleteDoc,query,orderBy,onSnapshot,serverTimestamp}from'firebase/firestore';import{db}from'../config/firebase';export const useServices=()=>{const[services,setServices]=useState([]);const[loading,setLoading]=useState(true);const[error,setError]=useState(null);// Real-time listener for services\nuseEffect(()=>{let unsubscribe=null;const setupListener=async()=>{try{const servicesQuery=query(collection(db,'services'),orderBy('category'),orderBy('name'));unsubscribe=onSnapshot(servicesQuery,snapshot=>{const servicesData=[];snapshot.forEach(doc=>{var _data$createdAt,_data$updatedAt;const data=doc.data();servicesData.push({id:doc.id,name:data.name||'',description:data.description||'',basePrice:data.basePrice||0,category:data.category||'',isActive:data.isActive!==false,allowPriceOverride:data.allowPriceOverride!==false,bundledServices:data.bundledServices||[],createdAt:((_data$createdAt=data.createdAt)===null||_data$createdAt===void 0?void 0:_data$createdAt.toDate())||new Date(),updatedAt:((_data$updatedAt=data.updatedAt)===null||_data$updatedAt===void 0?void 0:_data$updatedAt.toDate())||new Date()});});setServices(servicesData);setLoading(false);setError(null);console.log(\"\\u2705 Successfully loaded \".concat(servicesData.length,\" services\"));},error=>{console.error('Error fetching services:',error);// Check if it's an index error\nif(error.code==='failed-precondition'&&error.message.includes('index')){setError('Database index is being created. Please wait a few minutes and refresh the page.');}else{setError(\"Failed to fetch services: \".concat(error.message));}setLoading(false);});}catch(error){console.error('Error setting up services listener:',error);setError('Failed to initialize services');setLoading(false);}};setupListener();return()=>{if(unsubscribe){unsubscribe();}};},[]);const createService=async serviceData=>{try{setError(null);await addDoc(collection(db,'services'),_objectSpread(_objectSpread({},serviceData),{},{createdAt:serverTimestamp(),updatedAt:serverTimestamp()}));}catch(error){console.error('Error creating service:',error);setError('Failed to create service');throw error;}};const updateService=async(serviceId,updates)=>{try{setError(null);const{id,createdAt}=updates,updateData=_objectWithoutProperties(updates,_excluded);await updateDoc(doc(db,'services',serviceId),_objectSpread(_objectSpread({},updateData),{},{updatedAt:serverTimestamp()}));}catch(error){console.error('Error updating service:',error);setError('Failed to update service');throw error;}};const deleteService=async serviceId=>{try{setError(null);await deleteDoc(doc(db,'services',serviceId));}catch(error){console.error('Error deleting service:',error);setError('Failed to delete service');throw error;}};const getServiceById=async serviceId=>{try{const serviceDoc=await getDoc(doc(db,'services',serviceId));if(serviceDoc.exists()){var _data$createdAt2,_data$updatedAt2;const data=serviceDoc.data();return{id:serviceDoc.id,name:data.name||'',description:data.description||'',basePrice:data.basePrice||0,category:data.category||'',isActive:data.isActive!==false,allowPriceOverride:data.allowPriceOverride!==false,bundledServices:data.bundledServices||[],createdAt:((_data$createdAt2=data.createdAt)===null||_data$createdAt2===void 0?void 0:_data$createdAt2.toDate())||new Date(),updatedAt:((_data$updatedAt2=data.updatedAt)===null||_data$updatedAt2===void 0?void 0:_data$updatedAt2.toDate())||new Date()};}return null;}catch(error){console.error('Error fetching service:',error);throw error;}};const getServicesByCategory=category=>{return services.filter(service=>service.category===category&&service.isActive);};const getActiveServices=()=>{return services.filter(service=>service.isActive);};const getServiceCategories=()=>{const categories=[...new Set(services.map(service=>service.category))];return categories.sort();};return{services,loading,error,createService,updateService,deleteService,getServiceById,getServicesByCategory,getActiveServices,getServiceCategories};};", "map": {"version": 3, "names": ["useState", "useEffect", "collection", "doc", "getDoc", "addDoc", "updateDoc", "deleteDoc", "query", "orderBy", "onSnapshot", "serverTimestamp", "db", "useServices", "services", "setServices", "loading", "setLoading", "error", "setError", "unsubscribe", "setupListener", "servicesQuery", "snapshot", "servicesData", "for<PERSON>ach", "_data$createdAt", "_data$updatedAt", "data", "push", "id", "name", "description", "basePrice", "category", "isActive", "allowPriceOverride", "bundledServices", "createdAt", "toDate", "Date", "updatedAt", "console", "log", "concat", "length", "code", "message", "includes", "createService", "serviceData", "_objectSpread", "updateService", "serviceId", "updates", "updateData", "_objectWithoutProperties", "_excluded", "deleteService", "getServiceById", "serviceDoc", "exists", "_data$createdAt2", "_data$updatedAt2", "getServicesByCategory", "filter", "service", "getActiveServices", "getServiceCategories", "categories", "Set", "map", "sort"], "sources": ["E:/FX/Cyber POS/cyber-pos-system/src/hooks/useServices.ts"], "sourcesContent": ["import { useState, useEffect } from 'react';\nimport {\n  collection,\n  doc,\n  getDocs,\n  getDoc,\n  addDoc,\n  updateDoc,\n  deleteDoc,\n  query,\n  where,\n  orderBy,\n  onSnapshot,\n  serverTimestamp\n} from 'firebase/firestore';\nimport { db } from '../config/firebase';\nimport { Service } from '../types';\n\nexport const useServices = () => {\n  const [services, setServices] = useState<Service[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n\n  // Real-time listener for services\n  useEffect(() => {\n    let unsubscribe: (() => void) | null = null;\n\n    const setupListener = async () => {\n      try {\n        const servicesQuery = query(\n          collection(db, 'services'),\n          orderBy('category'),\n          orderBy('name')\n        );\n\n        unsubscribe = onSnapshot(\n          servicesQuery,\n          (snapshot) => {\n            const servicesData: Service[] = [];\n            snapshot.forEach((doc) => {\n              const data = doc.data();\n              servicesData.push({\n                id: doc.id,\n                name: data.name || '',\n                description: data.description || '',\n                basePrice: data.basePrice || 0,\n                category: data.category || '',\n                isActive: data.isActive !== false,\n                allowPriceOverride: data.allowPriceOverride !== false,\n                bundledServices: data.bundledServices || [],\n                createdAt: data.createdAt?.toDate() || new Date(),\n                updatedAt: data.updatedAt?.toDate() || new Date(),\n              });\n            });\n            setServices(servicesData);\n            setLoading(false);\n            setError(null);\n            console.log(`✅ Successfully loaded ${servicesData.length} services`);\n          },\n          (error) => {\n            console.error('Error fetching services:', error);\n\n            // Check if it's an index error\n            if (error.code === 'failed-precondition' && error.message.includes('index')) {\n              setError('Database index is being created. Please wait a few minutes and refresh the page.');\n            } else {\n              setError(`Failed to fetch services: ${error.message}`);\n            }\n            setLoading(false);\n          }\n        );\n      } catch (error) {\n        console.error('Error setting up services listener:', error);\n        setError('Failed to initialize services');\n        setLoading(false);\n      }\n    };\n\n    setupListener();\n\n    return () => {\n      if (unsubscribe) {\n        unsubscribe();\n      }\n    };\n  }, []);\n\n  const createService = async (serviceData: Omit<Service, 'id' | 'createdAt' | 'updatedAt'>) => {\n    try {\n      setError(null);\n      await addDoc(collection(db, 'services'), {\n        ...serviceData,\n        createdAt: serverTimestamp(),\n        updatedAt: serverTimestamp(),\n      });\n    } catch (error) {\n      console.error('Error creating service:', error);\n      setError('Failed to create service');\n      throw error;\n    }\n  };\n\n  const updateService = async (serviceId: string, updates: Partial<Service>) => {\n    try {\n      setError(null);\n      const { id, createdAt, ...updateData } = updates;\n      await updateDoc(doc(db, 'services', serviceId), {\n        ...updateData,\n        updatedAt: serverTimestamp(),\n      });\n    } catch (error) {\n      console.error('Error updating service:', error);\n      setError('Failed to update service');\n      throw error;\n    }\n  };\n\n  const deleteService = async (serviceId: string) => {\n    try {\n      setError(null);\n      await deleteDoc(doc(db, 'services', serviceId));\n    } catch (error) {\n      console.error('Error deleting service:', error);\n      setError('Failed to delete service');\n      throw error;\n    }\n  };\n\n  const getServiceById = async (serviceId: string): Promise<Service | null> => {\n    try {\n      const serviceDoc = await getDoc(doc(db, 'services', serviceId));\n      if (serviceDoc.exists()) {\n        const data = serviceDoc.data();\n        return {\n          id: serviceDoc.id,\n          name: data.name || '',\n          description: data.description || '',\n          basePrice: data.basePrice || 0,\n          category: data.category || '',\n          isActive: data.isActive !== false,\n          allowPriceOverride: data.allowPriceOverride !== false,\n          bundledServices: data.bundledServices || [],\n          createdAt: data.createdAt?.toDate() || new Date(),\n          updatedAt: data.updatedAt?.toDate() || new Date(),\n        };\n      }\n      return null;\n    } catch (error) {\n      console.error('Error fetching service:', error);\n      throw error;\n    }\n  };\n\n  const getServicesByCategory = (category: string) => {\n    return services.filter(service => service.category === category && service.isActive);\n  };\n\n  const getActiveServices = () => {\n    return services.filter(service => service.isActive);\n  };\n\n  const getServiceCategories = () => {\n    const categories = [...new Set(services.map(service => service.category))];\n    return categories.sort();\n  };\n\n  return {\n    services,\n    loading,\n    error,\n    createService,\n    updateService,\n    deleteService,\n    getServiceById,\n    getServicesByCategory,\n    getActiveServices,\n    getServiceCategories,\n  };\n};\n"], "mappings": "kSAAA,OAASA,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAC3C,OACEC,UAAU,CACVC,GAAG,CAEHC,MAAM,CACNC,MAAM,CACNC,SAAS,CACTC,SAAS,CACTC,KAAK,CAELC,OAAO,CACPC,UAAU,CACVC,eAAe,KACV,oBAAoB,CAC3B,OAASC,EAAE,KAAQ,oBAAoB,CAGvC,MAAO,MAAM,CAAAC,WAAW,CAAGA,CAAA,GAAM,CAC/B,KAAM,CAACC,QAAQ,CAAEC,WAAW,CAAC,CAAGf,QAAQ,CAAY,EAAE,CAAC,CACvD,KAAM,CAACgB,OAAO,CAAEC,UAAU,CAAC,CAAGjB,QAAQ,CAAC,IAAI,CAAC,CAC5C,KAAM,CAACkB,KAAK,CAAEC,QAAQ,CAAC,CAAGnB,QAAQ,CAAgB,IAAI,CAAC,CAEvD;AACAC,SAAS,CAAC,IAAM,CACd,GAAI,CAAAmB,WAAgC,CAAG,IAAI,CAE3C,KAAM,CAAAC,aAAa,CAAG,KAAAA,CAAA,GAAY,CAChC,GAAI,CACF,KAAM,CAAAC,aAAa,CAAGd,KAAK,CACzBN,UAAU,CAACU,EAAE,CAAE,UAAU,CAAC,CAC1BH,OAAO,CAAC,UAAU,CAAC,CACnBA,OAAO,CAAC,MAAM,CAChB,CAAC,CAEDW,WAAW,CAAGV,UAAU,CACtBY,aAAa,CACZC,QAAQ,EAAK,CACZ,KAAM,CAAAC,YAAuB,CAAG,EAAE,CAClCD,QAAQ,CAACE,OAAO,CAAEtB,GAAG,EAAK,KAAAuB,eAAA,CAAAC,eAAA,CACxB,KAAM,CAAAC,IAAI,CAAGzB,GAAG,CAACyB,IAAI,CAAC,CAAC,CACvBJ,YAAY,CAACK,IAAI,CAAC,CAChBC,EAAE,CAAE3B,GAAG,CAAC2B,EAAE,CACVC,IAAI,CAAEH,IAAI,CAACG,IAAI,EAAI,EAAE,CACrBC,WAAW,CAAEJ,IAAI,CAACI,WAAW,EAAI,EAAE,CACnCC,SAAS,CAAEL,IAAI,CAACK,SAAS,EAAI,CAAC,CAC9BC,QAAQ,CAAEN,IAAI,CAACM,QAAQ,EAAI,EAAE,CAC7BC,QAAQ,CAAEP,IAAI,CAACO,QAAQ,GAAK,KAAK,CACjCC,kBAAkB,CAAER,IAAI,CAACQ,kBAAkB,GAAK,KAAK,CACrDC,eAAe,CAAET,IAAI,CAACS,eAAe,EAAI,EAAE,CAC3CC,SAAS,CAAE,EAAAZ,eAAA,CAAAE,IAAI,CAACU,SAAS,UAAAZ,eAAA,iBAAdA,eAAA,CAAgBa,MAAM,CAAC,CAAC,GAAI,GAAI,CAAAC,IAAI,CAAC,CAAC,CACjDC,SAAS,CAAE,EAAAd,eAAA,CAAAC,IAAI,CAACa,SAAS,UAAAd,eAAA,iBAAdA,eAAA,CAAgBY,MAAM,CAAC,CAAC,GAAI,GAAI,CAAAC,IAAI,CAAC,CAClD,CAAC,CAAC,CACJ,CAAC,CAAC,CACFzB,WAAW,CAACS,YAAY,CAAC,CACzBP,UAAU,CAAC,KAAK,CAAC,CACjBE,QAAQ,CAAC,IAAI,CAAC,CACduB,OAAO,CAACC,GAAG,+BAAAC,MAAA,CAA0BpB,YAAY,CAACqB,MAAM,aAAW,CAAC,CACtE,CAAC,CACA3B,KAAK,EAAK,CACTwB,OAAO,CAACxB,KAAK,CAAC,0BAA0B,CAAEA,KAAK,CAAC,CAEhD;AACA,GAAIA,KAAK,CAAC4B,IAAI,GAAK,qBAAqB,EAAI5B,KAAK,CAAC6B,OAAO,CAACC,QAAQ,CAAC,OAAO,CAAC,CAAE,CAC3E7B,QAAQ,CAAC,kFAAkF,CAAC,CAC9F,CAAC,IAAM,CACLA,QAAQ,8BAAAyB,MAAA,CAA8B1B,KAAK,CAAC6B,OAAO,CAAE,CAAC,CACxD,CACA9B,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CACH,CAAE,MAAOC,KAAK,CAAE,CACdwB,OAAO,CAACxB,KAAK,CAAC,qCAAqC,CAAEA,KAAK,CAAC,CAC3DC,QAAQ,CAAC,+BAA+B,CAAC,CACzCF,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAEDI,aAAa,CAAC,CAAC,CAEf,MAAO,IAAM,CACX,GAAID,WAAW,CAAE,CACfA,WAAW,CAAC,CAAC,CACf,CACF,CAAC,CACH,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAA6B,aAAa,CAAG,KAAO,CAAAC,WAA4D,EAAK,CAC5F,GAAI,CACF/B,QAAQ,CAAC,IAAI,CAAC,CACd,KAAM,CAAAd,MAAM,CAACH,UAAU,CAACU,EAAE,CAAE,UAAU,CAAC,CAAAuC,aAAA,CAAAA,aAAA,IAClCD,WAAW,MACdZ,SAAS,CAAE3B,eAAe,CAAC,CAAC,CAC5B8B,SAAS,CAAE9B,eAAe,CAAC,CAAC,EAC7B,CAAC,CACJ,CAAE,MAAOO,KAAK,CAAE,CACdwB,OAAO,CAACxB,KAAK,CAAC,yBAAyB,CAAEA,KAAK,CAAC,CAC/CC,QAAQ,CAAC,0BAA0B,CAAC,CACpC,KAAM,CAAAD,KAAK,CACb,CACF,CAAC,CAED,KAAM,CAAAkC,aAAa,CAAG,KAAAA,CAAOC,SAAiB,CAAEC,OAAyB,GAAK,CAC5E,GAAI,CACFnC,QAAQ,CAAC,IAAI,CAAC,CACd,KAAM,CAAEW,EAAE,CAAEQ,SAAyB,CAAC,CAAGgB,OAAO,CAAtBC,UAAU,CAAAC,wBAAA,CAAKF,OAAO,CAAAG,SAAA,EAChD,KAAM,CAAAnD,SAAS,CAACH,GAAG,CAACS,EAAE,CAAE,UAAU,CAAEyC,SAAS,CAAC,CAAAF,aAAA,CAAAA,aAAA,IACzCI,UAAU,MACbd,SAAS,CAAE9B,eAAe,CAAC,CAAC,EAC7B,CAAC,CACJ,CAAE,MAAOO,KAAK,CAAE,CACdwB,OAAO,CAACxB,KAAK,CAAC,yBAAyB,CAAEA,KAAK,CAAC,CAC/CC,QAAQ,CAAC,0BAA0B,CAAC,CACpC,KAAM,CAAAD,KAAK,CACb,CACF,CAAC,CAED,KAAM,CAAAwC,aAAa,CAAG,KAAO,CAAAL,SAAiB,EAAK,CACjD,GAAI,CACFlC,QAAQ,CAAC,IAAI,CAAC,CACd,KAAM,CAAAZ,SAAS,CAACJ,GAAG,CAACS,EAAE,CAAE,UAAU,CAAEyC,SAAS,CAAC,CAAC,CACjD,CAAE,MAAOnC,KAAK,CAAE,CACdwB,OAAO,CAACxB,KAAK,CAAC,yBAAyB,CAAEA,KAAK,CAAC,CAC/CC,QAAQ,CAAC,0BAA0B,CAAC,CACpC,KAAM,CAAAD,KAAK,CACb,CACF,CAAC,CAED,KAAM,CAAAyC,cAAc,CAAG,KAAO,CAAAN,SAAiB,EAA8B,CAC3E,GAAI,CACF,KAAM,CAAAO,UAAU,CAAG,KAAM,CAAAxD,MAAM,CAACD,GAAG,CAACS,EAAE,CAAE,UAAU,CAAEyC,SAAS,CAAC,CAAC,CAC/D,GAAIO,UAAU,CAACC,MAAM,CAAC,CAAC,CAAE,KAAAC,gBAAA,CAAAC,gBAAA,CACvB,KAAM,CAAAnC,IAAI,CAAGgC,UAAU,CAAChC,IAAI,CAAC,CAAC,CAC9B,MAAO,CACLE,EAAE,CAAE8B,UAAU,CAAC9B,EAAE,CACjBC,IAAI,CAAEH,IAAI,CAACG,IAAI,EAAI,EAAE,CACrBC,WAAW,CAAEJ,IAAI,CAACI,WAAW,EAAI,EAAE,CACnCC,SAAS,CAAEL,IAAI,CAACK,SAAS,EAAI,CAAC,CAC9BC,QAAQ,CAAEN,IAAI,CAACM,QAAQ,EAAI,EAAE,CAC7BC,QAAQ,CAAEP,IAAI,CAACO,QAAQ,GAAK,KAAK,CACjCC,kBAAkB,CAAER,IAAI,CAACQ,kBAAkB,GAAK,KAAK,CACrDC,eAAe,CAAET,IAAI,CAACS,eAAe,EAAI,EAAE,CAC3CC,SAAS,CAAE,EAAAwB,gBAAA,CAAAlC,IAAI,CAACU,SAAS,UAAAwB,gBAAA,iBAAdA,gBAAA,CAAgBvB,MAAM,CAAC,CAAC,GAAI,GAAI,CAAAC,IAAI,CAAC,CAAC,CACjDC,SAAS,CAAE,EAAAsB,gBAAA,CAAAnC,IAAI,CAACa,SAAS,UAAAsB,gBAAA,iBAAdA,gBAAA,CAAgBxB,MAAM,CAAC,CAAC,GAAI,GAAI,CAAAC,IAAI,CAAC,CAClD,CAAC,CACH,CACA,MAAO,KAAI,CACb,CAAE,MAAOtB,KAAK,CAAE,CACdwB,OAAO,CAACxB,KAAK,CAAC,yBAAyB,CAAEA,KAAK,CAAC,CAC/C,KAAM,CAAAA,KAAK,CACb,CACF,CAAC,CAED,KAAM,CAAA8C,qBAAqB,CAAI9B,QAAgB,EAAK,CAClD,MAAO,CAAApB,QAAQ,CAACmD,MAAM,CAACC,OAAO,EAAIA,OAAO,CAAChC,QAAQ,GAAKA,QAAQ,EAAIgC,OAAO,CAAC/B,QAAQ,CAAC,CACtF,CAAC,CAED,KAAM,CAAAgC,iBAAiB,CAAGA,CAAA,GAAM,CAC9B,MAAO,CAAArD,QAAQ,CAACmD,MAAM,CAACC,OAAO,EAAIA,OAAO,CAAC/B,QAAQ,CAAC,CACrD,CAAC,CAED,KAAM,CAAAiC,oBAAoB,CAAGA,CAAA,GAAM,CACjC,KAAM,CAAAC,UAAU,CAAG,CAAC,GAAG,GAAI,CAAAC,GAAG,CAACxD,QAAQ,CAACyD,GAAG,CAACL,OAAO,EAAIA,OAAO,CAAChC,QAAQ,CAAC,CAAC,CAAC,CAC1E,MAAO,CAAAmC,UAAU,CAACG,IAAI,CAAC,CAAC,CAC1B,CAAC,CAED,MAAO,CACL1D,QAAQ,CACRE,OAAO,CACPE,KAAK,CACL+B,aAAa,CACbG,aAAa,CACbM,aAAa,CACbC,cAAc,CACdK,qBAAqB,CACrBG,iBAAiB,CACjBC,oBACF,CAAC,CACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}